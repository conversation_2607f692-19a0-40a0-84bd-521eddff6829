# 👤 AI生态平台平台角色功能-需求文档 V2.0


## 📋 目录
1. [一、功能概述](#一、功能概述)
2. [二、平台超级管理员核心功能](#二、平台超级管理员核心功能)
3. [三、SAAS租户代理商核心功能](#三、SAAS租户代理商核心功能)
4. [四、推广员核心功能](#四、推广员核心功能)
5. [五、普通用户核心功能](#五、普通用户核心功能)


## 一、功能概述

### 1.1 功能定位
平台角色功能是AI生态平台的核心基础设施，由两个微服务容器完成，分别是ai-admin和ai-users。平台超级管理员（由ai-admin负责）、SAAS租户代理商、推广员、平台用户（由ai-users负责），通过严格的多租户数据隔离和灵活的权限管理，支撑整个平台运营，实现完整的商业生态闭环。

### 1.2 核心设计原则
- **租户ID+用户ID参数传递**: 通过URL参数传递租户ID和用户ID
- **自定义域名支持**: SAAS代理商可配置多个自定义域名
- **推广归属追踪**: 完整的推广关系链条和归属管理
- **用户ID统一机制**: 推广员和普通用户共享用户ID，推广员只是身份标识
- **身份权限分离**: 用户可同时拥有普通用户和推广员两种身份

### 1.3 功能边界
- ✅ 平台超级管理员功能，拥有全平台最高权益（ai-admin负责）
- ✅ 三个角色（SAAS租户代理商、推广员、普通用户）用户管理（ai-users负责）
- ✅ 全平台推广链接机制，每次转发链接都自带推广锁定关系，防止重复推广
- ✅ 用户认证、注册、登录、密码找回、资料管理
- ✅ 推广分销体系和佣金管理
- ❌ 具体AI业务功能（由ai-agents、ai-llms、ai-tools负责）
- ❌ 全场景智能营销、商城系统、课程系统（其他文档记录）


## 二、平台超级管理员核心功能

### 2.1 功能概述
AI生态平台超级管理员是整个平台的最高管理者，通过独立的ai-admin微服务实现，专注于**纯平台级管理功能**，与租户层管理形成清晰的职责分离。超级管理员通过固定域名 `admin.cees.cc` 访问，拥有跨租户的数据统计、管理和决策支持能力。

**核心定位**
- **角色定位**: 平台最高管理者，负责整个平台的运营和管理
- **访问方式**: `admin.cees.cc`（固定域名访问，前后端集成的独立微服务）
- **管理范围**: 全平台所有权限、数据和功能
- **权限特点**: 不参与推广链接机制，拥有全平台最高权限

### 2.2 全平台数据大屏功能

- **快捷操作**: 常用管理操作的快捷入口（可自定义快捷操作）
- **总体指标**: 统计全平台的用户数、租户数、订单数、总营业额、API调用总数、智能体总数、大模型对接总数据、AI插件总数量、当前在线用户总人数、当前在线推广员人数共10大总体核心指标（分两行展示）
- **趋势图表**: 展示用户数、订单数、总营业额的趋势变化，支持按天、按月、按年的维度进行趋势分析（分一行展示）
- **系统监控**: 系统状态、容器健康、性能指标、实时日志（分一行展示）
- **租户数据聚合**: 实时汇总当前筛选租户的业务数据，支持按小时、天、月、年的维度进行数据聚合，包括总用户数、总订单数、总营业额、新增用户、流量统计、

### 2.3 全平台租户管理功能

#### 2.3.1 租户ID功能描述

租户ID是AI生态平台多租户架构的核心标识符，用于区分和隔离不同的SAAS代理商租户。每个租户ID代表一个独立的业务实体，拥有完全隔离的数据空间和独立的运营权限。

注意点：所有的租户创建、管理、维护都是由ai-admin微服务负责，而租户内的推广功能、商城、课堂、支付、第三方配置等，和租户旗下的用户的登录、注册、密码找回、资料管理、推广员等都是由ai-users微服务负责。

**设计原则**
- **唯一性**: 全平台唯一，永不重复
- **简洁性**: 6位字符，易于记忆和使用
- **可读性**: 大写英文字母和数字组合，避免歧义字符
- **扩展性**: 支持大规模租户扩展（约2.1亿个组合）
- **稳定性**: 一旦分配，终身不变

**业务价值**
- **数据隔离**: 确保SAAS租户间数据完全隔离
- **独立运营**: 支持代理商独立运营业务
- **品牌定制**: 支持自定义域名和品牌化
- **权限控制**: 基于租户的精细化权限管理
- **商业模式**: 支持"卖坑位"的SAAS商业模式

#### 2.3.2 租户ID功能特性

**租户识别**
- **域名解析**: 通过域名自动识别租户ID
- **参数解析**: 从URL参数中提取租户ID
- **API识别**: API请求中的租户身份验证
- **数据过滤**: 基于租户ID的数据自动过滤

**数据隔离**
- **数据库隔离**: 所有业务表包含tenant_id字段
- **文件隔离**: 文件存储按租户ID分目录
- **缓存隔离**: Redis缓存按租户ID分区
- **日志隔离**: 操作日志按租户ID分类

**权限控制**
- **访问控制**: 基于租户ID的访问权限验证
- **功能权限**: 不同租户的功能开关控制
- **API限流**: 按租户ID的API调用频率限制

**域名管理**
- **自定义域名**: SAAS租户支持绑定多个自定义域名

**配置管理**
- **主题定制**: 自定义UI主题和Logo
- **第三方集成**: 租户独立的第三方服务配置，这些参数的配置都属于租户层的数据，每个租户口都可以自己配置，而且每个需要使用租户ID来隔离存储到主数据库，注意这里的配置与超级管理员的配置不是一个层级的内容，第三方服务配置包括：
1. 多渠道多空间的智能体开发平台coze、dify、n8n
2. 微信支付、支付宝支付、new-api里面集成的易支付
3. 腾讯云SMS短信、阿里云SMS短信
4. 腾讯COS云存储、阿里OSS云存储、百度云盘存储的存储对接参数配置
5. 邮箱、微信公众号、手机短信的通知模板
6. 全球大模型API对接参数配置，用于对接到llms的大模型API
7. 微信公众号、小程序对接参数配置


#### 2.3.3 租户ID生成规则

**标准格式**: 6位大写英文字母和数字组合

**字符集**: A-Z, 0-9 (排除易混淆字符)
- 排除字符: 0(零)与O(欧), 1(一)与I(艾)与l(小写L)
- 实际字符集: A-Z(除I,O) + 2-9 = 24字母 + 8数字 = 32个字符

**唯一性保证**
- **数据库约束**: 租户ID字段设置唯一索引
- **生成检查**: 生成时实时检查数据库唯一性
- **重试机制**: 冲突时自动重新生成

**生成示例**:
```
D6SS21  # 代理商租户ID
A8BC45  # 代理商租户ID  
F2GH89  # 代理商租户ID
K3MN67  # 代理商租户ID
```

#### 2.3.4 租户的详细功能

##### 2.3.4.1 【数据概览】页面功能
- **数据概览**: 总租户数、总用户数、总推广员数、总营业额、总订单数、总支付金额、总退款金额、当前在线租户、当前浏览用户总人数

##### 2.3.4.2 【租户域名】页面功能
- **添加域名**: 页面右上角添加按钮，点击进入添加域名页面（填写信息：域名、绑定租户、SSL证书配置）（弹窗页面）
- **域名列表**: 列表展示租户域名、归属租户、租户ID、绑定状态、绑定时间、是否配置SSL证书、域名状态、操作区
- **域名操作**: 编辑按钮、删除按钮、绑定按钮、解绑按钮、证书配置
- **域名状态**: 状态一绑定状态：已绑定、未绑定。状态二证书状态：未配置、已配置、SSL已过期
- **域名筛选**：根据域名、绑定租户、绑定状态、证书状态进行筛选

##### 2.3.4.3 【租户管理】页面功能

1. **租户分类**: 创建分类按钮放在租户管理页面右上角（弹窗页面）平台超级管理员可以自己创建自定义的租户分类，
2. **租户创建**: 创建租户按钮放在租户管理页面右上角（弹窗页面），点击进入创建租户页面（弹窗页面）相当于右上角有两个按钮，分别是：分类管理 、创建租户
租户开通流程: 申请提交 → 租户创建 → 租户ID生成 → 初始化配置 → 绑定域名 → 开通成功
创建基础信息: SAAS平台名称、联系人、联系方式、域名
数据库初始化: 创建租户相关数据表记录
配置初始化: 设置默认配置参数
权限初始化: 创建租户管理员账户
3. **租户列表**: 列表展示租户名称、租户ID、域名、创建时间、状态、分类、联系人、联系方式、用户数、推广员数、总营业额、备注、操作区
4. **租户筛选**: 在租户列表可以根据租户ID、名称、状态、分类、联系人、联系方式进行筛选
5. **租户操作区**: 
详情按钮：点击查看租户详情（弹窗页面）
编辑按钮：点击编辑租户基础信息（弹窗页面）
操作按钮：点击出来下拉菜单，下拉菜单有：暂停、终止、删除、配置域名、备份数据、恢复数据、脱敏开关（一菜有7个下拉菜单，所有按钮点击后都以弹窗页面出现）

##### 2.3.4.4 【租户数据】页面功能
1. **数据概览卡片**: 统计所有租户数据：总租户数、总用户数、总推广员数、总营业额、总订单数、当前在线租户人数、当前在线用户总人数、当前在线推广员人数（分成两排卡片展示）
2. **租户数据筛选框**: 先通过筛选租户展示对应租户的数据，筛选可以通过搜索和下拉显示所有租户进行筛选
3. **租户级数据展示**: 展示当前筛选的租户数据，展示数据包括：
用户数据：总用户数、总推广员数、当前在线用户数+头像列表、当前在线推广员数+头像列表
金额数据：总营业额、总订单数、总支付金额、总退款金额、
API数据：API调用总次数、智能体调用总次数、大模型API调用总次数、AI插件调用总次数
数据库数据：数据库存储总大小、数据库文件总数量
文件数据：文件存储总大小、文件总数量
缓存数据：缓存存储总大小、缓存项总数量
日志数据：日志文件总大小、日志文件总数量


### 2.4 全平台用户管理功能

#### 2.4.1 【数据概览】页面功能
- **数据概览**: 统计全平台的总用户数、总推广员数、总订单数、总支付金额、总退款金额、
- **实时数据**: 统计全平台当前时间点的实时数据，包括当前在线总用户数、当前浏览智能体总人数、当前浏览AI大模型API总人数、当前浏览AI插件总人数、当前浏览商城总人数、当前课堂学习总人数
- **趋势分析**: 统计全平台的趋势分析，支持按天、月、年维度统计趋势分析，包括用户数增长、订单数增长、支付金额增长、退款金额增长、智能体调用总次数增长、大模型API调用总次数增长、AI插件调用总次数增长

菜单设计：数据概览不需要在左侧子菜单展示，用横向菜单展示三个数据概览，依次：数据概览、实时数据、趋势分析

#### 2.4.2 【用户设置】页面功能
- **用户分类设置**： 平台可以自定义设置用户分类，
- **用户标签设置**： 平台可以自定义设置用户标签，

用户设置页面左上角有两个按钮：添加用户分类、添加用户标签
页面列表分左右展示已创建的用户分类、用户标签（左侧展示用户分类、右侧展示用户标签）
左侧展示用户分类详细信息：展示分类名称、分类描述、分类用户数、排序
右侧展示用户标签详细信息：展示标签名称、标签描述、标签用户数、排序

#### 2.4.3 【用户管理】页面功能

##### 2.4.3.1 添加用户功能
用户管理页面左上角有一个添加用户按钮，点击进入添加用户页面（弹窗页面）
添加用户弹窗：
- 填写用户基础信息：用户ID、手机号、邮箱、租户、角色、分类、标签、资产、备注
- 填写用户密码：设置用户登录密码
- 提交用户信息：点击提交按钮，将用户信息提交到系统

注意点1：添加用户必须是先选择对应的租户，然后系统会自动生成用户ID，因为用户ID是用租户ID+用户生序号自动生成的
注意点2：添加用户时选择分类和标签，是可以选择多个进行用户管理的，比如一个用户可以同时存在于多个分类中，也可以同时存在于多个标签中

##### 2.4.3.2 用户列表功能
- **列表展示信息**: 用户名称、用户ID、手机号、邮箱、所属租户名称、所属租户ID、角色、分类、标签、资产、备注、创建时间、是否已购买用户、最近登录时间、操作区（三个按钮：详情、删除、编辑）

信息列表说明：
所属租户：展示租户名称、租户ID
角色：展示用户角色（推广员/普通用户）
分类：展示用户所属分类（可以有多个）
标签：展示用户所属标签（可以有多个）
是否已购买用户：只要在平台上有支付成功的订单，就表示用户已购买

##### 2.4.3.3 用户列表操作区功能
操作区一共有三个按钮，分别是：使用记录、详情、删除、编辑（4个菜单按钮点击后都是弹窗展示）

使用记录：点击查看该用户的所有使用记录（弹窗页面以日志形式展示）

详情按钮：点击查看用户详情，展示用户详细信息（弹窗页面）

删除按钮：点击删除用户（弹窗页面，弹出确认删除警告弹窗）

编辑按钮：点击编辑用户信息（弹窗页面）（弹窗页面左侧是展示用户信息，右侧是编辑用户的所有按钮）按键包括如下：
- 编辑用户按钮：点击编辑用户信息（弹窗页面）除了用户ID不能修改，其它信息都可以修改，包括租户ID，租户ID修改后自动归属到新的租户旗下
- 重置密码按钮：点击重置用户密码（弹窗页面修改密码，并提示管理员是否重置密码）
- 资产增加按钮：点击可增加/减少用户余额（弹窗页面）
- 业务开通按钮：可以开通全场景智能营销通过三层组装发布在商城的产品开通、课程产品开通、智能体产品开通、AI插件产品开通、（点击某个业务开通按钮，弹窗页面展示对应业务的列表，勾选后提示是否保存，保存后系统自动给客户开通对应的业务）
- 开通推广按钮：点击开通用户推广，选择推广模板，保存后同步到推广系统（弹窗页面）
- 拉黑用户按钮：点击拉黑用户，提示是否保存，保存后系统自动禁止用户登录（弹窗页面）
- 解封账户按钮：点击解封账户，提示是否保存，保存后系统自动允许用户登录（弹窗页面）
- 删除用户按钮：点击删除用户（弹窗页面，弹出确认删除警告弹窗）

##### 2.4.3.4 全平台用户列表搜索查询功能
输入搜索框：在用户列表页面的搜索框中输入用户名称、用户ID、手机号、邮箱、租户名称、租户ID字段，点击搜索按钮，即可查询符合搜索条件的用户列表
下拉筛选框：下拉筛选框包括：用户角色、用户分类、用户标签、用户是否已购买用户、租户名称、租户ID、最近登录

##### 2.4.3.5 批量操作功能
批量删除：点击批量删除，提示是否删除，删除后系统自动删除用户（弹窗页面）
批量拉黑：点击批量拉黑，提示是否拉黑，拉黑后系统自动禁止用户登录（弹窗页面）
批量解封：点击批量解封，提示是否解封，解封后系统自动允许用户登录（弹窗页面）
批量导出：点击批量导出，提示是否导出，导出后系统自动导出用户信息（弹窗页面）



### 2.5 全平台订单管理功能

#### 2.5.1 【订单概览】页面功能
**数据概览**: 统计全平台订单支付总金额、订单支付总数量、订单总退款金额、订单总退款数量、订单支付总人数（以一排卡片展示）
**趋势分析**: 统计全平台的订单趋势分析，支持按天、月、年维度统计趋势分析，包括全平台订单支付总金额、订单支付总数量、订单总退款金额、订单总退款数量、订单支付总人数、订单退款总人数（以折线图展示，一行展示两个趋势折线图，等于通过三行展示完所有的订单趋势折线图）

排版说明：数据概览/趋势分析通过一个页面展示完）

#### 2.5.2 【订单汇总】列表页面功能
**列表展示功能**: 订单编号、归属租户名称、归属租户ID、订单创建时间、订单类型、订单金额、订单退款金额、订单支付时间、订单支付方式、订单状态、订单备注、订单操作区（三个按钮：详情、删除、编辑）
  - 列表展示订单类型说明：课程订单、智能体订单、AI插件订单、商城订单
  - 列表展示订单状态说明：待支付、已支付、待退款、退款中、退款成功、退款失败

**订单操作功能**： 对应订单操作区（三个按钮：详情、删除、编辑）
  - 详情：点击查看订单详情（弹窗页面）（展示订单所有详细信息）
  - 删除：点击删除订单（弹窗页面，弹出确认删除警告弹窗）
  - 编辑：点击编辑订单（弹窗页面展示，弹窗页面的左侧是订单的详细信息，右侧是操作按钮，按钮从上往下排列。操作按钮包括：订单支付确认、取消订单、退款订单、删除订单）

  订单操作按钮说明：
  - 支付确认：点击订单支付确认（弹窗页面，弹出支付确认弹窗，支付后系统自动开通业务）
  - 取消订单：点击取消订单（弹窗页面，弹出取消订单弹窗）
  - 退款订单：点击退款订单（弹窗页面，弹出退款订单弹窗）
  - 删除订单：点击删除订单（弹窗页面，弹出删除订单弹窗）

**筛选订单功能**：
  - 输入框信息筛选：可输入订单编号、订单用户名称、订单用户ID进行订单搜索
  - 订单类型筛选：课程订单、智能体订单、AI插件订单、商城订单
  - 订单状态筛选：待支付、已支付、待退款、退款中、退款成功、退款失败
  - 订单时间筛选：按天、月、年维度筛选
  - 订单金额筛选：输入订单金额，筛选出金额大于等于输入金额的订单

#### 2.5.3 【退款订单】列表页面功能
**列表展示**: 默认展示全平台所有退款订单
展示信息包含：订单编号、归属租户名称、归属租户ID、订单创建时间、订单类型、订单金额、订单退款金额、订单支付时间、订单支付方式、订单状态、订单备注、【订单退款】操作按钮

**点击【订单退款】操作按钮**（弹窗页面，弹出退款订单详情与操作页面，页面左侧是退款订单详情，右侧是退款操作按钮）
  - 退款操作通过按钮说明：点击通过退款订单（弹窗页面，输入退款金额/全额，选择退款方式：线上退款/线下退款，选择线上退款则原路返回，系统自动退款，选择线下退款则财务打款并上传支付截图保存后，通过邮箱通知客户）（线上退款：只支持全额退款，其它非全额退款的订单全部走线下退款方式）
  - 退款操作拒绝按钮说明：点击拒绝退款订单（弹窗页面，弹出拒绝退款订单弹窗，填写拒绝理由，保存后通过邮箱通知客户）

**筛选订单功能**：
  - 输入框信息筛选：可输入订单编号、订单用户名称、订单用户ID进行订单搜索

#### 2.5.4 【充值订单】列表页面功能
**列表展示信息**: 充值编号、充值时间、充值金额、充值方式、充值状态、充值备注、充值操作区（三个按钮：详情、删除、操作）

操作区按钮：
- 详情：点击查看充值详情（弹窗页面）
- 删除：点击删除充值（弹窗页面，弹出确认删除警告弹窗）
- 操作：点击操作（弹窗页面，展示操作按钮）

信息说明
充值方式：微信/支付宝/易支付
充值状态：支付成功/支付失败/支付取消




### 2.6 财务功能（功能规划中，准备后期开发）

### 2.7 权限功能（功能规划中，准备后期开发）

### 2.8 数据功能（功能规划中，准备后期开发）




### 2.9 全平台运维功能

#### 2.9.1 【系统监控】页面功能
##### 2.9.1.1 服务状态监控
**微服务状态**: 实时监控5个微服务容器状态（ai-users、ai-agents、ai-llms、ai-tools、ai-admin），展示服务名称、容器状态（运行中/停止/异常）、运行时长、重启次数、CPU使用率、内存使用率
**数据库状态**: 监控PostgreSQL、Redis、MongoDB连接状态，展示数据库类型、连接状态、响应时间、连接数、存储使用率
**负载均衡状态**: 监控Nginx状态，展示服务状态、处理请求数、错误率、平均响应时间
**系统资源**: 监控服务器整体资源使用情况，展示CPU使用率、内存使用率、磁盘使用率、网络IO、系统负载

##### 2.9.1.2 API性能监控
**接口响应时间**: 统计各微服务API平均响应时间、最大响应时间、最小响应时间，支持按时间段查询
**接口调用统计**: 展示接口调用总次数、成功次数、失败次数、成功率，支持按接口路径分组统计
**慢查询检测**: 自动检测响应时间超过2秒的API请求，展示请求路径、响应时间、请求参数、错误信息
**错误率分析**: 统计各接口错误率趋势，支持按HTTP状态码分类展示

##### 2.9.1.3 业务指标监控
**用户活跃度**: 实时统计在线用户数、各功能模块访问人数（智能体、大模型API、AI插件、商城、课堂）
**调用量统计**: 统计智能体对话调用次数、大模型API调用次数、AI插件调用次数，支持按租户、按时间维度统计
**资源消耗**: 监控各租户资源使用情况，包括存储空间、API调用配额、带宽使用量

##### 2.9.1.4 主菜单设计
系统监控左侧子菜单包括：服务状态、API性能、业务指标


#### 2.9.2 【数据管理】页面功能
##### 2.9.2.1 数据库管理
**容量监控**: 展示各数据库当前使用容量、总容量、使用率，设置容量预警阈值（80%、90%）
**性能分析**: 展示数据库连接数、查询执行时间、慢查询日志、索引使用情况分析
**表空间管理**: 展示各表的数据量、索引大小、增长趋势，支持表空间清理建议
**连接池监控**: 监控数据库连接池状态，展示活跃连接数、空闲连接数、等待连接数

##### 2.9.2.2 备份管理
**备份策略配置**: 设置自动备份时间、备份保留天数、备份类型（全量/增量）
**备份任务管理**: 展示备份任务列表，包括备份时间、备份类型、备份大小、备份状态、操作按钮（下载、删除、恢复）
**备份验证**: 自动验证备份文件完整性，展示验证结果和备份可用性状态
**恢复管理**: 支持选择备份文件进行数据恢复，提供恢复进度和结果反馈

##### 2.9.2.3 缓存管理
**Redis监控**: 展示Redis使用内存、键数量、命中率、过期键数量、连接数
**缓存分析**: 按业务模块统计缓存使用情况，展示热点数据、缓存穿透统计
**缓存操作**: 支持缓存清理、缓存刷新、指定键删除功能
**缓存配置**: 设置缓存过期时间、内存限制、淘汰策略

##### 2.9.2.4 菜单设计
数据管理左侧子菜单包括：数据库管理、备份管理、缓存管理


#### 2.9.3 【运维操作】页面功能
##### 2.9.3.1 容器管理
**容器列表**: 展示所有容器信息，包括容器名称、镜像版本、状态、端口映射、创建时间、操作按钮（启动、停止、重启、查看日志、进入容器）
**批量操作**: 支持批量启动、停止、重启容器，提供操作确认弹窗
**容器日志**: 实时查看容器日志，支持日志级别过滤、关键词搜索、日志导出
**镜像管理**: 展示镜像列表、镜像大小、创建时间，支持镜像删除、镜像更新

##### 2.9.3.2 配置管理
**环境变量管理**: 展示.env文件配置项，支持在线编辑、配置验证、配置备份
**配置变更历史**: 记录所有配置变更历史，展示变更时间、变更人、变更内容、回滚操作

##### 2.9.3.3 系统维护
**系统重启**: 支持单个服务重启、全系统重启，提供重启进度和状态反馈
**清理工具**: 提供日志清理、临时文件清理、缓存清理、数据库清理功能
**系统更新**: 支持系统版本更新、补丁安装、更新回滚
**维护模式**: 支持开启维护模式，暂停用户访问，展示维护公告

##### 2.9.3.4 菜单设计
运维操作左侧子菜单包括：容器管理、配置管理、系统维护

#### 2.9.4 【告警中心】页面功能
##### 2.9.4.1 告警规则配置
**告警规则设置**: 配置各类告警规则，包括CPU使用率、内存使用率、磁盘使用率、API响应时间、错误率等阈值设置
**告警级别**: 设置告警级别（紧急、重要、一般），不同级别采用不同通知方式
**通知渠道**: 配置告警通知渠道，支持邮件、短信通知超级管理员

##### 2.9.4.2 告警历史管理
**告警列表**: 展示历史告警记录，包括告警时间、告警类型、告警级别、告警内容、处理状态、处理人、操作按钮（查看详情、标记处理、忽略）
**告警统计**: 统计告警数量趋势、告警类型分布、处理时长统计

##### 2.9.4.3 故障诊断
**错误日志聚合**: 自动收集各服务错误日志，按错误类型、发生频率进行分类展示
**异常堆栈分析**: 展示详细的异常堆栈信息，支持错误定位和根因分析
**服务依赖图**: 展示微服务间调用关系图，快速定位故障影响范围
**健康检查**: 点击执行系统健康检查，生成健康报告和优化建议

##### ******* 菜单设计
告警中心左侧子菜单包括：告警规则、告警历史、故障诊断

#### 2.9.5 【日志分析】页面功能
##### ******* 日志聚合查询
**日志搜索**: 支持跨服务日志搜索，可按时间范围、日志级别、关键词、服务名称进行搜索
**日志过滤**: 提供多维度过滤条件，包括租户ID、用户ID、IP地址、请求路径等
**日志导出**: 支持搜索结果导出，可选择导出格式（CSV、JSON、TXT）
**实时日志**: 提供实时日志流查看功能，支持多服务同时监控

##### ******* 安全审计
**登录审计**: 记录所有用户登录行为，包括登录时间、IP地址、设备信息、登录结果
**操作审计**: 记录敏感操作日志，如数据修改、权限变更、配置修改等
**API调用审计**: 记录所有API调用日志，包括调用者、调用时间、请求参数、响应结果
**数据访问审计**: 记录数据库访问日志，监控数据访问权限和数据安全

##### ******* 菜单设计
日志分析左侧子菜单包括：日志查询、安全审计

#### 2.9.6 【性能优化】页面功能
##### ******* 性能分析报告
**系统性能评估**: 生成系统整体性能评估报告，包括响应时间、吞吐量、资源利用率分析
**瓶颈识别**: 自动识别系统性能瓶颈，提供优化建议和解决方案
**性能趋势**: 展示系统性能历史趋势，预测性能变化和容量规划
**基准测试**: 提供性能基准测试工具，支持压力测试和性能对比

##### ******* 数据库优化
**慢查询分析**: 分析数据库慢查询，提供SQL优化建议和索引建议
**索引优化**: 分析表索引使用情况，提供索引优化方案
**查询计划分析**: 展示SQL执行计划，识别查询性能问题
**数据库调优**: 提供数据库参数调优建议，优化数据库性能

##### 2.9.6.3 缓存优化
**缓存命中率分析**: 分析缓存命中率，识别缓存效果和优化空间
**热点数据分析**: 识别热点数据访问模式，优化缓存策略
**缓存穿透检测**: 检测缓存穿透问题，提供解决方案
**缓存策略优化**: 提供缓存策略优化建议，提升缓存效率

##### 2.9.6.4 菜单设计
性能优化左侧子菜单包括：性能分析、数据库优化、缓存优化

#### 2.9.7 【运维工具】页面功能
##### 2.9.7.1 自动化运维
**定时任务管理**: 配置和管理系统定时任务，包括数据备份、日志清理、报告生成等
**自动化脚本**: 提供常用运维脚本库，支持一键执行和自定义脚本

##### 2.9.7.2 数据迁移工具
**数据导入导出**: 支持各种格式数据导入导出，包括用户数据、配置数据、业务数据

##### 2.9.7.3 系统诊断工具
**健康检查工具**: 提供系统健康检查工具，自动检测系统各项指标
**网络诊断**: 提供网络连通性测试、延迟测试、带宽测试工具
**服务依赖检查**: 检查服务间依赖关系，识别服务调用问题

##### 2.9.7.4 菜单设计
运维工具左侧子菜单包括：自动化运维、数据迁移、系统诊断

#### 2.9.8 【平台配置】页面功能
阿里云短信
阿里云存储
只需要配置这两个就可以了，满足平台的日常通知和存储需求


### 2.10 ai-admin管理后台左侧菜单

平台超级管理员管理后台左侧主菜单规划汇总：

├── 控制台（### 2.2 全平台数据大屏功能）
│
├── 租户管理
│   ├── 数据概览（##### 2.3.4.1 【数据概览】页面功能）
│   ├── 租户域名（##### 2.3.4.2 【租户域名】页面功能）
│   ├── 租户管理（##### 2.3.4.3 【租户管理】页面功能）
│   └── 租户数据（##### 2.3.4.4 【租户数据】页面功能）
│
├── 用户管理
│   ├── 数据概览（#### 2.4.1 【数据概览】页面功能）用横向菜单展示三个数据概览，依次：数据概览、实时数据、趋势分析
│   ├── 用户设置（#### 2.4.2 【用户设置】页面功能）
│   └── 用户管理（#### 2.4.3 【用户管理】页面功能）
│
├── 订单管理
│   ├── 订单概览（#### 2.5.1 【订单概览】页面功能）
│   ├── 订单汇总（#### 2.5.2 【订单汇总】列表页面功能）
│   ├── 退款订单（#### 2.5.3 【退款订单】列表页面功能）
│   └── 充值订单（#### 2.5.4 【充值订单】列表页面功能）
│
├── 财务管理（功能规划中，准备后期开发）
├── 权限管理（功能规划中，准备后期开发）
├── 数据管理（功能规划中，准备后期开发）
│
├── 运维
│   ├── 系统监控（#### 2.9.1 【系统监控】页面功能）
│   ├── 数据管理（#### 2.9.2 【数据管理】页面功能）
│   ├── 运维操作（#### 2.9.3 【运维操作】页面功能）
│   ├── 告警中心（#### 2.9.4 【告警中心】页面功能）
│   ├── 日志分析（#### 2.9.5 【日志分析】页面功能）
│   ├── 性能优化（#### 2.9.6 【性能优化】页面功能）
│   ├── 运维工具（#### 2.9.7 【运维工具】页面功能）
│   └── 平台配置（#### 2.9.8 【平台配置】页面功能）



## 三、SAAS租户核心功能

### 3.1 SAAS租户之数据大屏（该租户下所用业务的数据统计大屏）
- **快捷操作**: 常用管理操作的快捷入口（可自定义快捷操作）
- **总体指标**: 统计展示该租户的用户总数、订单总数、总营业额、智能体总数、大模型对接渠道总数据、AI插件总数量、共6大总体核心指标（分两行展示）
- **趋势图表**: 统计展示该租户的用户数、订单数、总营业额的趋势变化，支持按天、按月、按年的维度进行趋势分析（分一行展示）
- **实时数据**: 
第一行：当前在线用户总人数、当前在线推广员总人数、当前浏览智能体总人数、当前浏览模型API总人数、当前浏览AI插件总人数（共5个实时数据）
第二行：显示实时浏览用户列表（分三列列表展示）
左：当前智能体所有页面实时浏览用户列表（显示15个用户，可以下拉查看更多）
中：当前模型API所有页面实时浏览用户列表（显示15个用户，可以下拉查看更多）
右：当前AI插件所有页面实时浏览用户列表（显示15个用户，可以下拉查看更多）

### 3.2 SAAS租户之智能体功能

#### 3.2.1 智能体业务核心价值
- **平台聚合**: 支持Coze、Dify、n8n智能体开发平台对接
- **统一管理**: 提供完整的智能体生命周期管理功能
- **应用商店**: 构建智能体生态，支持用户在线使用与代码买断
- **数据分析**: 提供详细的使用统计和分析功能
- **多租户支持**: 通过租户ID进行隔离，确保每个租户配置的所有参数与其它租户完全隔离，，确保每个用户使用的智能体都对应到自己的绑定租户

#### 3.2.2 智能体业务流程说明

智能体的业务流程说明：

第1步：智能体设置（在智能体菜单页面设置智能体分类和平台对接，这是智能体业务的核心基础）
第2步：智能体同步（在智能体同步页面，同步智能体编程平台（coze、dify、n8n）已开发完成并上线发布成功的智能体，这是智能体的源头）
第3步：智能体审核（同步后，智能体到达【智能体审核】审核页面，可以对同步的智能体进行编辑，审核，拒绝等操作。
第4步：智能体列表（审核通过后，到达智能体列表，列表展示所有平台同步过来并已审核通过的智能体，并且每一行的智能体，最右边都有编辑、上线、下线、删除等操作按钮，页面右上方也有批量上线、下线、和批量删除按钮）
第5步：智能体商店（上一步点击上线后，智能体到达【智能体商店】列表，展示所有已上线的智能体，同步也在客户前端展示，客户可以在前端查看所有已上线的智能体，点击智能体，进入智能体详情页，展示智能体的详细信息，包括智能体的名称、分类、描述、使用次数、使用人数、价格、是否支持买断等信息）

```
第三方平台配置 → 智能体同步 → 审核管理 → 列表展示 → 应用商店 → 用户使用 → 数据统计
    ↓             ↓           ↓         ↓         ↓         ↓         ↓
  Coze          自动同步     人工审核  展示所有    公开展示   实时监控   使用分析
  Dify          选择性同步   批量操作  集中管理    分类管理   用户追踪   性能统计
  n8n           增量更新     状态管理  集中维护    搜索推荐   行为记录   趋势分析
```

#### 3.2.3 智能体数据概览

- **总体指标**: 统计展示该租户的智能体总数、智能体API调用总数、智能体使用总人数、智能体营业额、智能体订单总数、共5大总体核心指标（分一行卡片展示）
- **实时数据**: 左侧统计当前正在浏览智能体的总人数、右侧展示正在浏览智能体页面的用户列表，默认显示15行，下拉可以查看更多（分一行展示）
- **趋势图表**: 统计展示该租户的智能体浏览量、智能体使用量、API调用总数、智能体分享量、智能体总营业额、智能体订单总数量的趋势变化，支持按天、按月、按年的维度进行趋势分析（分两行展示）

#### 3.2.4 智能体配置功能

在智能体设置页面，需要开发两个功能（分类设置、平台对接）左侧两个子菜单

功能一、智能体分类：SAAS租户可以自定义智能体分类，分类名称不能重复，分类名称不能超过20个字符，分类描述不能超过100个字符。
功能二、智能体平台对接：支持coze、dify、n8n等智能体平台的对接，需要在智能体设置-平台对接配置对接参数。

**平台对接说明**：
- 1. 项目前期只开发扣子coze平台对接，dify、n8n平台对接后续再开发
- 2. 配置参数后，有测试按钮，可测试对接是否连通，是否完全达到可同步状态
- 3. 所有的平台对接都可以对接多个渠道，比如扣子可以对接多个空间，每个空间都定义为一个对接渠道，每个渠道都可以同步该空间已发布的智能体，渠道可自定义名称，如：扣子渠道一，扣子渠道二，扣子渠道三......
这里需要注意：如果SAAS管理员配置的是同一个扣子帐号的情况下，那么它的配置参数除了【Coze 空间ID】不一样外，其它对接参数可能都是一样的，但如果配置的是不同的扣子帐号，那么它的配置参数就肯定全部不一样，也就是说【Coze 空间ID】这个配置参数是具有唯一性的，在配置并保存参数时需要验证是否唯一存在数据库，如果已存在则提示：Coze空间ID已存在，请重新配置
- 4. 扣子对接参数示例说明：
API URL：https://api.coze.cn
授权空间ID：7467457693234642971
授权应用ID：1108317881949
授权公钥：wMYntoyIHNJHnhAmPIOYTquI6ksTvjVXCQMKl8fjnDs
授权私钥：
-----BEGIN PRIVATE KEY-----
——【共1649个字符】——
-----END PRIVATE KEY-----

#### 3.2.5 智能体同步功能
**智能体同步**: 支持同步coze、dify、n8n等智能体平台的智能体（同步前必须先在智能体设置-平台对接进行配置对接参数）

同步流程
第1步：选择要同步的智能体平台（coze、dify、n8n）
第2步：选择已对接的渠道（因为每个平台都可以对接多个渠道，所以第2步需要选择要同步的渠道）
第3步：选择渠道后，系统自动同步该渠道已发布的智能体列表，并进行列表展示，列表可以单选、多选要同步的智能体
第4步：勾选需要同步的智能体，点击同步按钮，进行同步操作
第5步：同步后智能体到达智能体审核列表页，列表展示信息：同步的头像、智能体名称、智能体描述

同步说明：
1. 当选择的智能体已同步过时，再次选择同步时，会提示是否要覆盖同步，选择同步后会覆盖掉之前的所有的同步、审核、上线、下线记录，如果已上线到智能体商店，选择同步后会自动下线，自动清除智能体商店、智能体列表的显示
2. 当选择的智能体未同步时，则按设计好的流程执行

#### 3.2.6 智能体审核列表功能
**智能体审核**: 当智能体同步后，智能体到达审核列表，SAAS管理员可以对智能体进行审核，审核通过后，智能体会在智能体列表中展示出来
**列表展示信息**：同步的头像、智能体名称、智能体描述、同步的渠道、同步时间、操作区（拒绝/通过）对单个智能体进行审核
**单个审核**：操作区（拒绝/通过）点击拒绝则弹出拒绝审核弹窗，弹窗中需要输入拒绝的原因，点击确定则拒绝审核。点击通过按钮则弹出通过审核弹窗，弹窗显示智能体头像/名称/描述/同步渠道/同步时间,点击确定则通过审核
**批量审核**：在列表勾选多个智能体，点击批量审核按钮，可对多个智能体进行批量审核（按钮放在右上角位置）

审核说明：
1. 审核通过：SAAS管理员可以对智能体进行审核，审核通过后，智能体则不展示在审核列表中了，智能体会在智能体列表中展示出来
2. 审核不通过：SAAS管理员可以对智能体进行审核，如果审核不通过则需要输入不通过的原因，智能体也不展示在审核列表中了，需要重新进行同步

#### 3.2.7 智能体管理列表功能
**智能体管理列表**：当智能体审核通过后，智能体会在智能体列表中展示出来
**列表展示信息**：同步的头像、智能体名称、智能体描述、智能体分类、同步的渠道名称、同步时间、审核时间、审核人、排序、是否发布、操作区（查看/编辑/发布/删除）
**批量操作**：支持勾选多个智能体，进行批量删除和发布这两个操作，按钮放在页面右上角，两个按钮名称分别为：批量删除、批量发布（如果选择的智能体有发布到智能体商店，则提示是否要先下线，点击是，系统自动下线并删除在智能体商店列表中的智能体）
**智能体筛选操作**：支持根据智能体名称输入框输入搜索、支持智能体分类、同步渠道、同步时间、审核时间、审核人、是否支持买断等选择进行筛选
**智能体操作区**：对应操作区（查看/编辑/发布/删除）
1. 查看操作：点击查看按钮，可查看智能体的详细信息
2. 编辑操作：点击编辑按钮，可对智能体进行设置头像、设置名称、设置描述、设置分类、排序、删除、是否使用CEES-API、是否支持买断等操作（弹窗页面）
- 编辑页面排版：左侧显示智能体已有详细信息，右侧显示所有操作按钮，依次从上到下排列，顺序为：设置头像、设置名称、设置描述、设置分类、是否买断、删除
- 设置头像说明：点击设置头像按钮，可上传智能体的头像，头像大小为1:1，建议尺寸为200*200，支持jpg、png、jpeg格式，上传时需要选择上传到哪个云存储，比如上传到阿里云OSS
- 设置分类说明：点击设置分类按钮，弹出分类选择弹窗，选择后点击确定按钮，即可设置智能体分类
- 设置使用CEES-API说明：点击设置是否使用CEES-API，弹出是否使用CEES-API弹窗，选择是/否，如果选择【是】，即代表该智能体使用CEES-API，客户在前端使用智能体时，自动弹出选择CEES-API的API令牌和选择大模型的弹窗，保存后，客户在前端使用智能体时，会自动调用CEES-API的大模型API中转系统，进行大模型的调用
- 设置是否买断：点击设置是否买断，弹出是否买断弹窗，选择是/否，如果选择【是】，即代表该智能体支持买断，然后在代码框输入json代码，点击保存按钮即可保存，保存后智能体上架到客户前端，则显示【支持买断】标签
- 修改后左侧的智能体信息会实时更新
3. 发布操作：点击发布按钮，可将智能体发布到智能体商店列表（注意，这里点击发布后智能体还不能直接显示在客户前端，需要在智能体商店列表上线操作后才会显示在客户前端）
4. 删除操作：点击删除按钮，可删除智能体（如果该智能体有发布到智能体商店，则提示是否要先下线，点击是，系统自动下线并删除在智能体商店列表中的智能体）只要在智能体列表删除后，就需要重新进行渠道同步

#### 3.2.8 智能体商店功能
**智能体商店列表**: 当智能体列表点击发布后，则在智能体商店列表展示
**列表展示信息**：智能体头像、智能体名称、智能体描述、智能体分类、同步的渠道名称、同步时间、审核时间、审核人、排序、上线状态、操作区（上线/下线/删除）
**批量操作**：支持勾选多个智能体，进行批量上线、下线、删除这三个操作，按钮放在页面右上角，三个按钮名称分别为：批量上线、批量下线、批量删除
**智能体筛选操作**：支持根据智能体名称输入框输入搜索、支持智能体分类、同步渠道、是否支持买断、已上线、已下线等选择进行筛选
**智能体操作区**: 操作区（上线/下线/删除）
1. 上线操作：上线后智能体会在客户前端显示
2. 下线操作：下线后智能体则不会在客户前端显示
3. 删除操作：删除后智能体则不会在客户前端显示，但在智能体列表中还会展示出来



### 3.3 SAAS租户大模型API功能（CEES-API）

#### 3.3.1 大模型API功能介绍
介绍一：CEES-API是基于new-api开源项目深度定制开发的企业级大模型API中转系统，把它集成到AI生态平台的ai-llms容器微服务器中，核心是把new-api的大模型API中转功能进行保留，其它的功能如用户管理/支付/订单/充值/个人设置/钱包/推广机制/参数配置等功能，则使用AI生态SAAS系统中的基础功能

介绍二：与现有功能全场景智能营销的配额系统、智能体需使用到的大模型API调用、AI插件工具需使用到的API调用，进行无缝衔接，举例在使用文案润色智能体时，需要调用claude4模型，则在使用智能体时，自动弹出选择CEES-API的API令牌和选择大模型的弹窗，保存后，客户在前端使用智能体时，会自动调用CEES-API的大模型API中转系统，进行大模型的调用

#### 3.3.2 大模型API功能核心价值
- **统一网关**: 统一使用OpenAI兼容接口
- **多模态支持**: 文本、图像、音频、视频全方位AI服务
- **数据分析**: 提供详细的使用统计和分析功能
- **多租户支持**: 通过租户ID进行隔离，确保每个租户配置的所有参数与其它租户完全隔离，每个用户使用的大模型API都对应到自己的绑定租户

#### 3.3.3 大模型API业务流程说明

第三方平台调用CEES-API的业务流程：
第1步：SAAS管理员在渠道管理添加大模型渠道
第2步：SAAS管理员在系统设置中设置倍率
第3步：客户在个人中心生成调用大模型API的令牌
第4步：客户在第三方平台配置CEES-API的API令牌、API地址、对应模型信息进行调用（第三方平台如：Cherry Studio、ComfyUI）

智能体/AI插件的系统内部使用（CEES-API）：
第1步：SAAS管理员在渠道管理添加大模型渠道
第2步：SAAS管理员在系统设置中设置倍率
第3步：客户在使用智能体/AI插件时，如果智能体/AI插件设置了需要使用CEES-API，则在进行智能体对话时，系统自动弹出选择CEES-API的API令牌和选择大模型的弹窗，保存后，客户在前端使用智能体时，会自动调用CEES-API的大模型API中转系统，进行大模型的调用

#### 3.3.4 大模型API功能

1. 数据概览：对应new-api左侧的数据看板菜单
2. 渠道管理：对应new-api左侧的渠道菜单
3. 模型列表：对应new-api顶部的模型列表菜单
4. 模型用户：对应new-api左侧的用户管理
5. 日志管理：使用日志、绘图日志、任务日志分三个横向菜单
6. 系统设置：对应new-api左侧的系统设置菜单


#### 3.3.5 new-api移植到CEES-API流程规划
第一步：在/www/ceesai/llms目录下，下载new-api源码

第二步：完整部署new-api，包括数据库、Redis、主程序三个容器

第三步：由平台超级管理员进行功能删除，删除后并验证系统是否还能正常使用主体功能
- 删除顶部菜单的（文档/关于）两个菜单
- 删除左侧主菜单（操练场/聊天/兑换码/钱包/个人设置）功能
- 删除左侧主菜单-系统设置-聊天设置功能
- 删除左侧主菜单-系统设置-支付设置功能

第四步：开始迁移功能，迁移的功能顺序如下：
1. 系统设置功能，迁移到AI生态项目ai-users
2. 用户功能，迁移到AI生态项目ai-users
3. 推广功能，迁移到AI生态项目ai-users
4. 支付功能，迁移到AI生态项目ai-users
5. 日志管理功能，迁移到AI生态项目ai-users

最后一步：测试所有功能迁移成功后，保留new-api主程序容器，删除new-api的数据库、Redis





_____________________________________________
### 3.4 SAAS租户AI插件功能（规划中）

需要把AI插件功能设计为可扩展插件生态系统，便于后续不断增加不同功能、不同需求的插件
_____________________________________________





### 3.5 SAAS租户之用户管理

### 3.5.1 功能概述
SAAS租户之用户管理功能，是租户管理员对其tenant_id下所有用户进行管理的核心功能模块。租户管理员通过该功能可以全面管理租户内的用户，包括用户升级、权限控制、行为监控等，确保租户内用户的有序管理和安全运营。

**核心定位**
- **管理范围**: 租户内所有用户的完整管理权限
- **权限特点**: 基于tenant_id的数据隔离，只能管理本租户用户
- **操作对象**: 普通用户、推广员、租户内管理员
- **管理深度**: 从用户基础信息到行为记录的全方位管理

### 3.5.2 数据概览

- **总体指标**: 统计展示该租户旗下的用户总人数、推广员总人数、已付费总人数、活跃用户总人数共4大总体核心指标（分一行卡片展示）【活跃用户标准为连接3天有在线的用户】
- **实时数据**: 左侧统计当前在线的总人数、右侧展示在线用户列表，列表需按在线时长进行排序，默认显示15行，下拉可以查看更多（分一行展示）
- **趋势图表**: 统计展示该租户的用户增长人数、在线人数、推广员增长人数的趋势变化，支持按天、按月、按年的维度进行趋势分析（分一行展示）

### 3.5.2 用户列表管理功能

#### 3.5.2.1 用户列表展示信息
**功能描述**: 展示租户内所有用户的综合信息列表

**列表展示信息**:
- **基础信息**: 用户头像、用户名、真实姓名、手机号、邮箱、注册时间
- **身份标识**: 用户类型（普通用户/推广员）
- **状态信息**: 账户状态（正常/冻结/禁用）、最后登录时间、在线状态
- **业务数据**: 账户余额、积分余额、推广收益、消费总额、是否付费用户
- **操作区域**: 详情、编辑、删除、操作按钮

#### 3.5.2.2 用户筛选和搜索
**功能描述**: 提供多维度的用户筛选和精确搜索功能

**筛选条件**:
- **基础筛选**: 用户名、手机号、邮箱、真实姓名关键词搜索
- **状态筛选**: 账户状态（正常/冻结/禁用）、在线状态（在线/不在线）
- **身份筛选**: 用户类型（普通用户/推广员）
- **时间筛选**: 注册时间范围、最后登录时间范围
- **业务筛选**: 消费金额范围、是否付费用户、推广业绩范围

#### 3.5.2.3 批量操作功能
**功能描述**: 支持对多个用户进行批量管理操作

**批量操作类型**:
- **状态批量操作**: 批量启用、禁用、冻结用户账户
- **通知批量操作**: 批量发送系统通知、营销消息

### 3.5.3 用户详细管理功能

#### 3.5.3.1 用户升级为推广员
**功能描述**: 租户管理员可以将普通用户升级为推广员

**升级流程**:
1. **选择用户**: 从用户列表中选择要升级的普通用户
2. **审核资质**: 查看用户的基础信息、活跃度、信用记录
3. **设置等级**: 选择推广员等级（一级推广员/二级推广员）
4. **配置权限**: 设置推广权限、佣金比例、推广范围
5. **确认升级**: 确认升级操作，系统自动生成推广员ID和推广码

**升级条件验证**:
- **基础条件**: 账户状态正常、已完成实名认证
- **活跃度要求**: 注册时间超过7天、最近30天有登录记录
- **信用要求**: 无违规记录、无恶意行为记录
- **业务要求**: 有一定的平台使用经验和了解度

#### 2.3.2 用户禁用和启用
**功能描述**: 租户管理员可以对用户账户进行禁用和启用操作

**禁用功能**:
- **禁用原因**: 违规行为、恶意操作、安全风险、用户申请等
- **禁用范围**: 登录禁用、功能禁用、部分功能禁用
- **禁用时长**: 临时禁用（指定时长）、永久禁用
- **禁用通知**: 自动发送禁用通知邮件/短信给用户

**启用功能**:
- **启用条件**: 禁用原因已解决、用户申诉通过、禁用期满
- **启用验证**: 验证用户身份、确认问题已解决
- **启用通知**: 自动发送启用通知邮件/短信给用户
- **权限恢复**: 自动恢复用户原有权限和功能访问

#### 2.3.3 用户删除功能
**功能描述**: 租户管理员可以删除用户账户（谨慎操作）

**删除类型**:
- **软删除**: 标记删除，数据保留，可恢复
- **硬删除**: 物理删除，数据清除，不可恢复

**删除前检查**:
- **业务检查**: 检查用户是否有未完成订单、未提现佣金
- **关联检查**: 检查用户是否有推广关系、团队成员
- **数据检查**: 检查用户是否有重要业务数据需要保留

**删除流程**:
1. **删除申请**: 管理员提交删除申请，说明删除原因
2. **风险评估**: 系统自动评估删除风险和影响范围
3. **数据备份**: 自动备份用户重要数据（如需要）
4. **执行删除**: 执行删除操作，清理相关数据
5. **删除记录**: 记录删除操作日志，便于审计追踪

#### 2.3.4 用户资料修改
**功能描述**: 租户管理员可以修改用户的基础资料信息

**可修改信息**:
- **基础信息**: 用户名、真实姓名、性别、生日
- **联系信息**: 手机号、邮箱、联系地址
- **身份信息**: 身份证号、企业信息（需要验证）
- **扩展信息**: 职业信息、个人简介、标签设置

**修改权限控制**:
- **敏感信息**: 手机号、邮箱修改需要验证原信息
- **身份信息**: 身份证号修改需要重新认证
- **安全信息**: 密码修改需要用户本人操作
- **操作记录**: 所有修改操作都会记录操作日志

### 2.4 用户行为监控功能

#### 2.4.1 用户行为记录查看
**功能描述**: 查看用户在平台上的各种行为记录和操作轨迹

**行为记录类型**:
- **登录行为**: 登录时间、登录IP、登录设备、登录方式
- **功能使用**: AI功能使用记录、智能体对话记录、工具使用记录
- **交易行为**: 充值记录、消费记录、购买记录、提现记录
- **推广行为**: 推广链接生成、推广用户绑定、佣金获得记录
- **社交行为**: 分享行为、评论行为、收藏行为、点赞行为

**记录展示方式**:
- **时间轴展示**: 按时间顺序展示用户行为轨迹
- **分类展示**: 按行为类型分类展示相关记录
- **统计图表**: 用户行为的统计图表和趋势分析
- **详细信息**: 每个行为记录的详细信息和上下文

#### 2.4.2 用户登录日志
**功能描述**: 详细记录和展示用户的登录相关日志信息

**登录日志内容**:
- **登录信息**: 登录时间、登录IP地址、登录地理位置
- **设备信息**: 设备类型、操作系统、浏览器信息、设备指纹
- **登录方式**: 密码登录、验证码登录、第三方登录
- **登录结果**: 登录成功、登录失败、失败原因
- **会话信息**: 会话时长、退出时间、退出方式

**异常登录检测**:
- **异地登录**: 检测异常地理位置登录
- **异常设备**: 检测新设备或异常设备登录
- **异常时间**: 检测异常时间段登录
- **频繁登录**: 检测短时间内频繁登录尝试
- **安全提醒**: 异常登录自动发送安全提醒

#### 2.4.3 用户操作审计
**功能描述**: 记录和审计用户的重要操作，确保操作可追溯

**审计操作类型**:
- **账户操作**: 密码修改、资料修改、认证操作
- **资产操作**: 充值操作、提现操作、转账操作
- **权限操作**: 角色变更、权限申请、权限使用
- **业务操作**: 重要业务功能的使用和操作
- **安全操作**: 安全设置修改、设备管理操作

**审计信息记录**:
- **操作详情**: 操作类型、操作内容、操作参数
- **操作环境**: 操作时间、操作IP、操作设备
- **操作结果**: 操作成功、操作失败、失败原因
- **影响范围**: 操作影响的数据和功能范围
- **关联信息**: 相关的业务数据和上下文信息

### 2.5 用户数据统计功能

#### 2.5.1 用户概览统计
**功能描述**: 提供租户内用户的整体统计数据和趋势分析

**统计指标**:
- **用户规模**: 总用户数、新增用户数、活跃用户数、流失用户数
- **用户结构**: 用户类型分布、年龄分布、地域分布、职业分布
- **用户活跃度**: 日活跃用户、周活跃用户、月活跃用户
- **用户价值**: 付费用户数、平均消费金额、用户生命周期价值

**趋势分析**:
- **增长趋势**: 用户注册趋势、活跃度趋势、留存率趋势
- **行为趋势**: 功能使用趋势、消费趋势、推广趋势
- **质量趋势**: 用户质量评分趋势、满意度趋势

#### 2.5.2 用户行为分析
**功能描述**: 深入分析用户在平台上的行为模式和偏好

**行为分析维度**:
- **使用频次**: 功能使用频次、登录频次、活跃时段分析
- **使用深度**: 功能使用深度、会话时长、页面浏览深度
- **使用偏好**: 功能偏好、内容偏好、交互偏好
- **转化路径**: 用户转化漏斗、关键转化节点分析

**分析结果应用**:
- **产品优化**: 基于用户行为优化产品功能和体验
- **运营策略**: 制定针对性的用户运营策略
- **营销决策**: 支持精准营销和个性化推荐
- **风险预警**: 识别用户流失风险和异常行为





### 3.6 订单管理
#### 3.6.1 订单管理功能介绍


订单概览
订单列表
退款订单
充值订单


### 3.7 AI商城
#### 3.7.1 AI商城功能介绍
商城概览
商城设置
商城列表
商城管理


### 3.8 课程中心
#### 3.8.1 课程中心功能介绍
课程概览
课程列表
课程设置
课程管理





### 3.9 营销推广
#### 3.9.1 营销推广功能介绍
配额组装
权益组装
产品组装
推广中心




### 3.10 数据中心
#### 3.10.1 数据中心功能介绍
用户数据
订单数据
智能体数据
模型API数据
AI工具箱数据
商城数据
课程数据
推广数据



### 3.11 平台配置
#### 3.11.1 平台配置功能介绍
平台配置
存储配置
支付配置
微信配置
邮箱配置




### 2.12 租户功能菜单（ai-admin管理后台左侧菜单）

SAAS租户管理员管理后台左侧主菜单规划汇总：

├── 数据大屏（### 3.1 SAAS租户之数据大屏（该租户下所用业务的数据统计大屏））
│
├── 智能体
│   ├── 数据概览（#### 3.2.3 智能体数据概览）
│   ├── 智能体同步（#### 3.2.5 智能体同步功能）
│   ├── 智能体审核（#### 3.2.6 智能体审核列表功能）
│   ├── 智能体列表（#### 3.2.7 智能体管理列表功能）
│   ├── 智能体商店（#### 3.2.8 智能体商店功能）
│   └── 智能体设置（#### 3.2.4 智能体配置功能）
│
├── 大模型API
│   ├── 数据概览（对应new-api左侧的数据看板菜单）
│   ├── 渠道管理（对应new-api左侧的渠道菜单）
│   ├── 模型列表（对应new-api顶部的模型列表菜单）
│   ├── 模型用户（对应new-api左侧的用户管理）
│   ├── 日志管理（使用日志、绘图日志、任务日志）分三个横向菜单
│   └── 系统设置（对应new-api左侧的系统设置菜单）
│
├── AI插件
│   ├── 插件概览
│   ├── 插件列表
│   ├── 插件设置
│   └── 插件管理
│
├── 用户管理
│   ├── 用户概览
│   ├── 用户设置
│   └── 用户管理
│
├── 订单管理
│   ├── 订单概览
│   ├── 订单列表
│   ├── 退款订单
│   └── 充值订单
│
├── AI商城
│   ├── 商城概览
│   ├── 商城设置
│   ├── 商城列表
│   └── 商城管理
│
├── 课程中心
│   ├── 课程概览
│   ├── 课程列表
│   ├── 课程设置
│   └── 课程管理
│
├── 营销推广
│   ├── 配额组装
│   ├── 权益组装
│   ├── 产品组装
│   └── 推广中心
│
├── 数据中心
│   ├── 用户数据
│   ├── 订单数据
│   ├── 智能体数据
│   ├── 模型API数据
│   ├── AI工具箱数据
│   ├── 商城数据
│   ├── 课程数据
│   └── 推广数据
│
├── 平台配置
│   ├── 平台配置
│   ├── 存储配置
│   ├── 支付配置
│   ├── 微信配置
│   └── 邮箱配置



## 四、平台用户核心功能

## 功能概述

### 4.1 功能定位
用户功能是AI生态平台的核心基础设施，由ai-users微服务容器完成，承载着用户全生命周期管理、租户用户管理、用户认证、用户资产管理、推广员管理等核心业务，通过严格的多租户数据隔离和灵活的权限管理，支撑整个平台的用户运营体系。

### 4.2 核心设计原则
- **租户ID+用户ID参数传递**: 通过URL参数传递租户ID和用户ID
- **多租户数据隔离**: 所有用户数据严格按tenant_id隔离
- **身份权限分离**: 用户可同时拥有普通用户和推广员两种身份
- **用户ID统一机制**: 推广员和普通用户共享用户ID，推广员只是身份标识
- **资产统一管理**: 用户余额、积分、佣金等资产的统一管理

### 1.3 功能边界
- ✅ SAAS租户用户管理功能（租户管理员对用户的管理操作）
- ✅ 用户基础认证功能（注册、登录、密码管理、会话管理）
- ✅ 用户资产管理功能（充值、余额、积分、课程、智能体、插件管理）
- ✅ 用户推广员功能（推广员申请、佣金管理、推广数据统计）
- ❌ 具体AI业务功能（由ai-agents、ai-llms、ai-tools负责）
- ❌ 平台级管理功能（由ai-admin负责）
- ❌ 会员营销体系（属于营销功能模块）
- ❌ 积分系统和卡券营销（属于营销功能模块）

### 4.1 用户基础认证功能







### 5.1 用户注册登录功能

#### 5.1.1 多渠道注册
**功能描述**: 支持多种注册方式，降低用户注册门槛

**具体需求**:
- **手机号注册**: 支持+86手机号，短信验证码验证
- **邮箱注册**: 支持常见邮箱格式，邮件验证链接验证
- **第三方登录**: 微信OAuth、QQ登录、支付宝登录
- **企业批量注册**: 支持企业管理员批量创建员工账户

**业务规则**:
- 手机号/邮箱全平台唯一，不可重复注册
- 验证码5分钟有效期，每日最多发送10次
- 第三方登录首次需要绑定手机号或邮箱
- 企业注册需要营业执照认证




#### 2.1.2 多种登录方式
**功能描述**: 提供便捷的登录方式，提升用户体验

**具体需求**:
- **用户名+密码登录**: 支持用户名、邮箱、手机号作为登录标识
- **手机号+验证码登录**: 免密码快速登录
- **第三方快速登录**: 微信扫码、QQ一键登录
- **记住登录状态**: 支持7天、30天免登录

**安全机制**:
- 登录失败5次锁定账户30分钟
- 异地登录邮件/短信提醒
- 设备管理和信任设备机制
- 会话超时自动退出（24小时）

#### 2.1.3 密码管理
**功能描述**: 完善的密码安全管理机制

**具体需求**:
- **密码强度检测**: 8位以上，包含字母、数字、特殊字符
- **密码找回**: 手机验证码找回、邮箱链接找回
- **密码修改**: 旧密码验证后修改
- **密码历史**: 不允许使用最近3次使用过的密码

#### 2.2 用户资料管理功能

#### 2.2.1 基础信息管理
**功能描述**: 用户个人基础信息的完整管理

**具体需求**:
- **基础信息**: 昵称、头像、性别、生日、地区
- **联系信息**: 手机号、邮箱、微信号、QQ号
- **身份信息**: 真实姓名、身份证号（实名认证用）
- **头像管理**: 支持上传、裁剪、默认头像选择

#### 2.2.2 职业信息管理
**功能描述**: 用户职业相关信息管理，用于个性化推荐

**具体需求**:
- **工作信息**: 公司名称、职位、行业、工作经验
- **教育背景**: 学历、专业、毕业院校
- **技能标签**: 编程语言、专业技能、兴趣方向
- **个人简介**: 自我介绍、专业描述

#### 2.2.3 偏好设置管理
**功能描述**: 用户个性化偏好和系统设置

**具体需求**:
- **系统偏好**: 语言设置、时区设置、主题模式
- **通知偏好**: 邮件通知、短信通知、推送通知开关
- **隐私设置**: 资料可见性、搜索可见性、推荐设置
- **AI偏好**: 首选AI模型、对话风格、功能偏好

#### 2.3 用户权限控制功能

#### 2.3.1 角色权限体系
**功能描述**: 基于角色的用户权限管理系统

**权限层级**:
```
超级管理员 (Super Admin)
├── 平台全局管理权限
├── 所有租户数据访问权限
└── 系统配置和维护权限

租户管理员 (Tenant Admin)
├── 租户内全部功能管理权限
├── 用户管理和权限分配权限
└── 租户配置和设置权限

VIP用户 (VIP User)
├── 高级功能使用权限
├── 优先客服支持权限
└── 专属功能访问权限

普通用户 (Regular User)
├── 基础功能使用权限
├── 标准客服支持权限
└── 免费功能访问权限

访客用户 (Guest User)
├── 有限功能体验权限
├── 注册引导权限
└── 公开内容访问权限
```

#### 2.3.2 权限验证机制
**功能描述**: 细粒度的权限验证和控制

**具体需求**:
- **功能权限**: 基于用户角色的功能访问控制
- **数据权限**: 基于租户隔离的数据访问控制
- **操作权限**: 基于业务规则的操作权限控制
- **时间权限**: 基于时间限制的权限控制（如会员到期）

#### 2.4 用户安全验证功能

#### 2.4.1 身份认证系统
**功能描述**: 多层次的用户身份认证机制

**个人认证**:
- **手机号认证**: 短信验证码验证手机号真实性
- **邮箱认证**: 邮件链接验证邮箱真实性
- **身份证认证**: 身份证号码和姓名验证
- **人脸识别认证**: 活体检测和人脸比对（预留接口）

**企业认证**:
- **营业执照认证**: 营业执照信息验证
- **企业信息验证**: 企业基本信息核实
- **法人身份验证**: 法人身份证和授权验证
- **银行账户验证**: 企业银行账户验证

#### 2.4.2 安全监控系统
**功能描述**: 全方位的用户安全保护机制

**具体需求**:
- **登录安全**: 异常登录检测、设备指纹识别
- **操作安全**: 敏感操作二次验证、操作日志记录
- **数据安全**: 个人信息加密存储、数据脱敏展示
- **隐私保护**: 数据导出、数据删除、隐私设置


### 4.1 用户注册功能

#### 4.1.1 多渠道注册
**功能描述**: 支持多种注册方式，满足不同用户习惯和场景需求

**注册方式支持**:
- **手机号注册**: 手机号+短信验证码+密码
- **邮箱注册**: 邮箱+邮件验证+密码
- **微信注册**: 微信OAuth2授权登录
- **推广注册**: 通过推广链接或邀请码注册

**注册流程**:
```
访问租户域名 → 选择注册方式 → 填写基础信息 → 身份验证 → 设置密码 → 完善资料 → 绑定租户 → 建立推广关系
```

**具体需求**:
- **租户自动识别**: 基于访问域名自动识别用户归属租户
- **推广关系追踪**: 支持推广链接参数解析和推广关系建立
- **身份验证**: 手机短信验证、邮箱验证、微信授权验证
- **数据验证**: 用户名唯一性、邮箱格式、手机号格式验证
- **密码安全**: 密码强度检查、加密存储
- **实时反馈**: 注册过程中的实时验证和错误提示

#### 4.1.2 注册规则
**用户名规则**:
- 长度：3-20个字符
- 格式：字母、数字、下划线组合
- 唯一性：在同一租户内唯一

**密码规则**:
- 长度：8-32个字符
- 复杂度：包含大小写字母、数字、特殊字符中的至少3种
- 加密：使用bcrypt加密存储

**验证码规则**:
- 短信验证码：6位数字，5分钟有效
- 邮箱验证码：6位数字，10分钟有效
- 同一手机号/邮箱每分钟最多发送1次

### 4.2 用户认证与授权体系

#### 4.2.1 多渠道注册登录
**功能描述**: 提供全面的注册登录方式，满足不同用户需求

**注册方式支持**:
- **手机号注册**: 手机号+短信验证码+密码
- **邮箱注册**: 邮箱+邮件验证+密码
- **微信注册**: 微信OAuth2授权登录
- **第三方OAuth**: 支持QQ、微博、钉钉等第三方登录

**登录方式支持**:
- **密码登录**: 用户名/手机号/邮箱 + 密码
- **短信验证码**: 手机号 + 短信验证码
- **微信扫码**: 微信扫码登录
- **生物识别**: 指纹、面部识别（移动端）

**安全验证机制**:
- **图形验证码**: 防止机器人攻击
- **滑块验证**: 人机验证
- **二次验证(2FA)**: 短信、邮箱、TOTP等二次验证
- **风险控制**: 异常登录检测和拦截

**密码管理**:
- **密码强度检测**: 实时检测密码强度并给出建议
- **找回密码**: 多种方式找回密码（手机、邮箱、安全问题）
- **定期更换提醒**: 密码使用时间过长时提醒更换
- **密码历史**: 防止使用最近使用过的密码

#### 4.2.2 JWT令牌管理
**功能描述**: 基于JWT的无状态认证机制，支持微服务间的身份验证

**令牌对机制**:
- **AccessToken**: 短期访问令牌（2小时有效）
- **RefreshToken**: 长期刷新令牌（30天有效）
- **令牌轮换**: RefreshToken使用后自动更新

**自动续期机制**:
- **无感知刷新**: 前端自动检测令牌过期并刷新
- **预刷新策略**: 令牌过期前5分钟自动刷新
- **失败重试**: 刷新失败时的重试机制

**多端登录控制**:
- **同时多设备**: 支持用户同时在多个设备登录
- **单点登录**: 可配置为单点登录模式
- **设备管理**: 查看和管理已登录设备
- **强制下线**: 管理员可强制用户下线

**会话管理**:
- **在线状态**: 实时显示用户在线状态
- **强制下线**: 支持管理员强制用户下线
- **异地登录提醒**: 检测到异地登录时发送通知
- **会话超时**: 长时间无操作自动退出

### 4.5 密码管理功能

#### 4.3.1 找回密码
**功能描述**: 用户忘记密码时的找回机制

**找回方式**:
- **手机找回**: 手机号+短信验证码+新密码
- **邮箱找回**: 邮箱+邮件验证链接+新密码
- **安全问题**: 预设安全问题+答案验证+新密码

**安全机制**:
- **验证码保护**: 验证码5分钟有效，每分钟最多发送1次
- **链接保护**: 邮件链接30分钟有效，一次性使用
- **操作记录**: 记录所有密码找回操作，异常时通知用户

#### 4.3.2 修改密码
**功能描述**: 用户主动修改密码的功能

**修改方式**:
- **原密码验证**: 输入原密码+新密码+确认密码
- **短信验证**: 手机号+短信验证码+新密码
- **邮箱验证**: 邮箱+邮件验证+新密码

**安全要求**:
- **密码强度**: 新密码必须符合密码复杂度要求
- **历史密码**: 新密码不能与最近3次使用的密码相同
- **强制修改**: 支持管理员强制用户修改密码

### 4.4 用户资料管理

#### 4.4.1 基础信息管理
**功能描述**: 用户个人资料的完善和管理

**基础信息字段**:
- **个人信息**: 昵称、头像、性别、生日、地区
- **联系信息**: 手机号、邮箱、微信号、QQ号
- **身份信息**: 真实姓名、身份证号（实名认证）
- **职业信息**: 公司、职位、行业、工作经验

**管理功能**:
- **信息编辑**: 用户可自主编辑基础信息
- **头像上传**: 支持头像图片上传和裁剪
- **信息验证**: 手机号、邮箱变更需要验证
- **隐私设置**: 控制信息的可见性和公开程度

#### 4.4.2 偏好设置管理
**功能描述**: 用户个性化偏好和系统设置

**偏好设置分类**:
- **系统偏好**: 语言设置、时区设置、主题模式（明亮/暗黑）
- **通知偏好**: 邮件通知、短信通知、推送通知开关
- **隐私设置**: 资料可见性、搜索可见性、推荐设置
- **AI偏好**: 首选AI模型、对话风格、功能偏好

### 4.5 用户状态管理

#### 4.5.1 用户生命周期管理
**功能描述**: 管理用户从注册到注销的完整生命周期

**用户状态**:
- **正常状态**: 用户可正常使用所有功能
- **待激活**: 注册后未完成邮箱/手机验证
- **已冻结**: 临时冻结，禁止登录和操作
- **已封禁**: 永久封禁，禁止使用平台
- **已注销**: 用户主动注销或被注销

**状态转换**:
- **激活流程**: 待激活→正常状态的激活流程
- **冻结流程**: 正常状态→已冻结的冻结流程
- **解冻流程**: 已冻结→正常状态的解冻流程
- **封禁流程**: 任意状态→已封禁的封禁流程
- **注销流程**: 正常状态→已注销的注销流程

**状态管理**:
- **批量操作**: 支持批量修改用户状态
- **状态历史**: 记录用户状态变更历史
- **自动处理**: 基于规则的自动状态变更
- **通知机制**: 状态变更时的通知机制

#### 4.5.2 用户分组管理
**功能描述**: 基于不同维度的用户分组管理

**分组类型**:
- **部门分组**: 按部门组织架构分组
- **角色分组**: 按用户角色分组
- **地区分组**: 按地理位置分组
- **业务分组**: 按业务线分组
- **自定义分组**: 管理员自定义分组

**分组功能**:
- **动态分组**: 基于条件的动态分组
- **静态分组**: 手动维护的静态分组
- **分组权限**: 不同分组的权限配置
- **分组操作**: 批量操作分组内用户

### 4.6 用户通知与消息

#### 4.6.1 消息通知系统
**功能描述**: 全方位的消息通知体系

**通知类型**:
- **系统通知**: 系统维护、更新等通知
- **业务通知**: 订单、支付、审核等业务通知
- **营销通知**: 活动、优惠等营销信息
- **安全通知**: 登录异常、密码修改等安全提醒

**通知渠道**:
- **站内消息**: 系统内消息中心
- **邮件通知**: 邮箱推送通知
- **短信通知**: 手机短信通知
- **微信通知**: 微信公众号/小程序通知
- **APP推送**: 移动端推送通知

**通知管理**:
- **通知偏好**: 用户可设置接收偏好
- **通知历史**: 查看历史通知记录
- **批量操作**: 批量标记已读、删除
- **通知统计**: 通知发送和阅读统计

#### 4.6.2 消息中心
**功能描述**: 统一的消息管理中心

**消息分类**:
- **未读消息**: 未读消息列表和数量提醒
- **已读消息**: 已读消息历史记录
- **重要消息**: 标记为重要的消息
- **系统公告**: 平台系统公告

**消息功能**:
- **消息搜索**: 按关键词、时间搜索消息
- **消息过滤**: 按类型、状态过滤消息
- **消息导出**: 导出消息记录
- **消息设置**: 消息接收和显示设置

### 4.7 账户安全功能

#### 4.5.1 安全设置
**功能描述**: 用户账户安全相关的设置和管理

**安全功能**:
- **登录密码**: 修改登录密码
- **支付密码**: 设置和修改支付密码
- **手机绑定**: 绑定和更换手机号
- **邮箱绑定**: 绑定和更换邮箱
- **微信绑定**: 绑定和解绑微信账号
- **实名认证**: 身份证实名认证

**安全验证**:
- **双重验证**: 支持短信、邮箱双重验证
- **设备管理**: 查看和管理登录设备
- **登录记录**: 查看历史登录记录和异常登录
- **安全日志**: 查看账户安全相关的操作日志

#### 4.5.2 账户注销
**功能描述**: 用户主动注销账户的功能

**注销流程**:
1. **注销申请**: 用户提交账户注销申请
2. **身份验证**: 通过密码、短信等方式验证身份
3. **数据确认**: 确认账户数据和资产处理方式
4. **冷静期**: 7天冷静期，期间可撤销注销
5. **数据清理**: 清理用户数据，保留必要的法律记录
6. **注销完成**: 账户彻底注销，无法恢复

**注销限制**:
- 账户有未完成订单时不能注销
- 账户有余额时需要先提现或转移
- 推广员身份用户需要先处理推广关系












#### 4.1.1 功能概述
用户基础认证功能是AI生态平台用户系统的核心基础，提供完整的用户身份认证、账户安全管理和会话控制功能。通过多种注册登录方式、完善的密码管理机制和严格的安全控制，确保用户账户的安全性和便利性。

**核心定位**
- **身份验证**: 确保用户身份的真实性和唯一性
- **账户安全**: 提供全方位的账户安全保护机制
- **便捷体验**: 支持多种便捷的认证方式
- **会话管理**: 完善的登录会话管理和控制

#### 4.1.2 用户注册功能

##### 4.1.2.1 多种注册方式
**功能描述**: 提供多种便捷的用户注册方式，满足不同用户的注册需求

**注册方式支持**:
- **手机号注册**: 通过手机号+短信验证码完成注册
- **邮箱注册**: 通过邮箱+邮件验证链接完成注册
- **微信快速注册**: 通过微信OAuth授权快速注册
- **企业批量注册**: 支持企业用户批量导入注册
- **推广链接注册**: 通过推广链接注册并自动绑定推广关系

**注册流程设计**:
```
选择注册方式 → 填写基础信息 → 验证身份 → 创建账户 → 完善资料 → 推广关系绑定 → 初始权益分配 → 欢迎引导
```

##### 4.1.2.2 注册信息验证
**功能描述**: 对用户注册信息进行严格验证，确保信息的真实性和有效性

**验证机制**:
- **手机号验证**: 发送短信验证码，验证手机号真实性和可用性
- **邮箱验证**: 发送验证邮件，验证邮箱真实性和可用性
- **身份唯一性**: 检查手机号、邮箱在平台内的唯一性
- **密码强度**: 验证密码复杂度，确保账户安全
- **防机器注册**: 图形验证码、行为验证等防止机器批量注册

**验证规则**:
- **手机号格式**: 验证手机号格式的正确性
- **邮箱格式**: 验证邮箱地址格式的正确性
- **密码强度**: 8位以上，包含字母、数字、特殊字符
- **用户名规则**: 3-20位字符，支持中英文、数字、下划线
- **重复检查**: 防止重复注册，一个手机号/邮箱只能注册一个账户

##### ******* 注册安全策略
**功能描述**: 实施多层次的注册安全策略，防止恶意注册和安全风险

**安全策略**:
- **IP频率限制**: 同一IP地址每小时最多注册5个账户
- **设备限制**: 同一设备每天最多注册3个账户
- **验证码防刷**: 短信/邮件验证码5分钟有效期，每日最多10次
- **黑名单机制**: 维护恶意IP、手机号、邮箱黑名单
- **风险评估**: 注册时进行风险评估，高风险注册需要人工审核

#### 4.1.3 用户登录功能

##### ******* 多种登录方式
**功能描述**: 提供多种便捷的登录方式，提升用户登录体验

**登录方式支持**:
- **用户名+密码登录**: 支持用户名、邮箱、手机号作为登录标识
- **手机号+验证码登录**: 免密码快速登录方式
- **邮箱+密码登录**: 邮箱作为登录标识的密码登录
- **微信扫码登录**: 微信OAuth授权快速登录
- **记住登录状态**: 支持7天、30天免登录选项

**登录流程优化**:
- **智能识别**: 自动识别登录标识类型（用户名/邮箱/手机号）
- **登录记忆**: 记住用户最后使用的登录方式
- **快速切换**: 支持在不同登录方式间快速切换
- **登录引导**: 为新用户提供登录方式选择引导

##### ******* 登录安全机制
**功能描述**: 实施严格的登录安全机制，保护用户账户安全

**安全机制**:
- **登录失败锁定**: 连续5次登录失败锁定账户30分钟
- **异地登录提醒**: 检测异地登录并发送邮件/短信提醒
- **设备管理**: 支持信任设备管理，新设备登录需要验证
- **会话超时**: 24小时无操作自动退出登录
- **多设备支持**: 支持多设备同时登录，每设备独立会话管理

**异常登录检测**:
- **地理位置检测**: 检测异常地理位置登录
- **设备指纹检测**: 检测新设备或异常设备登录
- **登录时间检测**: 检测异常时间段登录行为
- **登录频率检测**: 检测短时间内频繁登录尝试
- **行为模式检测**: 检测异常的登录行为模式

##### 4.1.3.3 会话管理功能
**功能描述**: 提供完善的用户会话管理和控制功能

**会话管理特性**:
- **会话创建**: 登录成功后创建用户会话，生成会话令牌
- **会话维持**: 通过令牌刷新机制维持用户登录状态
- **会话超时**: 设置会话超时时间，超时自动退出
- **会话销毁**: 用户主动退出或强制退出时销毁会话
- **多会话管理**: 支持用户在多个设备上的会话管理

**令牌管理**:
- **访问令牌**: 短期有效的访问令牌（30分钟）
- **刷新令牌**: 长期有效的刷新令牌（7天）
- **令牌刷新**: 自动刷新访问令牌，保持登录状态
- **令牌撤销**: 支持主动撤销令牌，强制退出登录
- **令牌安全**: 令牌加密存储，防止令牌泄露

#### 4.1.4 密码管理功能

##### 4.1.4.1 密码找回和重置
**功能描述**: 提供安全便捷的密码找回和重置功能

**密码找回方式**:
- **手机验证码找回**: 通过注册手机号接收验证码重置密码
- **邮箱链接找回**: 通过注册邮箱接收重置链接重置密码
- **安全问题找回**: 通过预设安全问题验证身份后重置密码
- **人工客服找回**: 通过客服人工验证身份后协助重置密码

**重置流程**:
1. **身份验证**: 验证用户身份（手机号/邮箱/安全问题）
2. **验证码发送**: 发送验证码或重置链接
3. **验证码验证**: 验证用户输入的验证码
4. **密码重置**: 用户设置新密码
5. **重置确认**: 确认密码重置成功，发送通知

##### 4.1.4.2 密码修改功能
**功能描述**: 用户主动修改密码的安全功能

**修改流程**:
- **旧密码验证**: 验证用户当前密码的正确性
- **新密码设置**: 用户设置新密码并确认
- **密码强度检测**: 检测新密码的强度和安全性
- **修改确认**: 确认密码修改，更新密码信息
- **通知发送**: 发送密码修改成功通知

**安全策略**:
- **密码历史**: 不允许使用最近3次使用过的密码
- **修改频率**: 限制密码修改频率，防止频繁修改
- **强制修改**: 定期提醒用户修改密码，增强安全性
- **修改记录**: 记录密码修改历史，便于安全审计

##### 4.1.4.3 密码安全策略
**功能描述**: 实施全面的密码安全策略，保护用户账户安全

**密码强度要求**:
- **长度要求**: 密码长度至少8位字符
- **复杂度要求**: 包含大小写字母、数字、特殊字符
- **常见密码检测**: 禁止使用常见弱密码
- **个人信息检测**: 禁止使用包含个人信息的密码

**密码保护机制**:
- **加密存储**: 密码使用强加密算法存储
- **盐值加密**: 使用随机盐值增强密码安全性
- **传输加密**: 密码传输过程使用HTTPS加密
- **定期检测**: 定期检测密码安全性，提醒用户更新

#### 4.1.5 账户安全设置

##### 4.1.5.1 安全设置管理
**功能描述**: 提供全面的账户安全设置和管理功能

**安全设置项**:
- **登录密码**: 设置和修改登录密码
- **支付密码**: 设置独立的支付密码（用于资金操作）
- **二次验证**: 开启手机/邮箱二次验证
- **登录通知**: 设置登录通知方式和频率
- **设备管理**: 管理信任设备和登录设备

**安全等级设置**:
- **基础安全**: 密码登录+基础验证
- **中级安全**: 密码登录+二次验证
- **高级安全**: 密码登录+二次验证+设备绑定
- **企业安全**: 高级安全+管理员审批

##### 4.1.5.2 设备管理功能
**功能描述**: 管理用户的登录设备，提供设备安全控制

**设备管理功能**:
- **设备列表**: 显示所有登录过的设备信息
- **设备详情**: 设备类型、操作系统、浏览器、最后登录时间
- **信任设备**: 设置信任设备，信任设备登录无需额外验证
- **设备删除**: 删除不再使用的设备记录
- **远程登出**: 远程登出指定设备上的会话

**设备安全策略**:
- **新设备验证**: 新设备登录需要额外身份验证
- **设备数量限制**: 限制同时登录的设备数量
- **异常设备检测**: 检测异常设备登录行为
- **设备指纹**: 使用设备指纹技术识别设备

##### 4.1.5.3 安全日志和通知
**功能描述**: 记录安全相关日志并及时通知用户

**安全日志记录**:
- **登录日志**: 记录所有登录尝试和结果
- **操作日志**: 记录重要的安全操作
- **异常日志**: 记录异常的安全事件
- **设备日志**: 记录设备相关的安全事件

**安全通知机制**:
- **实时通知**: 重要安全事件实时通知
- **邮件通知**: 通过邮件发送安全通知
- **短信通知**: 通过短信发送紧急安全通知
- **站内通知**: 在平台内显示安全通知消息




### 4.2 用户资产管理功能

#### 4.2.1 功能概述
用户资产管理功能是AI生态平台用户财务管理的核心模块，提供用户充值、余额管理、积分管理、已购买产品管理、使用记录管理、收藏功能等全方位的资产管理服务。通过统一的资产管理体系，确保用户资产的安全性、透明性和便利性。

**核心定位**
- **资产安全**: 确保用户各类资产的安全存储和管理
- **透明管理**: 提供清晰透明的资产变动记录和统计
- **便捷操作**: 支持便捷的充值、消费、提现等操作
- **统一管理**: 统一管理用户的各类数字资产和购买记录

#### 4.2.2 用户充值功能

##### 4.2.2.1 多种充值方式
**功能描述**: 提供多种便捷的充值方式，满足用户不同的充值需求

**充值方式支持**:
- **支付宝充值**: 通过支付宝扫码或跳转支付
- **微信支付充值**: 通过微信扫码或跳转支付
- **银行卡充值**: 通过银行卡快捷支付或网银支付
- **企业转账**: 支持企业用户通过银行转账充值
- **充值卡充值**: 支持充值卡兑换充值

**充值流程**:
1. **选择充值方式**: 用户选择合适的充值方式
2. **输入充值金额**: 输入或选择充值金额
3. **确认充值信息**: 确认充值金额和支付方式
4. **跳转支付页面**: 跳转到第三方支付页面
5. **完成支付**: 用户完成支付操作
6. **支付结果回调**: 接收支付结果通知
7. **余额更新**: 充值成功后自动更新用户余额
8. **充值通知**: 发送充值成功通知给用户

##### 4.2.2.2 充值记录管理
**功能描述**: 详细记录和管理用户的充值历史记录

**充值记录信息**:
- **基础信息**: 充值时间、充值金额、充值方式、订单号
- **支付信息**: 支付平台、支付账户、支付时间、交易流水号
- **状态信息**: 充值状态、处理时间、完成时间
- **优惠信息**: 使用的优惠券、赠送金额、实际到账金额

#### 4.3 账户余额管理

##### 4.3.1 余额账户体系
**功能描述**: 建立完善的用户余额账户体系，支持多种余额类型管理

**余额类型**:
- **可用余额**: 可以直接用于消费的余额
- **冻结余额**: 因为某些原因被冻结的余额
- **赠送余额**: 通过活动获得的赠送余额
- **退款余额**: 退款返还的余额

**余额使用规则**:
- **使用优先级**: 赠送余额 > 退款余额 > 可用余额
- **有效期管理**: 赠送余额可设置有效期
- **转换规则**: 冻结余额解冻后转为可用余额
- **提现限制**: 赠送余额不能提现，只能消费

##### 4.3.2 余额变动记录
**功能描述**: 详细记录用户余额的所有变动情况

**变动类型**:
- **充值入账**: 充值成功后的余额增加
- **消费扣款**: 购买产品或服务的余额扣除
- **退款入账**: 退款返还的余额增加
- **赠送入账**: 活动赠送的余额增加
- **提现扣款**: 提现申请的余额扣除
- **冻结操作**: 余额冻结和解冻操作

### 4.4 已购买产品管理

#### 4.4.1 已购买课程管理
**功能描述**: 管理用户已购买的课程和学习记录

**课程信息管理**:
- **课程列表**: 显示用户已购买的所有课程
- **课程详情**: 课程名称、购买时间、有效期、学习进度
- **学习状态**: 未开始、学习中、已完成、已过期
- **课程分类**: 按课程类型、难度等级分类管理

**学习记录管理**:
- **学习进度**: 记录每个课程的学习进度百分比
- **学习时长**: 记录总学习时长和每次学习时长
- **章节记录**: 记录每个章节的学习状态和完成时间
- **考试记录**: 记录课程考试成绩和通过情况
- **证书管理**: 管理课程完成证书的获得和下载

#### 4.4.2 已购买智能体管理
**功能描述**: 管理用户已购买的智能体和使用记录

**智能体信息管理**:
- **智能体列表**: 显示用户已购买的所有智能体
- **购买详情**: 智能体名称、购买时间、购买类型、有效期
- **使用权限**: 使用次数限制、功能权限、访问权限
- **智能体状态**: 正常使用、已过期、已禁用

**使用记录管理**:
- **对话记录**: 记录与智能体的对话历史
- **使用统计**: 统计使用次数、使用时长、使用频率
- **功能使用**: 记录使用的具体功能和操作
- **效果评价**: 用户对智能体使用效果的评价和反馈

#### 4.4.3 已购买AI插件管理
**功能描述**: 管理用户已购买的AI插件和使用记录

**插件信息管理**:
- **插件列表**: 显示用户已购买的所有AI插件
- **购买详情**: 插件名称、购买时间、版本信息、授权类型
- **使用权限**: 使用次数、功能限制、API调用限制
- **插件状态**: 激活状态、过期时间、更新状态

### 4.5 使用历史记录管理

#### 4.5.1 各业务模块使用记录
**功能描述**: 统一管理用户在各个业务模块的使用历史记录

**业务模块记录**:
- **智能体使用**: 智能体对话记录、使用时长、效果评价
- **大模型调用**: API调用记录、Token使用量、调用结果
- **AI工具使用**: 工具使用记录、处理结果、文件生成
- **课程学习**: 学习记录、进度跟踪、成绩记录
- **商城购买**: 购买记录、支付记录、物流信息

### 4.6 用户收藏功能

#### 4.6.1 智能体收藏
**功能描述**: 支持用户收藏喜欢的智能体，便于快速访问

**收藏功能**:
- **收藏操作**: 一键收藏/取消收藏智能体
- **收藏列表**: 显示用户收藏的所有智能体
- **收藏分类**: 支持自定义分类管理收藏的智能体
- **收藏排序**: 支持按收藏时间、使用频率等排序

#### 4.6.2 课程收藏
**功能描述**: 支持用户收藏感兴趣的课程，便于后续购买和学习

**收藏功能**:
- **课程收藏**: 收藏感兴趣但暂未购买的课程
- **收藏提醒**: 收藏课程有优惠活动时自动提醒
- **收藏推荐**: 基于收藏偏好推荐相关课程
- **收藏清单**: 管理课程收藏清单和购买计划
































——————————————————————————————————————————————————————————————



**角色定位**: 用户的一种身份标识，专门负责推广获客并获得佣金

**用户ID**: 租户ID+递增数字（如：D6SS211001）
**身份特点**: 与普通用户共享同一个用户ID，只是身份标识不同
**管理范围**: 个人推广业务和下级推广员（二级推广员）

**核心功能**:
- **推广链接管理**: 生成和管理个人推广链接
- **邀请码管理**: 生成和分发邀请码
- **推广数据**: 查看推广效果和转化数据
- **佣金管理**: 查看推广佣金和提现记录
- **素材管理**: 推广素材下载和使用
- **团队管理**: 管理下级推广员（仅限二级推广员）

**推广链接功能**:
- **链接生成**: 生成包含租户ID和用户ID的推广链接
- **链接格式**: `https://{domain}?tenant_id={tenant_id}&user_id={user_id}`
- **归属处理**: 通过推广员链接注册的用户归属于该推广员
- **佣金权限**: 有推广佣金权限，根据推广业绩获得佣金

**分级管理**:
- **一级推广员**: 直接推广用户，获得一级佣金
- **二级推广员**: 管理下级推广员，获得二级佣金

全平台推广链接机制

### 3.1 推广链接设计原理

#### 3.1.1 推广链接机制
**功能描述**: 推广员和普通用户可以生成和分享推广链接

**设计原理**:
- **用户可分享**: 推广员和普通用户都可以生成推广链接
- **权限分离**: 推广链接分享权限与推广佣金权限分离
- **归属追踪**: 通过用户ID追踪推广关系和用户归属
- **佣金控制**: 只有具备推广员身份的用户才能获得推广佣金

#### 3.1.2 推广链接格式设计
**标准格式**: `https://{domain}?tenant_id={tenant_id}&user_id={user_id}`

**参数说明**:
- `domain`: SAAS代理商的域名（可以是自定义域名或默认域名）
- `tenant_id`: 6位租户标识，确定用户注册到哪个租户
- `user_id`: 租户ID+递增数字的用户标识，确定推广归属

**链接示例**:
```
# 使用默认域名的推广链接
https://demo.cees.cc?tenant_id=D6SS21&user_id=D6SS211001

# 使用自定义域名的推广链接
https://myai.com?tenant_id=D6SS21&user_id=D6SS211001
https://aiservice.net?tenant_id=D6SS21&user_id=D6SS211001

# 推广员推广链接（有佣金权限）
https://demo.cees.cc?tenant_id=D6SS21&user_id=D6SS211002

# 普通用户推广链接（无佣金权限）
https://demo.cees.cc?tenant_id=A8BC45&user_id=A8BC451001
```

### 3.2 推广归属机制

#### 3.2.1 用户注册归属处理
**功能描述**: 通过推广链接注册的用户自动建立归属关系

**归属处理流程**:
1. **链接解析**: 解析推广链接中的租户ID和用户ID
2. **推广者验证**: 验证推广者的用户ID是否有效
3. **租户验证**: 验证推广者是否属于目标租户
4. **归属建立**: 在数据库中建立推广关系记录
5. **身份检查**: 检查推广者是否具备推广员身份
6. **佣金标记**: 标记该推广关系是否可获得佣金

#### 3.2.2 佣金权限判断机制
**功能描述**: 根据推广者身份决定是否可获得佣金

**佣金权限规则**:
- **SAAS代理商**: 无佣金权限，通过租户运营获得收益
- **推广员身份用户**: 有佣金权限，根据推广业绩获得佣金
- **普通用户**: 无佣金权限，但推广关系保留，升级为推广员后可获得佣金

**佣金计算示例**:
```
场景1：推广员身份用户A推广用户B购买产品
- 推广者：用户A (拥有推广员身份)
- 订单金额：1000元
- 佣金比例：20%
- 佣金金额：200元
- 结果：用户A获得200元佣金

场景2：普通用户C推广用户D购买产品
- 推广者：用户C (无推广员身份)
- 订单金额：1000元
- 佣金比例：20%
- 佣金金额：0元
- 结果：用户C不获得佣金，但推广关系保留

场景3：普通用户C升级为推广员后，用户D再次购买
- 推广者：用户C (已获得推广员身份)
- 订单金额：500元
- 佣金比例：20%
- 佣金金额：100元
- 结果：用户C获得100元佣金
```

### 3.3 推广链接生成功能

#### 3.3.1 推广链接生成规则
**功能描述**: 推广员和普通用户生成推广链接的具体功能实现

**推广员推广链接生成**:
- **生成位置**: 推广员管理后台
- **生成权限**: 拥有推广员身份的用户
- **链接用途**: 个人推广，用户归属推广员，可获得佣金
- **生成规则**: 使用自己的用户ID和所属租户ID生成链接
- **域名选择**: 可选择租户的任意已配置域名

**普通用户推广链接生成**:
- **生成位置**: 用户个人中心
- **生成权限**: 所有普通用户
- **链接用途**: 个人分享，用户归属普通用户，无佣金
- **生成规则**: 使用自己的用户ID和所属租户ID生成链接
- **域名选择**: 可选择租户的任意已配置域名

**SAAS代理商推广方式**:
- **不能直接生成**: SAAS代理商不能直接生成推广链接
- **间接推广**: 用绑定域名注册新用户，设定为推广员身份
- **推广员管理**: 通过管理推广员实现推广业务

#### 3.3.2 推广链接管理功能
**功能描述**: 推广链接的管理、统计和分析功能

**链接管理功能**:
- **链接生成**: 一键生成个人专属推广链接
- **域名选择**: 选择租户已配置的域名生成链接
- **链接复制**: 快速复制推广链接到剪贴板
- **链接分享**: 支持分享到微信、QQ、微博等社交平台
- **链接统计**: 查看链接点击量、注册转化率等数据
- **链接历史**: 查看历史生成的推广链接记录

**推广数据统计**:
- **点击统计**: 推广链接的点击次数和来源分析
- **注册统计**: 通过推广链接注册的用户数量
- **转化统计**: 注册用户的付费转化率和金额
- **佣金统计**: 推广获得的佣金金额和明细（仅推广员身份用户）
- **时间分析**: 按日、周、月的推广数据趋势分析

#### 3.3.3 域名配置管理
**功能描述**: SAAS代理商的域名配置和管理

**域名配置功能**:
- **默认域名**: 系统分配的默认域名（如：demo.cees.cc）
- **自定义域名**: 代理商可配置多个自定义域名
- **域名验证**: 验证域名所有权和DNS配置
- **SSL证书**: 自动配置SSL证书确保HTTPS访问
- **域名状态**: 监控域名状态和可用性

**域名使用规则**:
- 推广链接可使用任意已配置的域名
- 不同域名的推广效果可单独统计
- 域名变更不影响已有推广关系
- 支持域名的启用和禁用管理






## 五、用户与推广的核心功能

**角色定位**: AI服务使用者，平台的最终用户

**用户ID**: 租户ID+递增数字（如：D6SS211002）
**身份特点**: 可以同时拥有推广员身份，共享同一个用户ID
**管理范围**: 个人账户和使用的服务

**核心功能**:
- **服务使用**: 使用智能体、大模型API、AI工具箱等服务
- **资料管理**: 管理个人资料和偏好设置
- **订单管理**: 查看购买记录和使用记录
- **资产管理**: 余额、积分等资产查询和操作
- **推广参与**: 可申请成为推广员
- **客服支持**: 获得客服支持和反馈渠道

**推广链接功能**:
- **链接生成**: 可生成推广链接（无佣金权限）
- **链接格式**: `https://{domain}?tenant_id={tenant_id}&user_id={user_id}`
- **归属处理**: 通过普通用户链接注册的用户归属于该普通用户
- **佣金权限**: 无推广佣金权限，但推广关系保留

**身份升级**:
- 可申请成为推广员，获得推广佣金权限
- 用户ID保持不变，只是增加推广员身份标识
- 升级后原有的推广关系开始产生佣金


用户推广员功能

### 5.1 功能概述
用户推广员功能是AI生态平台用户裂变获客的核心模块，支持用户申请成为推广员、管理推广业务、获得推广佣金等功能。通过完善的推广员体系，实现用户自主推广、降低获客成本、提升平台收入的目标。注意：用户可以同时拥有普通用户和推广员两种身份。

**核心定位**
- **身份叠加**: 用户可同时拥有普通用户和推广员身份
- **推广获客**: 通过用户推广实现平台用户增长
- **佣金激励**: 通过佣金机制激励用户积极推广
- **数据透明**: 提供完整的推广数据统计和分析

### 5.2 推广员申请功能

#### 5.2.1 推广员申请流程
**功能描述**: 用户申请成为推广员的完整流程管理

**申请条件**:
- **基础条件**: 账户状态正常、已完成实名认证
- **活跃度要求**: 注册时间超过7天、最近30天有登录记录
- **信用要求**: 无违规记录、无恶意行为记录
- **平台了解**: 对平台功能有一定了解和使用经验

**申请流程**:
1. **提交申请**: 用户填写推广员申请表
2. **资料审核**: 系统和人工审核用户资料
3. **条件验证**: 验证用户是否满足申请条件
4. **面试环节**: 可选的在线面试或测试环节
5. **审核决定**: 审核通过或拒绝，并说明原因
6. **权限开通**: 审核通过后开通推广员权限
7. **培训引导**: 提供推广员培训和操作指导

#### 5.2.2 申请信息管理
**功能描述**: 管理推广员申请过程中的各类信息

**申请信息收集**:
- **基础信息**: 真实姓名、联系方式、身份证信息
- **推广经验**: 以往推广经验、推广渠道、推广能力
- **推广计划**: 推广目标、推广策略、预期效果
- **个人优势**: 个人特长、资源优势、推广优势
- **申请原因**: 申请推广员的动机和目标

**申请状态管理**:
- **申请中**: 用户已提交申请，等待审核
- **审核中**: 正在进行人工审核
- **补充材料**: 需要用户补充申请材料
- **审核通过**: 申请通过，等待权限开通
- **审核拒绝**: 申请被拒绝，说明拒绝原因
- **权限开通**: 推广员权限已开通

#### 5.2.3 审核管理功能
**功能描述**: 租户管理员对推广员申请进行审核管理

**审核流程**:
- **初审**: 系统自动进行基础条件检查
- **人工审核**: 人工审核申请材料和用户资质
- **背景调查**: 可选的用户背景和信用调查
- **面试评估**: 可选的在线面试或能力测试
- **最终决定**: 综合评估后做出审核决定

**审核标准**:
- **基础资质**: 实名认证、账户状态、活跃度
- **推广能力**: 推广经验、推广资源、推广计划
- **信用记录**: 平台使用记录、违规记录、投诉记录
- **沟通能力**: 表达能力、理解能力、学习能力

### 5.3 推广员佣金管理

#### 5.3.1 佣金计算体系
**功能描述**: 完善的推广佣金计算和分配体系

**佣金类型**:
- **直接推广佣金**: 直接推广用户注册的佣金
- **消费分成佣金**: 推广用户消费金额的分成佣金
- **层级推广佣金**: 多层级推广关系的佣金分配
- **特殊奖励佣金**: 达成特定目标的额外奖励佣金

**佣金计算规则**:
- **推广注册**: 每成功推广一个用户注册获得固定佣金
- **消费分成**: 推广用户消费金额的一定比例作为佣金
- **层级分成**: 多层级推广关系的佣金分配比例
- **奖励机制**: 达成推广目标的额外奖励佣金

#### 5.3.2 佣金记录管理
**功能描述**: 详细记录和管理推广员的佣金获得情况

**佣金记录信息**:
- **基础信息**: 佣金获得时间、佣金类型、佣金金额、佣金来源
- **推广信息**: 推广用户信息、推广渠道、推广活动
- **计算信息**: 佣金计算基数、佣金比例、计算公式
- **状态信息**: 佣金状态（待结算、已结算、已提现）

**佣金统计分析**:
- **收入统计**: 总佣金收入、月度收入、日均收入
- **来源分析**: 不同佣金类型的收入占比分析
- **趋势分析**: 佣金收入的时间趋势分析
- **效率分析**: 推广效率和佣金转化率分析

#### 5.3.3 佣金提现功能
**功能描述**: 推广员佣金提现申请和处理功能

**提现条件**:
- **最小提现**: 设置最小提现金额（如100元）
- **实名认证**: 必须完成实名认证
- **银行卡绑定**: 必须绑定有效的银行卡或支付账户
- **提现频率**: 设置提现频率限制（如每月最多2次）

**提现流程**:
1. **提现申请**: 推广员提交佣金提现申请
2. **信息验证**: 验证推广员身份和银行卡信息
3. **佣金核算**: 核算可提现佣金金额
4. **审核处理**: 人工或自动审核提现申请
5. **资金划转**: 将佣金转账到推广员账户
6. **提现完成**: 更新提现状态，发送通知

### 5.4 推广数据统计

#### 5.4.1 推广效果统计
**功能描述**: 统计和分析推广员的推广效果和业绩

**推广数据指标**:
- **推广用户数**: 成功推广的用户总数
- **注册转化率**: 推广链接点击到注册的转化率
- **活跃用户数**: 推广用户中的活跃用户数量
- **付费用户数**: 推广用户中的付费用户数量
- **用户价值**: 推广用户的平均消费金额

**时间维度统计**:
- **日统计**: 每日推广数据统计
- **周统计**: 每周推广数据汇总
- **月统计**: 每月推广业绩统计
- **年统计**: 年度推广业绩总结

#### 5.4.2 推广渠道分析
**功能描述**: 分析不同推广渠道的效果和表现

**渠道分类**:
- **社交媒体**: 微信、QQ、微博等社交平台推广
- **内容平台**: 知乎、小红书、抖音等内容平台推广
- **线下推广**: 线下活动、会议、培训等推广
- **其他渠道**: 邮件、短信、电话等其他推广方式

**渠道效果分析**:
- **渠道转化率**: 不同渠道的用户转化率对比
- **渠道质量**: 不同渠道推广用户的质量分析
- **渠道成本**: 不同渠道的推广成本效益分析
- **渠道优化**: 基于数据的渠道优化建议

#### 5.4.3 推广趋势分析
**功能描述**: 分析推广数据的趋势变化和规律

**趋势分析维度**:
- **推广增长趋势**: 推广用户数量的增长趋势
- **质量变化趋势**: 推广用户质量的变化趋势
- **收入增长趋势**: 推广佣金收入的增长趋势
- **效率变化趋势**: 推广效率的变化趋势

### 5.5 推广用户管理

#### 5.5.1 旗下推广用户列表
**功能描述**: 管理推广员旗下的所有推广用户

**用户列表信息**:
- **基础信息**: 用户昵称、注册时间、推广渠道、用户状态
- **活跃信息**: 最后登录时间、活跃度、使用频率
- **消费信息**: 消费总额、消费次数、平均消费
- **推广层级**: 直接推广、间接推广、推广层级关系

**用户管理功能**:
- **用户筛选**: 按注册时间、活跃度、消费金额等筛选
- **用户搜索**: 按用户昵称、手机号等关键词搜索
- **用户分组**: 将推广用户进行分组管理
- **用户标签**: 为推广用户添加自定义标签

#### 5.5.2 推广用户跟踪
**功能描述**: 跟踪推广用户的使用情况和价值贡献

**跟踪指标**:
- **注册转化**: 从点击推广链接到完成注册的转化过程
- **激活转化**: 从注册到首次使用平台功能的转化
- **付费转化**: 从注册到首次付费的转化过程
- **留存情况**: 推广用户的留存率和活跃度变化

**价值评估**:
- **用户生命周期价值**: 推广用户的预期生命周期价值
- **推广投资回报**: 推广成本与用户价值的投资回报率
- **用户质量评分**: 基于多维度指标的用户质量评分
- **贡献度排名**: 推广用户对推广员收入的贡献度排名

### 5.6 推广链接管理

#### 5.6.1 推广链接生成
**功能描述**: 生成个性化的推广链接，支持多种推广场景

**链接类型**:
- **通用推广链接**: 指向平台首页的通用推广链接
- **产品推广链接**: 指向特定产品或服务的推广链接
- **活动推广链接**: 指向特定活动页面的推广链接
- **定制推广链接**: 根据推广需求定制的专属链接

**链接参数**:
- **推广员标识**: 自动包含推广员ID和推广码
- **推广渠道**: 标识推广链接的来源渠道
- **推广活动**: 标识推广链接所属的活动
- **自定义参数**: 支持添加自定义的跟踪参数

#### 5.6.2 推广素材管理
**功能描述**: 提供丰富的推广素材，帮助推广员进行有效推广

**素材类型**:
- **图片素材**: 产品宣传图、活动海报、推广横幅等
- **文案素材**: 推广文案、产品介绍、用户评价等
- **视频素材**: 产品演示视频、用户案例视频等
- **H5页面**: 互动式推广页面、产品展示页面等

**素材管理功能**:
- **素材分类**: 按产品、活动、渠道等分类管理素材
- **素材搜索**: 按关键词搜索相关推广素材
- **素材下载**: 支持批量下载推广素材
- **素材定制**: 支持个性化定制推广素材

#### 5.6.3 推广活动管理
**功能描述**: 参与和管理各类推广活动

**活动类型**:
- **注册奖励活动**: 推广用户注册获得奖励的活动
- **消费返利活动**: 推广用户消费获得返利的活动
- **限时推广活动**: 限定时间内的特殊推广活动
- **竞赛推广活动**: 推广员之间的推广竞赛活动

**活动参与**:
- **活动报名**: 报名参与推广活动
- **活动规则**: 查看活动规则和奖励机制
- **活动进度**: 跟踪活动参与进度和排名
- **活动奖励**: 查看和领取活动奖励

**业务流程说明**:
```
用户申请推广员 → 资料审核 → 权限开通 → 推广培训 → 生成推广链接 → 开始推广 → 用户注册 → 推广关系绑定 → 佣金计算 → 佣金发放 → 数据统计分析
```

**功能边界**:
- ✅ 推广员申请和审核流程
- ✅ 推广佣金计算和管理
- ✅ 推广数据统计和分析
- ✅ 推广用户管理和跟踪
- ✅ 推广链接生成和管理
- ✅ 推广素材和活动管理
- ❌ 复杂的多层级分销体系（属于营销功能模块）
- ❌ 高级推广策略和算法（属于营销功能模块）







### 5.3 推广关系建立规则

#### 5.3.1 推广关系建立条件
**必要条件**:
- 通过有效的推广链接访问注册页面
- 推广链接包含有效的租户ID和用户ID
- 推广者和被推广者属于同一租户
- 被推广者之前没有建立过推广关系

**推广关系建立流程**:
1. **链接解析**: 解析推广链接中的tenant_id和user_id参数
2. **推广者验证**: 验证推广者的用户ID是否存在
3. **租户匹配**: 验证推广者是否属于目标租户
4. **重复检查**: 检查被推广者是否已有推广关系
5. **关系建立**: 在数据库中创建推广关系记录
6. **身份标记**: 标记推广者是否具备推广员身份

#### 5.3.2 推广关系数据结构
**推广关系记录**:
```json
{
  "relation_id": "REL_20250115_001",
  "tenant_id": "D6SS21",
  "promoter_user_id": "D6SS211001",
  "customer_user_id": "D6SS211002",
  "promoter_is_promoter": true,
  "promoter_name": "推广员张三",
  "customer_name": "用户李四",
  "source_domain": "demo.cees.cc",
  "source_url": "https://demo.cees.cc?tenant_id=D6SS21&user_id=D6SS211001",
  "can_earn_commission": true,
  "created_at": "2025-01-15T10:30:00Z"
}
```

### 5.4 佣金计算规则

#### 5.4.1 佣金权限判断规则
**佣金权限矩阵**:
```
推广者身份      | 佣金权限 | 收益方式
SAAS代理商     | 无      | 租户运营收益
推广员身份用户  | 有      | 推广佣金
普通用户       | 无      | 升级后可获得佣金
```

#### 5.4.2 佣金计算公式
**一级推广佣金**:
```
一级佣金 = 订单实际支付金额 × 一级佣金比例
```

**二级推广佣金**:
```
二级佣金 = 订单实际支付金额 × 二级佣金比例
```

**佣金计算示例**:
```
订单场景：用户购买1000元会员卡
一级佣金比例：20%
二级佣金比例：10%

计算结果：
- 一级推广员获得：1000 × 20% = 200元
- 二级推广员获得：1000 × 10% = 100元
- 平台净收入：1000 - 200 - 100 = 700元
```

#### 5.4.3 佣金结算规则
**结算条件**:
- 订单状态为"已完成"
- 无退款申请或退款期已过
- 推广员身份状态为"正常"
- 推广关系有效

**结算流程**:
1. **订单完成**: 用户订单状态变为已完成
2. **佣金计算**: 系统自动计算应得佣金
3. **佣金入账**: 佣金进入推广员待结算余额
4. **结算申请**: 推广员申请佣金提现
5. **审核处理**: 代理商审核提现申请
6. **资金到账**: 佣金转入推广员指定账户

### 5.5 身份升级规则

#### 5.5.1 普通用户获得推广员身份
**升级条件**:
- 账户状态正常，无违规记录
- 完成实名认证
- 提交推广员申请并通过审核
- 接受推广员协议和规则

**升级流程**:
1. **申请提交**: 用户提交推广员申请
2. **资料审核**: 代理商审核申请资料
3. **能力评估**: 评估用户的推广能力
4. **协议签署**: 签署推广员协议
5. **身份开通**: 开通推广员身份
6. **培训指导**: 提供推广员培训

**升级后变化**:
- 用户ID保持不变
- 增加推广员身份标识
- 获得推广佣金权限
- 历史推广关系开始产生佣金