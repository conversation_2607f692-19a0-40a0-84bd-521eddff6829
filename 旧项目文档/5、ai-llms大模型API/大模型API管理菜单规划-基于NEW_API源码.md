# AI生态平台大模型API管理菜单规划 (基于NEW_API源码)

**文档版本**: v2.0  
**创建时间**: 2025-07-18  
**负责人**: AI开发团队  
**数据来源**: NEW_API源码完整分析  
**目标**: 完全基于NEW_API真实源码结构，规划AI生态平台大模型API管理菜单  

---

## 📋 NEW_API源码真实结构分析

### SAAS管理端需要的页面 (精简后)
```
/web/src/pages/
├── Channel/               # 渠道管理 ⭐
│   ├── index.js          # 渠道列表页面 (使用ChannelsTable组件)
│   ├── EditChannel.js    # 编辑渠道表单
│   └── EditTagModal.js   # 渠道标签编辑
├── Detail/               # 数据看板 ⭐
│   └── index.js          # 数据统计仪表盘
├── Log/                  # 使用日志 ⭐
│   └── index.js          # API调用日志 (使用LogsTable组件)
├── Midjourney/           # 绘图日志 ⭐
│   └── index.js          # AI绘图日志 (使用MjLogsTable组件)
├── Redemption/           # 兑换码管理 ⭐
│   ├── index.js          # 兑换码列表 (使用RedemptionsTable组件)
│   └── EditRedemption.js # 编辑兑换码
├── Setting/              # 系统设置 ⭐
│   ├── index.js          # 设置主页面 (Tab切换)
│   ├── Dashboard/        # 仪表盘设置
│   ├── Drawing/          # 绘图设置
│   ├── Model/            # 模型设置
│   ├── Operation/        # 运营设置
│   ├── RateLimit/        # 速率限制设置
│   └── Ratio/            # 倍率设置
├── Task/                # 任务日志 ⭐
│   └── index.js          # 异步任务日志 (使用TaskLogsTable组件)
├── Token/               # 令牌管理 ⭐
│   ├── index.js          # 令牌列表 (使用TokensTable组件)
│   └── EditToken.js      # 编辑令牌
└── User/                # 用户管理 ⭐
    ├── index.js          # 用户列表 (使用UsersTable组件)
    ├── AddUser.js        # 添加用户
    └── EditUser.js       # 编辑用户
```

### 用户端需要的页面 (精简后)
```
/web/src/pages/
├── Detail/               # 使用统计 ⭐
│   └── index.js          # 个人数据统计仪表盘
├── Log/                  # 使用记录 ⭐
│   └── index.js          # 个人API调用记录
├── Midjourney/           # AI绘图 ⭐
│   └── index.js          # AI绘图功能
├── Task/                # 任务中心 ⭐
│   └── index.js          # 个人异步任务
└── Token/               # API密钥 ⭐
    ├── index.js          # 个人API密钥管理
    └── EditToken.js      # 编辑个人密钥
```

### 实际表格组件结构
```
/web/src/components/table/
├── ChannelsTable.js      # 渠道管理表格 (2138行代码)
├── LogsTable.js          # 使用日志表格
├── MjLogsTable.js        # 绘图日志表格
├── ModelPricing.js       # 模型定价表格
├── RedemptionsTable.js   # 兑换码表格
├── TaskLogsTable.js      # 任务日志表格
├── TokensTable.js        # 令牌管理表格 (826行代码)
└── UsersTable.js         # 用户管理表格
```

### 精简后的菜单结构

#### SAAS管理端菜单结构
```javascript
// 控制台区域
const workspaceItems = [
  { text: '数据看板', itemKey: 'detail', to: '/detail' },
  { text: 'API令牌', itemKey: 'token', to: '/token' },
  { text: '使用日志', itemKey: 'log', to: '/log' },
  { text: '绘图日志', itemKey: 'midjourney', to: '/midjourney' },
  { text: '任务日志', itemKey: 'task', to: '/task' }
];

// 管理员区域
const adminItems = [
  { text: '渠道', itemKey: 'channel', to: '/channel' },
  { text: '兑换码', itemKey: 'redemption', to: '/redemption' },
  { text: '用户管理', itemKey: 'user', to: '/user' },
  { text: '系统设置', itemKey: 'setting', to: '/setting' }
];
```

#### 用户端菜单结构
```javascript
// 用户功能区域
const userItems = [
  { text: '使用统计', itemKey: 'detail', to: '/detail' },
  { text: 'API密钥', itemKey: 'token', to: '/token' },
  { text: '使用记录', itemKey: 'log', to: '/log' },
  { text: 'AI绘图', itemKey: 'midjourney', to: '/midjourney' },
  { text: '任务中心', itemKey: 'task', to: '/task' }
];
```

---

## 🏗️ AI生态平台菜单架构 (完全基于NEW_API源码)

### SAAS管理端菜单 (admin.cees.cc/llms/)

#### 主菜单: 大模型API
```
大模型API
├── 📊 数据看板 (/admin/llms/detail)
├── 🔗 渠道管理 (/admin/llms/channel)
├── 🔑 令牌管理 (/admin/llms/token)
├── 👥 用户管理 (/admin/llms/user)
├── 🎁 兑换码管理 (/admin/llms/redemption)
├── 📋 使用日志 (/admin/llms/log)
├── 🎨 绘图日志 (/admin/llms/midjourney)
├── 📝 任务日志 (/admin/llms/task)
└── ⚙️ 系统设置 (/admin/llms/setting)
```

**注意**: 去除了以下功能
- ❌ 关于页面 (About) - 不需要
- ❌ 聊天功能 (Chat) - 不需要
- ❌ 首页 (Home) - 不需要
- ❌ 操练场 (Playground) - 不需要
- ❌ 支付设置 (Payment) - 集成到AI生态平台统一支付系统

#### 详细功能页面

**📊 数据看板** (`/admin/llms/detail`)
- 基于NEW_API的Detail页面
- 显示API调用统计、收入统计、用户统计
- 实时监控面板

**🔗 渠道管理** (`/admin/llms/channel`)
- 基于NEW_API的Channel页面和ChannelsTable组件
- 渠道列表、添加渠道、编辑渠道
- 渠道测试、状态管理、优先级设置
- 支持50+AI服务商渠道配置

**🔑 令牌管理** (`/admin/llms/token`)
- 基于NEW_API的Token页面和TokensTable组件
- 令牌列表、创建令牌、编辑令牌
- 配额管理、权限设置、过期管理

**👥 用户管理** (`/admin/llms/user`)
- 基于NEW_API的User页面和UsersTable组件
- 用户列表、添加用户、编辑用户
- 用户余额管理、用户组管理

**🎁 兑换码管理** (`/admin/llms/redemption`)
- 基于NEW_API的Redemption页面和RedemptionsTable组件
- 兑换码列表、生成兑换码、编辑兑换码
- 兑换码使用统计

**📋 使用日志** (`/admin/llms/log`)
- 基于NEW_API的Log页面和LogsTable组件
- API调用日志查询、统计分析
- 支持按用户、模型、时间筛选

**🎨 绘图日志** (`/admin/llms/midjourney`)
- 基于NEW_API的Midjourney页面和MjLogsTable组件
- AI绘图请求日志、图片管理
- 绘图成本统计

**📝 任务日志** (`/admin/llms/task`)
- 基于NEW_API的Task页面和TaskLogsTable组件
- 异步任务执行日志
- 任务状态监控

**⚙️ 系统设置** (`/admin/llms/setting`)
- 基于NEW_API的Setting页面，精简后包含以下Tab:
  - 运营设置 (Operation): 基础配置、日志设置、监控设置
  - 仪表盘设置 (Dashboard): API信息、公告、FAQ
  - 绘图设置 (Drawing): AI绘图功能配置
  - 倍率设置 (Ratio): 模型倍率、用户组倍率
  - 速率限制 (RateLimit): API调用频率限制
  - 模型设置 (Model): 模型配置、模型定价

**去除的设置项**:
- ❌ 聊天设置 (Chat) - 不需要聊天功能
- ❌ 支付设置 (Payment) - 集成到AI生态平台统一支付系统

---

### 用户端菜单 (www.cees.cc/llms/)

#### 主菜单: 大模型API服务
```
大模型API服务
├── 🔑 API密钥 (/llms/token)
├── 📊 使用统计 (/llms/detail)
├── 🎨 AI绘图 (/llms/midjourney)
├── 📝 任务中心 (/llms/task)
└── 📋 使用记录 (/llms/log)
```

**注意**: 去除了以下功能
- ❌ 操练场 (Playground) - 不需要
- ❌ 聊天对话 (Chat) - 不需要

#### 详细功能页面

**🔑 API密钥** (`/llms/token`)
- 基于NEW_API的Token页面 (用户视角)
- 个人API密钥管理
- 密钥使用统计

**📊 使用统计** (`/llms/detail`)
- 基于NEW_API的Detail页面 (用户视角)
- 个人使用数据统计
- 消费记录分析

**🎨 AI绘图** (`/llms/midjourney`)
- 基于NEW_API的Midjourney页面 (用户视角)
- AI绘图功能
- 绘图历史管理

**📝 任务中心** (`/llms/task`)
- 基于NEW_API的Task页面 (用户视角)
- 个人异步任务管理
- 任务状态查询

**📋 使用记录** (`/llms/log`)
- 基于NEW_API的Log页面 (用户视角)
- 个人API调用记录
- 详细使用历史

---

## 🔐 权限控制 (基于NEW_API源码)

### 权限级别
```javascript
// 基于NEW_API的权限控制
isAdmin()  // 管理员权限 - 可访问渠道、兑换码、用户管理
isRoot()   // 超级管理员权限 - 可访问系统设置
```

### 功能开关 (基于NEW_API源码)
```javascript
// 基于localStorage的功能开关
localStorage.getItem('enable_data_export')  // 数据导出功能
localStorage.getItem('enable_drawing')      // AI绘图功能
localStorage.getItem('enable_task')         // 异步任务功能
```

---

## 📊 开发工作量 (基于源码分析)

### 核心表格组件 (直接复用NEW_API代码)
- ChannelsTable.js (2138行) - 渠道管理表格
- TokensTable.js (826行) - 令牌管理表格
- UsersTable.js - 用户管理表格
- LogsTable.js - 使用日志表格
- MjLogsTable.js - 绘图日志表格
- TaskLogsTable.js - 任务日志表格
- RedemptionsTable.js - 兑换码表格

### 页面组件 (精简后)
- **SAAS管理端**: 9个主要页面组件
- **用户端**: 5个主要页面组件
- **设置子页面**: 6个设置Tab组件 (去除聊天和支付设置)
- **编辑表单**: 5个编辑表单组件

### 预计开发周期 (精简后)
- **第一阶段 (2周)**: 基础框架搭建 + 渠道管理
- **第二阶段 (2周)**: 令牌管理 + 用户管理 + 数据看板
- **第三阶段 (2周)**: 日志管理 + 系统设置
- **第四阶段 (2周)**: 用户端功能 + 测试优化

**总计**: 8周开发周期 (比原计划减少2周)

---

## 📝 技术实现要点

### 数据库表 (基于NEW_API结构)
```sql
-- 基于NEW_API的表结构，添加tenant_id支持多租户
llmsapi_channels     -- 渠道表
llmsapi_tokens       -- 令牌表  
llmsapi_users        -- 用户表
llmsapi_logs         -- 使用日志表
llmsapi_redemptions  -- 兑换码表
llmsapi_abilities    -- 能力表
llmsapi_options      -- 配置表
```

### 前端组件复用
- 最大化复用NEW_API的React组件
- 改造为Vue.js组件
- 保持原有的功能逻辑和UI设计

### API接口设计
- 基于NEW_API的Go后端接口
- 保持相同的API结构和参数
- 添加多租户支持

---

---

## 📊 精简融合后的功能对比

### 去除的功能模块
| 功能模块 | 原因 | 影响 |
|---------|------|------|
| 关于页面 (About) | 不需要独立关于页面 | 无影响 |
| 聊天功能 (Chat) | AI生态平台有独立聊天模块 | 避免功能重复 |
| 首页 (Home) | 集成到数据看板 | 简化导航 |
| 操练场 (Playground) | 用户端不需要API测试工具 | 降低复杂度 |
| 支付设置 (Payment) | 集成到AI生态平台统一支付 | 避免支付系统冲突 |

### 保留的核心功能
| 功能模块 | SAAS管理端 | 用户端 | 重要性 |
|---------|-----------|-------|-------|
| 数据看板 | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| 渠道管理 | ✅ | ❌ | ⭐⭐⭐⭐⭐ |
| 令牌管理 | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| 用户管理 | ✅ | ❌ | ⭐⭐⭐⭐⭐ |
| 兑换码管理 | ✅ | ❌ | ⭐⭐⭐⭐ |
| 使用日志 | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| 绘图日志 | ✅ | ✅ | ⭐⭐⭐ |
| 任务日志 | ✅ | ✅ | ⭐⭐⭐ |
| 系统设置 | ✅ | ❌ | ⭐⭐⭐⭐⭐ |

### 开发效率提升
- **页面数量减少**: 从18个页面减少到14个页面
- **开发周期缩短**: 从10周缩短到8周
- **维护成本降低**: 去除重复功能，降低维护复杂度
- **集成度提高**: 与AI生态平台现有功能更好融合

---

## 🎯 最终菜单架构

### SAAS管理端 (9个页面)
```
大模型API管理
├── 📊 数据看板        # 核心统计
├── 🔗 渠道管理        # 核心功能
├── 🔑 令牌管理        # 核心功能
├── 👥 用户管理        # 核心功能
├── 🎁 兑换码管理      # 营销功能
├── 📋 使用日志        # 监控功能
├── 🎨 绘图日志        # 扩展功能
├── 📝 任务日志        # 扩展功能
└── ⚙️ 系统设置        # 配置功能
```

### 用户端 (5个页面)
```
大模型API服务
├── 🔑 API密钥        # 核心功能
├── 📊 使用统计        # 核心功能
├── 🎨 AI绘图         # 扩展功能
├── 📝 任务中心        # 扩展功能
└── 📋 使用记录        # 监控功能
```

---

**文档状态**: ✅ 精简融合完成
**最后更新**: 2025-07-18
**开发周期**: 8周 (比原计划减少2周)
**下一步**: 开始第一阶段开发 - 渠道管理功能
