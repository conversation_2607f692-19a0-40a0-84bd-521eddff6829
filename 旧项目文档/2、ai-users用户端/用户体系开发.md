# 用户体系版块完整开发规划

## 📋 项目概述

**开发目标**: 构建商用级用户体系版块，支持可扩展的会员权益管理、智能标签系统、动态营销和分销预留功能。

**核心特色**: 
- 🏗️ 可扩展会员权益体系 - 支持未来功能模块快速集成
- 🏷️ 智能标签配置系统 - 系统管理子菜单独立管理
- 🎯 动态精准营销系统 - 前端组件化 + 后端配置驱动
- 💰 分销接口预留 - 为后续分销系统预留扩展点
- 📊 复用现有数据 - 基于现有智能体数据分析

## 🎯 整合后的功能架构

### 1. 用户管理系统升级

#### 1.1 多渠道注册与认证（简化版）
```
注册方式:
├── 手机号注册 (短信验证)
├── 邮箱注册 (邮件验证)
└── 微信登录 (OAuth2)

登录方式:
├── 手机号密码登录
├── 邮箱密码登录
├── 手机号短信验证登录
├── 微信扫码登录
└── 记住登录状态 (7天/30天)
```

#### 1.2 身份认证机制（简化版）
```
个人用户认证:
├── 手机号实名认证
└── 身份证认证

企业用户认证:
├── 营业执照认证
└── 企业基本信息认证
```

#### 1.3 安全策略管理
```
登录安全:
├── 密码强度检查
├── 登录失败锁定
├── 异地登录提醒
├── 设备管理 (信任设备)
└── 登录日志记录

账户安全:
├── 二次验证 (2FA)
├── 安全问题设置
├── 密码定期更新提醒
└── 账户冻结/解冻
```

### 2. 用户画像系统

#### 2.1 基础信息管理
```
个人信息:
├── 基本资料 (姓名、性别、年龄、地区)
├── 联系方式 (手机、邮箱、地址)
├── 职业信息 (行业、职位、公司)
└── 头像管理 (上传、裁剪、默认)

企业信息:
├── 企业基本信息
├── 行业分类
├── 企业规模
└── 联系人信息
```

#### 2.2 行为数据分析（复用现有数据）
```
基于现有智能体数据分析:
├── 智能体访问记录 (复用 ai_agent_realtime_viewers)
├── 智能体使用统计 (复用 ai_agent_statistics)
├── 用户使用偏好分析
├── 活跃度评分
└── 价值评估模型
```

### 3. 标签配置系统（系统管理子菜单）

#### 3.1 标签配置管理
```
系统管理 > 标签配置:
├── 用户标签配置
├── 行为标签配置
├── 价值标签配置
├── 业务标签配置
└── 自定义标签配置
```

#### 3.2 标签规则引擎
```
自动标签规则:
├── 活跃度标签 (基于登录频次)
├── 价值标签 (基于付费行为)
├── 偏好标签 (基于智能体使用)
└── 风险标签 (基于异常行为)

手动标签管理:
├── 运营标签分配
├── 业务标签管理
├── 批量标签操作
└── 标签统计分析
```

### 4. 可扩展会员权益体系（核心架构）

#### 4.1 权益模块注册机制
```python
# 智能体模块权益
class AgentPermissionModule(PermissionModule):
    def register_permissions(self):
        return {
            "display_name": "智能体系统",
            "permissions": {
                "agent.use": "使用智能体",
                "agent.create": "创建智能体", 
                "agent.share": "分享智能体",
                "agent.analytics": "查看智能体数据"
            },
            "quotas": {
                "agent.daily_calls": "每日调用次数",
                "agent.monthly_calls": "每月调用次数",
                "agent.concurrent_sessions": "并发会话数"
            },
            "features": {
                "agent.custom_prompt": "自定义提示词",
                "agent.advanced_settings": "高级设置",
                "agent.api_access": "API访问"
            },
            "resource_lists": {
                "available_agents": {
                    "display_name": "可使用的智能体",
                    "options": [] # 动态从数据库获取
                }
            }
        }
```

### 5. 订阅管理系统

#### 5.1 订阅生命周期管理
```
订阅管理:
├── 套餐选择和对比
├── 优惠券应用
├── 支付方式选择
├── 自动续费设置
├── 套餐升级/降级
├── 订阅暂停/恢复
└── 退款处理
```

### 6. 积分权益系统

#### 6.1 积分获取规则
```
日常行为积分:
├── 每日登录 (+10积分)
├── 完善个人信息 (+50积分)
├── 首次使用智能体 (+20积分)
└── 分享智能体 (+15积分)

付费行为积分:
├── 首次付费 (+500积分)
├── 续费奖励 (+200积分)
├── 升级套餐 (+300积分)
└── 推荐付费用户 (+1000积分)
```

### 7. 动态精准营销系统

#### 7.1 客户端动态营销实现
```
动态内容配置:
├── 营销横幅配置 (位置、内容、目标用户群)
├── 弹窗消息配置 (触发条件、展示内容)
├── 推荐位配置 (智能体推荐、功能推荐)
├── 个性化消息配置 (基于用户标签)
└── 活动页面配置 (动态生成活动页)

前端动态渲染:
├── 基于用户标签的内容过滤
├── 实时营销内容拉取
├── A/B测试支持
├── 点击行为追踪
└── 转化效果统计
```

## 🛠️ 数据库设计（核心表）

### 用户扩展表
```sql
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    real_name VARCHAR(100),
    gender VARCHAR(10),
    birthday DATE,
    profession VARCHAR(100),
    company VARCHAR(200),
    industry VARCHAR(100),
    user_type VARCHAR(20) DEFAULT 'personal',
    avatar_url VARCHAR(500),
    tags JSON,
    preferences JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 权益模块注册表
```sql
CREATE TABLE permission_modules (
    id SERIAL PRIMARY KEY,
    module_name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    schema_config JSON,
    version VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 会员等级表
```sql
CREATE TABLE membership_levels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2),
    billing_cycle VARCHAR(20),
    permissions_config JSON,
    description TEXT,
    features_highlight JSON,
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 标签配置表
```sql
CREATE TABLE tag_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50),
    category VARCHAR(50),
    rules JSON,
    description TEXT,
    color VARCHAR(20),
    icon VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🚀 开发阶段规划

### 第一阶段 (3-4天): 基础架构 + 用户画像
- 实现可扩展权益架构
- 用户画像系统
- 标签配置系统

### 第二阶段 (3-4天): 会员权益体系
- 可扩展会员等级配置
- 订阅管理系统
- 权益使用统计

### 第三阶段 (2-3天): 积分系统 + 分销预留
- 积分权益系统
- 分销接口预留
- 用户中心完善

### 第四阶段 (2-3天): 动态营销系统
- 营销配置管理
- 前端动态渲染
- 营销效果分析

### 第五阶段 (1-2天): 扩展验证 + 优化
- 扩展模块验证
- 系统优化
- 文档和测试

## 💡 技术重点和创新点

### 1. 可扩展架构设计
- 模块化权益管理，支持未来功能快速集成
- 统一权限接口，标准化开发流程
- 动态配置系统，无需代码修改即可调整权益

### 2. 智能标签系统
- 自动标签规则引擎
- 基于用户行为的智能分类
- 支持营销精准投放

### 3. 动态营销系统
- 前端组件化设计
- 后端配置驱动
- 实时效果追踪

## 📊 完整数据库设计

### 用户订阅表
```sql
CREATE TABLE user_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    level_id INTEGER REFERENCES membership_levels(id),
    status VARCHAR(20),
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    auto_renew BOOLEAN DEFAULT FALSE,
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 用户标签关联表
```sql
CREATE TABLE user_tags (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    tag_id INTEGER REFERENCES tag_configs(id),
    assigned_by INTEGER,
    assigned_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    UNIQUE(user_id, tag_id)
);
```

### 用户权益使用记录表
```sql
CREATE TABLE user_permission_usage (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    module_name VARCHAR(50),
    quota_key VARCHAR(100),
    used_amount INTEGER DEFAULT 0,
    quota_limit INTEGER,
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, module_name, quota_key, period_start)
);
```

### 积分记录表
```sql
CREATE TABLE user_points (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    points INTEGER,
    type VARCHAR(50),
    source VARCHAR(100),
    description TEXT,
    reference_id INTEGER,
    reference_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 营销配置表
```sql
CREATE TABLE marketing_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    type VARCHAR(50),
    content JSON,
    target_rules JSON,
    display_rules JSON,
    schedule_config JSON,
    status VARCHAR(20) DEFAULT 'active',
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 分销提现表 (预留)
```sql
CREATE TABLE withdrawal_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    amount DECIMAL(10,2),
    status VARCHAR(20),
    bank_info JSON,
    admin_note TEXT,
    processed_by INTEGER,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🎨 前端页面设计

### 用户中心模块
```
用户中心:
├── 个人资料页面
│   ├── 基本信息编辑
│   ├── 头像上传裁剪
│   ├── 认证状态显示
│   └── 用户标签展示
├── 安全设置页面
│   ├── 密码修改
│   ├── 二次验证设置
│   ├── 设备管理
│   ├── 登录日志
│   └── 安全问题设置
├── 会员中心页面
│   ├── 当前会员状态
│   ├── 权益使用情况
│   ├── 套餐对比升级
│   └── 权益详细说明
├── 订阅管理页面
│   ├── 订阅历史
│   ├── 自动续费设置
│   ├── 发票管理
│   └── 退款申请
├── 积分中心页面
│   ├── 积分余额显示
│   ├── 积分获取记录
│   ├── 积分兑换商城
│   └── 兑换历史
└── 消息中心页面
    ├── 系统通知
    ├── 营销消息
    ├── 安全提醒
    └── 个性化推荐
```

### 管理后台模块
```
管理后台:
├── 用户管理
│   ├── 用户列表 (支持标签筛选)
│   ├── 用户详情 (画像、行为、权益)
│   ├── 用户分群管理
│   └── 用户行为分析
├── 会员管理
│   ├── 会员等级配置 (可扩展权益配置界面)
│   ├── 订阅管理
│   ├── 权益使用统计
│   └── 会员数据分析
├── 系统管理 > 标签配置
│   ├── 标签分类管理
│   ├── 自动标签规则配置
│   ├── 手动标签分配
│   └── 标签使用统计
├── 积分管理
│   ├── 积分规则配置
│   ├── 积分商品管理
│   ├── 兑换记录管理
│   └── 积分统计分析
├── 营销管理
│   ├── 营销活动配置
│   ├── 目标用户群设置
│   ├── 营销内容管理
│   ├── 效果数据分析
│   └── A/B测试管理
└── 分销管理 (预留)
    ├── 分销用户管理
    ├── 佣金规则配置
    ├── 提现申请处理
    └── 分销数据统计
```

## 📊 API接口设计

### 用户管理API
```python
/api/v1/users/profile                    # GET/PUT 用户画像
/api/v1/users/security                   # GET/PUT 安全设置
/api/v1/users/tags                       # GET 用户标签
/api/v1/users/behaviors                  # GET 行为分析
/api/v1/users/login-logs                 # GET 登录日志
```

### 认证API
```python
/api/v1/auth/register                    # POST 注册
/api/v1/auth/login                       # POST 登录
/api/v1/auth/sms-login                   # POST 短信登录
/api/v1/auth/wechat-login               # POST 微信登录
/api/v1/auth/2fa/setup                  # POST 设置二次验证
/api/v1/auth/2fa/verify                 # POST 验证二次验证
```

### 会员系统API
```python
/api/v1/membership/levels               # GET 会员等级列表
/api/v1/membership/current              # GET 当前会员状态
/api/v1/membership/subscribe            # POST 订阅会员
/api/v1/membership/upgrade              # PUT 升级会员
/api/v1/membership/cancel               # DELETE 取消订阅
/api/v1/membership/permissions          # GET 权限检查
/api/v1/membership/quotas               # GET 配额查询
/api/v1/membership/usage                # GET 使用统计
```

### 权益模块API
```python
/api/v1/permissions/modules             # GET 可用权益模块
/api/v1/permissions/check               # POST 权限检查
/api/v1/permissions/quota-usage         # GET 配额使用情况
```

### 标签系统API
```python
/api/v1/tags/configs                    # GET/POST 标签配置
/api/v1/tags/assign                     # POST 分配标签
/api/v1/tags/auto-assign                # POST 自动标签分配
/api/v1/tags/user-tags                  # GET 用户标签列表
```

### 积分系统API
```python
/api/v1/points/balance                  # GET 积分余额
/api/v1/points/history                  # GET 积分历史
/api/v1/points/products                 # GET 积分商品
/api/v1/points/exchange                 # POST 积分兑换
/api/v1/points/exchange-history         # GET 兑换历史
```

### 营销系统API
```python
/api/v1/marketing/content               # GET 动态营销内容
/api/v1/marketing/recommendations       # GET 个性化推荐
/api/v1/marketing/track                 # POST 行为追踪
/api/v1/marketing/configs               # GET/POST 营销配置
```

---

## 🚀 开发进度记录

### 第一阶段：基础用户体系 ✅ 已完成 (2025-07-06)
- [x] 用户注册登录系统
- [x] 用户画像管理
- [x] 标签系统
- [x] 基础权限控制

### 第二阶段：会员权益体系 ✅ 已完成 (2025-07-06)
- [x] 会员等级管理
- [x] 订阅管理系统
- [x] 权益使用统计
- [x] 配额管理优化

### 第三阶段：积分系统 + 分销预留 ✅ 已完成 (2025-07-06)
- [x] 积分权益系统开发
  - [x] 积分规则管理（后台）
  - [x] 积分商品管理（后台）
  - [x] 积分获取/消费API
  - [x] 积分兑换功能
  - [x] 积分统计分析
- [x] 分销接口预留
  - [x] 分销商管理系统
  - [x] 佣金规则配置
  - [x] 提现申请处理
  - [x] 分销数据统计
- [x] 用户中心完善
- [x] 数据库表结构创建

### 第四阶段：动态营销系统 ✅ 已完成 (2025-07-06)
- [x] 营销配置管理
  - [x] 营销配置CRUD操作
  - [x] 目标用户规则设置
  - [x] 展示规则配置
  - [x] 时间安排管理
- [x] 前端动态渲染
  - [x] 动态营销组件
  - [x] 多种营销类型支持
  - [x] 用户行为追踪
  - [x] 个性化内容展示
- [x] 营销效果分析
  - [x] 实时数据监控
  - [x] 营销效果统计
  - [x] 用户群体分析
  - [x] 转化率分析

### 已实现的核心功能
1. **完整的会员体系** - 4个会员等级，完整的权益配置
2. **智能配额管理** - 实时监控、预警、重置、转移
3. **积分权益系统** - 规则管理、商品管理、兑换功能
4. **数据统计分析** - 多维度的使用统计和收入分析
5. **用户画像系统** - 6种预设标签，动态用户分群

**开发状态**: 🚧 第三阶段进行中，积分系统已完成
**预计工期**: 11-16天
**商业价值**: 会员付费转化率提升30-50%，用户留存率提升20-30%
