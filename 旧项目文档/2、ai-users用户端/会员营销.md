会员营销模块说明：

一、会员体系（高自定义扩展模式）
1、时间卡：VIP月卡、VIP年卡、SVIP年卡（每种可设置不同的权益和配额）
2、特权卡：可配置大模型、智能体、插件、推广收益，这四个产品体系的使用次数，
比如论文智能体使用次卡套餐，在权益体系已经配置了【论文权益模板：论文智能体A可用50次+智能体B可用100次】
比如大模型claude4可用100万tokens量，在权益体系已经配置了【claude使用权益模板：claude4可用量100万tokens】


二、权益体系（高自定义扩展模式）
1、可使用的智能体
2、可使用的大模型API
3、可使用的插件
4、推广佣金比例

可设置权益模板
比如权益A模板：可使用大模型20种模型+20个智能体+4个插件
比如权益B模板：可使用大模型60种模型+9个智能体+25个插件
比如权益C模板：设置推广拉新模板A+提示词插件+30个智能体


三、配额体系（高自定义扩展模式）
1、大模型配额
每个大模型调用的（限制次数、使用tokens量、使用价格）举例以claude-sonnet-4-20250514这个模型为例
次数配额：如每天可调用200次
使用量配额：如每天可调用10万tokens
价格配额：【价格1（提示¥3/M tokens 补全¥15/M tokens）】【价格2（提示¥4/M tokens 补全¥17/M tokens）】
支持可设置大模型配额模板

2、智能体配额
智能体对话次数配额（除了可设置次数外，还需要有无限次）
智能体分组使用配额，比如A组可使用智能体1.2.3.4可用，B组可使用2.3.4.5.6.7可用
可设置智能体配额模板

3、插件配额
价格配额：（按次价格/按量价格）比如即梦视频生成调用插件可以按0.3元一次。按输入输出tokens
时间配额：可设置在某个时间段可用，日/月/年/永久
次数配额：可设置不同的插件可调用的次数
可设置插件配额模板

4、推广配额
拉新奖励配额
销售会员分成比例配额
充值金额分成配额
可设置推广分成比例的配额模板

业务实现路径：先设置配额体系→再设置权益体系→最后设置会员体系
把推广配额想象为汽车零件，权益体系想象为车身，会员体系想象为整部汽车，组合起来，这套营销体系叫做超级会员营销体系。
核心是这三个都是独立的，但又是互相协作的

主菜单设计排序（二次设计整改）
会员营销
├数据大屏（横向菜单：数据概况/订单数据/使用统计）
├会员列表（已购买的会员用户）
├会员订单
├会员组装【对应的是：一、会员体系（高自定义扩展模式）】
├权益组装【对应的是：二、权益体系（高自定义扩展模式）】
├配额组装【对应的是：三、配额体系（高自定义扩展模式）】
├会员设置（横向菜单：会员设置/权益设置/配额设置/数据设置）

推广系统（原分销推广主菜单）
├推广系统
├推广数据大屏
├分销商管理
├推广链接管理
├佣金管理
├推广活动
├推广设置

增加要求2：暂未开发功能接入说明
由于大模型API、插件这两个大功能还没有开发，所以在开发会员营销时，要预留接口，并在说明文档中重点提示，以便后期开发理解和使用

增加要求3：后期扩展
因为后面这个AI生态系统还会接入数字人、私域运营、AI智能直播、AI剪辑等功能，所以要预接口，并在说明文档中重点提示，以便后期开发理解和使用