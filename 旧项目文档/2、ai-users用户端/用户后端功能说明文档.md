# AI生态平台用户后端功能说明文档

**文档版本**: 第二版
**更新时间**: 2025年7月14日
**技术负责人**: 技术部主管
**适用范围**: ai-users容器所有已开发功能的完整分析

---

## 📋 文档概述

本文档详细分析AI生态平台ai-users容器的所有已开发功能，包括用户管理、会员营销、推广、积分、商城、支付、课程等功能模块的完整业务逻辑、数据模型设计和技术实现。

### 🎯 ai-users服务架构概览

ai-users服务是AI生态平台的用户基础层核心服务，负责所有用户相关的业务功能：

```
ai-users服务架构
├── 🔐 用户认证层
│   ├── 用户注册登录
│   ├── JWT令牌管理
│   ├── 会话管理
│   └── 安全验证
├── 👤 用户管理层
│   ├── 用户基础信息
│   ├── 用户资料管理
│   ├── 权限角色管理
│   └── 用户行为分析
├── 💎 会员营销层
│   ├── 会员等级体系
│   ├── 权益配置管理
│   ├── 营销活动管理
│   └── 配额管理系统
├── 💰 推广赚钱层
│   ├── 推广链接管理
│   ├── 佣金计算系统
│   ├── 团队管理体系
│   └── 提现管理系统
├── 🎯 积分系统层
│   ├── 积分获取规则
│   ├── 积分消费管理
│   ├── 积分兑换商城
│   └── 积分等级体系
├── 🛒 商城系统层
│   ├── 商品管理系统
│   ├── 订单处理流程
│   ├── 库存管理系统
│   └── 优惠券系统
├── 💳 支付系统层
│   ├── 支付接口集成
│   ├── 订单管理系统
│   ├── 退款处理流程
│   └── 账单管理系统
└── 🎓 课程系统层 (预留)
    ├── 课程管理系统
    ├── 学习进度跟踪
    ├── 认证考试系统
    └── 证书颁发系统
```

---

## 🔐 用户认证管理功能

### 核心设计思路
用户认证采用JWT令牌机制，支持多租户隔离，实现安全可靠的用户身份验证和会话管理。

### 1. 用户注册功能 ✅ 已完成

#### 业务流程设计
```
用户注册流程：
1. 前端提交注册信息 → 2. 后端验证数据格式 → 3. 检查用户名/邮箱唯一性
4. 密码哈希加密 → 5. 创建用户记录 → 6. 创建用户资料 → 7. 生成JWT令牌对
8. 返回用户信息和令牌 → 9. 可选：发送验证邮件
```

#### 数据模型设计
```go
// User 用户基础信息模型
type User struct {
    ID          string    `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Username    string    `json:"username" gorm:"type:varchar(50);uniqueIndex;not null"`
    Email       string    `json:"email" gorm:"type:varchar(100);uniqueIndex;not null"`
    PhoneNumber *string   `json:"phone_number" gorm:"type:varchar(20);uniqueIndex"`
    Password    string    `json:"-" gorm:"type:varchar(255);not null"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    IsVerified  bool      `json:"is_verified" gorm:"default:false"`
    LastLoginAt *time.Time `json:"last_login_at"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

// UserProfile 用户扩展信息模型
type UserProfile struct {
    ID            uint      `json:"id" gorm:"primaryKey"`
    UserID        string    `json:"user_id" gorm:"type:varchar(50);uniqueIndex;not null"`
    RealName      *string   `json:"real_name" gorm:"type:varchar(100)"`
    Gender        *string   `json:"gender" gorm:"type:varchar(10)"`
    Birthday      *time.Time `json:"birthday"`
    Profession    *string   `json:"profession" gorm:"type:varchar(100)"`
    Company       *string   `json:"company" gorm:"type:varchar(200)"`
    UserType      string    `json:"user_type" gorm:"type:varchar(20);default:'personal'"`
    AvatarURL     *string   `json:"avatar_url" gorm:"type:varchar(500)"`
    Tags          JSON      `json:"tags" gorm:"type:jsonb"`
    Preferences   JSON      `json:"preferences" gorm:"type:jsonb"`
    ActivityScore int       `json:"activity_score" gorm:"default:0"`
    ValueScore    int       `json:"value_score" gorm:"default:0"`
}
```

#### 技术实现特点
- **密码安全**: 使用bcrypt哈希算法，盐值随机生成
- **数据验证**: 邮箱格式、用户名长度、密码强度验证
- **唯一性检查**: 用户名、邮箱、手机号唯一性约束
- **多租户支持**: 通过TenantID实现数据隔离
- **扩展性设计**: 用户基础信息和扩展信息分离

### 2. 用户登录功能 ✅ 已完成

#### 业务流程设计
```
用户登录流程：
1. 前端提交登录凭据 → 2. 后端验证用户存在性 → 3. 验证密码正确性
4. 检查用户状态 → 5. 生成JWT令牌对 → 6. 保存会话信息 → 7. 更新最后登录时间
8. 返回用户信息和令牌 → 9. 可选：记录登录日志
```

#### JWT令牌管理
```go
// TokenPair JWT令牌对
type TokenPair struct {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int64  `json:"expires_in"`
    TokenType    string `json:"token_type"`
}

// JWTClaims JWT声明
type JWTClaims struct {
    UserID   string   `json:"user_id"`
    TenantID string   `json:"tenant_id"`
    Username string   `json:"username"`
    Roles    []string `json:"roles"`
    jwt.RegisteredClaims
}
```

#### 会话管理设计
```go
// UserSession 用户会话模型
type UserSession struct {
    ID           string     `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID       string     `json:"user_id" gorm:"type:varchar(50);not null;index"`
    RefreshToken string     `json:"refresh_token" gorm:"type:varchar(500);not null"`
    DeviceInfo   *string    `json:"device_info" gorm:"type:text"`
    IPAddress    *string    `json:"ip_address" gorm:"type:varchar(45)"`
    UserAgent    *string    `json:"user_agent" gorm:"type:text"`
    ExpiresAt    time.Time  `json:"expires_at"`
    IsActive     bool       `json:"is_active" gorm:"default:true"`
    CreatedAt    time.Time  `json:"created_at"`
    UpdatedAt    time.Time  `json:"updated_at"`
}
```

### 3. 令牌刷新功能 ✅ 已完成

#### 刷新机制设计
- **访问令牌**: 短期有效（30分钟），用于API访问
- **刷新令牌**: 长期有效（7天），用于获取新的访问令牌
- **安全策略**: 刷新令牌使用后立即失效，生成新的令牌对
- **会话管理**: 支持多设备登录，每个设备独立会话

### 4. 用户权限管理 ✅ 已完成

#### 角色权限模型
```go
// UserRole 用户角色模型
type UserRole struct {
    ID       uint      `json:"id" gorm:"primaryKey"`
    UserID   string    `json:"user_id" gorm:"type:varchar(50);not null;index"`
    Role     string    `json:"role" gorm:"type:varchar(50);not null"`
    IsActive bool      `json:"is_active" gorm:"default:true"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

#### 权限设计
- **基础角色**: user（普通用户）、admin（管理员）、super_admin（超级管理员）
- **权限继承**: 高级角色自动继承低级角色权限
- **动态权限**: 支持运行时权限检查和动态授权
- **租户隔离**: 权限在租户范围内有效

---

## 💎 会员营销管理功能

### 核心设计思路
会员营销采用"配额组装 → 权益组装 → 会员组装"的三层设计思路，实现灵活的会员等级体系和权益配置。

### 1. 会员等级体系 ✅ 已完成

#### 设计思路说明
```
会员等级设计思路：
配额组装层：定义各种资源配额（AI对话次数、存储空间、API调用等）
    ↓
权益组装层：将配额组合成权益包（基础权益、高级权益、专属权益）
    ↓
会员组装层：将权益包组合成会员等级（免费版、VIP月卡、VIP年卡、SVIP年卡）
```

#### 会员等级模型
```go
// MembershipLevel 会员等级模型
type MembershipLevel struct {
    ID                uint            `json:"id" gorm:"primaryKey"`
    Name              string          `json:"name" gorm:"type:varchar(50);not null"`
    Price             decimal.Decimal `json:"price" gorm:"type:decimal(10,2)"`
    BillingCycle      *string         `json:"billing_cycle" gorm:"type:varchar(20)"` // monthly, yearly
    PermissionsConfig JSON            `json:"permissions_config" gorm:"type:jsonb"`
    Description       *string         `json:"description" gorm:"type:text"`
    FeaturesHighlight JSON            `json:"features_highlight" gorm:"type:jsonb"`
    SortOrder         int             `json:"sort_order" gorm:"default:0"`
    Status            string          `json:"status" gorm:"type:varchar(20);default:'active'"`
}
```

#### 权益配置设计
```json
// PermissionsConfig 权益配置示例
{
  "quotas": {
    "ai_chat_daily": 100,           // 每日AI对话次数
    "agent_use_daily": 50,          // 每日智能体使用次数
    "storage_mb": 1000,             // 存储空间(MB)
    "api_calls_monthly": 10000,     // 每月API调用次数
    "plugin_install_limit": 20      // 插件安装数量限制
  },
  "features": {
    "priority_support": true,       // 优先客服支持
    "advanced_analytics": true,     // 高级数据分析
    "custom_branding": false,       // 自定义品牌
    "api_access": true,             // API访问权限
    "export_data": true             // 数据导出功能
  },
  "restrictions": {
    "concurrent_sessions": 5,       // 并发会话数
    "file_upload_size_mb": 100,     // 文件上传大小限制
    "team_members": 1               // 团队成员数量
  }
}
```

### 2. 用户会员管理 ✅ 已完成

#### 用户会员模型
```go
// UserMembership 用户会员信息模型
type UserMembership struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    LevelID         uint            `json:"level_id" gorm:"not null;index"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'active'"`
    StartDate       time.Time       `json:"start_date"`
    EndDate         *time.Time      `json:"end_date"`
    AutoRenew       bool            `json:"auto_renew" gorm:"default:false"`
    PaymentMethod   *string         `json:"payment_method" gorm:"type:varchar(50)"`
    LastPaymentDate *time.Time      `json:"last_payment_date"`
    NextPaymentDate *time.Time      `json:"next_payment_date"`
    TotalPaid       decimal.Decimal `json:"total_paid" gorm:"type:decimal(10,2)"`
    TrialStartDate  *time.Time      `json:"trial_start_date"`
    TrialEndDate    *time.Time      `json:"trial_end_date"`
    CancelledAt     *time.Time      `json:"cancelled_at"`
    CancellationReason *string      `json:"cancellation_reason" gorm:"type:text"`
}
```

#### 会员订阅流程
```
会员订阅业务流程：
1. 用户选择会员等级 → 2. 系统计算价格和优惠 → 3. 创建会员订单
4. 用户选择支付方式 → 5. 调用支付接口 → 6. 支付成功回调
7. 激活会员权益 → 8. 分配配额资源 → 9. 发送确认通知
```

### 3. 权益分配系统 ✅ 已完成

#### 权益模板设计
```go
// BenefitTemplate 权益模板模型
type BenefitTemplate struct {
    ID                 uint            `json:"id" gorm:"primaryKey"`
    MembershipLevelID  uint            `json:"membership_level_id" gorm:"not null;index"`
    BenefitType        string          `json:"benefit_type" gorm:"type:varchar(50);not null"`
    BenefitName        string          `json:"benefit_name" gorm:"type:varchar(100);not null"`
    BenefitValue       JSON            `json:"benefit_value" gorm:"type:jsonb"`
    Description        *string         `json:"description" gorm:"type:text"`
    IsActive           bool            `json:"is_active" gorm:"default:true"`
    SortOrder          int             `json:"sort_order" gorm:"default:0"`
}
```

#### 配额管理系统
```go
// UserQuota 用户配额模型
type UserQuota struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    UserID      string    `json:"user_id" gorm:"type:varchar(50);not null;index"`
    QuotaType   string    `json:"quota_type" gorm:"type:varchar(50);not null"`
    TotalQuota  int       `json:"total_quota" gorm:"not null"`
    UsedQuota   int       `json:"used_quota" gorm:"default:0"`
    ResetPeriod string    `json:"reset_period" gorm:"type:varchar(20)"` // daily, monthly, yearly
    LastReset   time.Time `json:"last_reset"`
    ExpiresAt   *time.Time `json:"expires_at"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
}
```

### 4. 营销活动管理 🚧 部分完成

#### 优惠券系统
```go
// Coupon 优惠券模型
type Coupon struct {
    ID           uint            `json:"id" gorm:"primaryKey"`
    TenantID     string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Code         string          `json:"code" gorm:"type:varchar(50);uniqueIndex;not null"`
    Name         string          `json:"name" gorm:"type:varchar(100);not null"`
    Type         string          `json:"type" gorm:"type:varchar(20);not null"` // percentage, fixed, points
    Value        decimal.Decimal `json:"value" gorm:"type:decimal(10,2);not null"`
    MinAmount    *decimal.Decimal `json:"min_amount" gorm:"type:decimal(10,2)"`
    MaxDiscount  *decimal.Decimal `json:"max_discount" gorm:"type:decimal(10,2)"`
    UsageLimit   *int            `json:"usage_limit"`
    UsedCount    int             `json:"used_count" gorm:"default:0"`
    UserLimit    *int            `json:"user_limit"`
    ValidityDays *int            `json:"validity_days"`
    Status       string          `json:"status" gorm:"type:varchar(20);default:'active'"`
    StartDate    time.Time       `json:"start_date"`
    EndDate      *time.Time      `json:"end_date"`
}
```

---

## 💰 推广赚钱管理功能

### 核心设计思路
推广系统采用多级分销模式，支持推广链接生成、佣金计算、团队管理和提现处理，实现完整的推广营销闭环。

### 1. 推广链接管理 ✅ 已完成

#### 推广链接模型
```go
// ReferralLink 推广链接模型
type ReferralLink struct {
    ID          string    `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID      string    `json:"user_id" gorm:"type:varchar(50);not null;index"`
    Code        string    `json:"code" gorm:"type:varchar(20);uniqueIndex;not null"`
    Type        string    `json:"type" gorm:"type:varchar(20);not null"` // general, product, event
    TargetURL   string    `json:"target_url" gorm:"type:varchar(500);not null"`
    ClickCount  int       `json:"click_count" gorm:"default:0"`
    ConvertCount int      `json:"convert_count" gorm:"default:0"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    ExpiresAt   *time.Time `json:"expires_at"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### 推广关系管理
```go
// ReferralRelation 推广关系模型
type ReferralRelation struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    ReferrerID  string    `json:"referrer_id" gorm:"type:varchar(50);not null;index"`
    RefereeID   string    `json:"referee_id" gorm:"type:varchar(50);not null;index"`
    Level       int       `json:"level" gorm:"not null;default:1"` // 推广层级
    ReferralCode string   `json:"referral_code" gorm:"type:varchar(20);not null"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 2. 佣金计算系统 ✅ 已完成

#### 佣金规则设计
```go
// CommissionRule 佣金规则模型
type CommissionRule struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Name            string          `json:"name" gorm:"type:varchar(100);not null"`
    Type            string          `json:"type" gorm:"type:varchar(20);not null"` // percentage, fixed
    Level           int             `json:"level" gorm:"not null"` // 推广层级
    Rate            decimal.Decimal `json:"rate" gorm:"type:decimal(5,4);not null"`
    MinAmount       *decimal.Decimal `json:"min_amount" gorm:"type:decimal(10,2)"`
    MaxAmount       *decimal.Decimal `json:"max_amount" gorm:"type:decimal(10,2)"`
    ProductCategory *string         `json:"product_category" gorm:"type:varchar(50)"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'active'"`
    StartDate       time.Time       `json:"start_date"`
    EndDate         *time.Time      `json:"end_date"`
}
```

#### 佣金记录管理
```go
// CommissionRecord 佣金记录模型
type CommissionRecord struct {
    ID              string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    RefereeID       string          `json:"referee_id" gorm:"type:varchar(50);not null"`
    OrderID         string          `json:"order_id" gorm:"type:varchar(50);not null"`
    Level           int             `json:"level" gorm:"not null"`
    CommissionType  string          `json:"commission_type" gorm:"type:varchar(20);not null"`
    OrderAmount     decimal.Decimal `json:"order_amount" gorm:"type:decimal(10,2);not null"`
    CommissionRate  decimal.Decimal `json:"commission_rate" gorm:"type:decimal(5,4);not null"`
    CommissionAmount decimal.Decimal `json:"commission_amount" gorm:"type:decimal(10,2);not null"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
    SettledAt       *time.Time      `json:"settled_at"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

### 3. 团队管理体系 ✅ 已完成

#### 团队统计模型
```go
// TeamStats 团队统计模型
type TeamStats struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);uniqueIndex;not null"`
    DirectReferrals int             `json:"direct_referrals" gorm:"default:0"`
    TotalReferrals  int             `json:"total_referrals" gorm:"default:0"`
    ActiveReferrals int             `json:"active_referrals" gorm:"default:0"`
    TotalCommission decimal.Decimal `json:"total_commission" gorm:"type:decimal(10,2);default:0"`
    PaidCommission  decimal.Decimal `json:"paid_commission" gorm:"type:decimal(10,2);default:0"`
    PendingCommission decimal.Decimal `json:"pending_commission" gorm:"type:decimal(10,2);default:0"`
    TeamLevel       int             `json:"team_level" gorm:"default:1"`
    LastActiveAt    *time.Time      `json:"last_active_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

### 4. 提现管理系统 ✅ 已完成

#### 提现申请模型
```go
// WithdrawalRequest 提现申请模型
type WithdrawalRequest struct {
    ID              string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    Amount          decimal.Decimal `json:"amount" gorm:"type:decimal(10,2);not null"`
    Fee             decimal.Decimal `json:"fee" gorm:"type:decimal(10,2);default:0"`
    ActualAmount    decimal.Decimal `json:"actual_amount" gorm:"type:decimal(10,2);not null"`
    PaymentMethod   string          `json:"payment_method" gorm:"type:varchar(20);not null"`
    PaymentAccount  string          `json:"payment_account" gorm:"type:varchar(100);not null"`
    PaymentName     string          `json:"payment_name" gorm:"type:varchar(100);not null"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
    ProcessedAt     *time.Time      `json:"processed_at"`
    ProcessedBy     *string         `json:"processed_by" gorm:"type:varchar(50)"`
    RejectReason    *string         `json:"reject_reason" gorm:"type:text"`
    TransactionID   *string         `json:"transaction_id" gorm:"type:varchar(100)"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

#### 提现业务流程
```
提现处理业务流程：
1. 用户提交提现申请 → 2. 系统验证余额和限制 → 3. 创建提现记录
4. 管理员审核申请 → 5. 调用支付接口转账 → 6. 更新提现状态
7. 扣除用户余额 → 8. 记录财务流水 → 9. 发送处理通知
```

---

## 🎯 积分系统管理功能

### 核心设计思路
积分系统采用"积分获取 → 积分消费 → 积分兑换 → 积分等级"的完整闭环设计，支持多种积分获取方式和消费场景。

### 1. 积分获取规则 ✅ 已完成

#### 积分规则模型
```go
// PointRule 积分规则模型
type PointRule struct {
    ID          string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Name        string          `json:"name" gorm:"type:varchar(100);not null"`
    Type        string          `json:"type" gorm:"type:varchar(20);not null"` // earn, spend
    Event       string          `json:"event" gorm:"type:varchar(50);not null"` // signup, order, referral, etc.
    Points      int             `json:"points" gorm:"not null"`
    Multiplier  decimal.Decimal `json:"multiplier" gorm:"type:decimal(5,2);default:1.00"`
    MaxPoints   *int            `json:"max_points"`
    MinAmount   *decimal.Decimal `json:"min_amount" gorm:"type:decimal(10,2)"`
    DailyLimit  *int            `json:"daily_limit"`
    MonthlyLimit *int           `json:"monthly_limit"`
    Status      string          `json:"status" gorm:"type:varchar(20);default:'active'"`
    StartDate   time.Time       `json:"start_date"`
    EndDate     *time.Time      `json:"end_date"`
}
```

#### 积分获取场景
```json
// 积分获取规则配置示例
{
  "signup": {
    "points": 100,
    "description": "注册奖励",
    "limit": "once_per_user"
  },
  "daily_login": {
    "points": 10,
    "description": "每日签到",
    "limit": "once_per_day"
  },
  "order_payment": {
    "points_rate": 0.01,
    "description": "订单支付积分",
    "min_amount": 1.00
  },
  "referral_success": {
    "points": 500,
    "description": "推荐用户注册",
    "limit": "unlimited"
  },
  "share_content": {
    "points": 5,
    "description": "分享内容",
    "daily_limit": 50
  }
}
```

### 2. 用户积分管理 ✅ 已完成

#### 用户积分模型
```go
// UserPoint 用户积分模型
type UserPoint struct {
    ID          string    `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID      string    `json:"user_id" gorm:"type:varchar(50);uniqueIndex;not null"`
    Balance     int       `json:"balance" gorm:"not null;default:0"`
    TotalEarned int       `json:"total_earned" gorm:"not null;default:0"`
    TotalSpent  int       `json:"total_spent" gorm:"not null;default:0"`
    Level       int       `json:"level" gorm:"not null;default:1"`
    LevelProgress int     `json:"level_progress" gorm:"not null;default:0"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### 积分交易记录
```go
// PointTransaction 积分交易记录模型
type PointTransaction struct {
    ID          string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    UserID      string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    Type        string          `json:"type" gorm:"type:varchar(20);not null"` // earn, spend, expire
    Amount      int             `json:"amount" gorm:"not null"`
    Balance     int             `json:"balance" gorm:"not null"`
    Source      string          `json:"source" gorm:"type:varchar(50);not null"`
    SourceID    *string         `json:"source_id" gorm:"type:varchar(50)"`
    RuleID      *string         `json:"rule_id" gorm:"type:varchar(50)"`
    Description string          `json:"description" gorm:"type:varchar(200);not null"`
    ExpiresAt   *time.Time      `json:"expires_at"`
    CreatedAt   time.Time       `json:"created_at"`
}
```

### 3. 积分兑换商城 🚧 部分完成

#### 积分商品模型
```go
// PointProduct 积分商品模型
type PointProduct struct {
    ID          string    `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Name        string    `json:"name" gorm:"type:varchar(100);not null"`
    Description *string   `json:"description" gorm:"type:text"`
    Image       *string   `json:"image" gorm:"type:varchar(500)"`
    Category    string    `json:"category" gorm:"type:varchar(50);not null"`
    PointsPrice int       `json:"points_price" gorm:"not null"`
    Stock       *int      `json:"stock"`
    SoldCount   int       `json:"sold_count" gorm:"default:0"`
    ValidityDays *int     `json:"validity_days"`
    UserLimit   *int      `json:"user_limit"`
    DailyLimit  *int      `json:"daily_limit"`
    Config      JSON      `json:"config" gorm:"type:jsonb"`
    SortOrder   int       `json:"sort_order" gorm:"default:0"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    StartDate   time.Time `json:"start_date"`
    EndDate     *time.Time `json:"end_date"`
}
```

### 4. 积分等级体系 ✅ 已完成

#### 积分等级模型
```go
// PointLevel 积分等级模型
type PointLevel struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    TenantID    string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Level       int       `json:"level" gorm:"not null;uniqueIndex"`
    Name        string    `json:"name" gorm:"type:varchar(50);not null"`
    MinPoints   int       `json:"min_points" gorm:"not null"`
    MaxPoints   *int      `json:"max_points"`
    Benefits    JSON      `json:"benefits" gorm:"type:jsonb"`
    Description *string   `json:"description" gorm:"type:text"`
    Icon        *string   `json:"icon" gorm:"type:varchar(200)"`
    Color       *string   `json:"color" gorm:"type:varchar(20)"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
}
```

---

## 🛒 商城系统管理功能

### 核心设计思路
商城系统支持虚拟商品和实体商品销售，包含完整的商品管理、订单处理、库存管理和优惠券系统。

### 1. 商品管理系统 🚧 部分完成

#### 商品模型设计
```go
// Product 商品模型
type Product struct {
    ID          string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Name        string          `json:"name" gorm:"type:varchar(200);not null"`
    Description *string         `json:"description" gorm:"type:text"`
    Category    string          `json:"category" gorm:"type:varchar(50);not null"`
    Type        string          `json:"type" gorm:"type:varchar(20);not null"` // virtual, physical
    Price       decimal.Decimal `json:"price" gorm:"type:decimal(10,2);not null"`
    OriginalPrice *decimal.Decimal `json:"original_price" gorm:"type:decimal(10,2)"`
    Images      JSON            `json:"images" gorm:"type:jsonb"`
    Attributes  JSON            `json:"attributes" gorm:"type:jsonb"`
    Stock       *int            `json:"stock"`
    SoldCount   int             `json:"sold_count" gorm:"default:0"`
    Status      string          `json:"status" gorm:"type:varchar(20);default:'active'"`
    SortOrder   int             `json:"sort_order" gorm:"default:0"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
}
```

### 2. 订单处理流程 ✅ 已完成

#### 订单模型设计
```go
// Order 订单模型
type Order struct {
    ID              string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    OrderNumber     string          `json:"order_number" gorm:"type:varchar(50);uniqueIndex;not null"`
    OrderType       string          `json:"order_type" gorm:"type:varchar(20);not null"`
    ProductID       string          `json:"product_id" gorm:"type:varchar(50);not null"`
    ProductName     string          `json:"product_name" gorm:"type:varchar(200);not null"`
    ProductType     string          `json:"product_type" gorm:"type:varchar(20);not null"`
    Quantity        int             `json:"quantity" gorm:"not null;default:1"`
    UnitPrice       decimal.Decimal `json:"unit_price" gorm:"type:decimal(10,2);not null"`
    TotalAmount     decimal.Decimal `json:"total_amount" gorm:"type:decimal(10,2);not null"`
    DiscountAmount  decimal.Decimal `json:"discount_amount" gorm:"type:decimal(10,2);default:0"`
    FinalAmount     decimal.Decimal `json:"final_amount" gorm:"type:decimal(10,2);not null"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
    PaymentStatus   string          `json:"payment_status" gorm:"type:varchar(20);default:'unpaid'"`
    PaymentMethod   *string         `json:"payment_method" gorm:"type:varchar(50)"`
    CouponCode      *string         `json:"coupon_code" gorm:"type:varchar(50)"`
    Notes           *string         `json:"notes" gorm:"type:text"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

#### 订单处理流程
```
订单处理业务流程：
1. 用户选择商品 → 2. 添加到购物车 → 3. 确认订单信息
4. 选择支付方式 → 5. 应用优惠券 → 6. 创建订单记录
7. 调用支付接口 → 8. 支付成功回调 → 9. 更新订单状态
10. 扣减库存 → 11. 发放虚拟商品 → 12. 发送确认通知
```

### 3. 库存管理系统 🚧 部分完成

#### 库存记录模型
```go
// ProductStock 商品库存模型
type ProductStock struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    ProductID   string    `json:"product_id" gorm:"type:varchar(50);uniqueIndex;not null"`
    TotalStock  int       `json:"total_stock" gorm:"not null;default:0"`
    AvailableStock int    `json:"available_stock" gorm:"not null;default:0"`
    ReservedStock int     `json:"reserved_stock" gorm:"not null;default:0"`
    SoldStock   int       `json:"sold_stock" gorm:"not null;default:0"`
    LowStockThreshold *int `json:"low_stock_threshold"`
    Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 4. 优惠券系统 ✅ 已完成

#### 用户优惠券模型
```go
// UserCoupon 用户优惠券模型
type UserCoupon struct {
    ID          uint       `json:"id" gorm:"primaryKey"`
    UserID      string     `json:"user_id" gorm:"type:varchar(50);not null;index"`
    CouponID    uint       `json:"coupon_id" gorm:"not null;index"`
    Code        string     `json:"code" gorm:"type:varchar(50);not null"`
    Status      string     `json:"status" gorm:"type:varchar(20);default:'unused'"`
    UsedAt      *time.Time `json:"used_at"`
    UsedOrderID *string    `json:"used_order_id" gorm:"type:varchar(50)"`
    ExpiresAt   time.Time  `json:"expires_at"`
    CreatedAt   time.Time  `json:"created_at"`
    UpdatedAt   time.Time  `json:"updated_at"`
}
```

---

## 💳 支付系统管理功能

### 核心设计思路
支付系统采用统一支付接口设计，支持多种支付方式，包含完整的订单管理、退款处理和账单管理功能。

### 1. 支付接口集成 ✅ 已完成

#### 支付记录模型
```go
// Payment 支付记录模型
type Payment struct {
    ID              string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    OrderID         string          `json:"order_id" gorm:"type:varchar(50);not null;index"`
    PaymentNumber   string          `json:"payment_number" gorm:"type:varchar(50);uniqueIndex;not null"`
    PaymentMethod   string          `json:"payment_method" gorm:"type:varchar(20);not null"`
    PaymentProvider string          `json:"payment_provider" gorm:"type:varchar(20);not null"`
    Amount          decimal.Decimal `json:"amount" gorm:"type:decimal(10,2);not null"`
    Currency        string          `json:"currency" gorm:"type:varchar(10);default:'CNY'"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
    ProviderOrderID *string         `json:"provider_order_id" gorm:"type:varchar(100)"`
    ProviderResponse JSON           `json:"provider_response" gorm:"type:jsonb"`
    PaidAt          *time.Time      `json:"paid_at"`
    FailedAt        *time.Time      `json:"failed_at"`
    FailureReason   *string         `json:"failure_reason" gorm:"type:text"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

#### 支付方式配置
```json
// 支付方式配置示例
{
  "alipay": {
    "name": "支付宝",
    "enabled": true,
    "app_id": "2021000000000000",
    "private_key": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...",
    "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
    "gateway_url": "https://openapi.alipay.com/gateway.do"
  },
  "wechat": {
    "name": "微信支付",
    "enabled": true,
    "app_id": "wx**********123456",
    "mch_id": "**********",
    "api_key": "your_api_key_here",
    "cert_path": "/path/to/cert.pem"
  },
  "stripe": {
    "name": "Stripe",
    "enabled": false,
    "public_key": "pk_test_...",
    "secret_key": "sk_test_..."
  }
}
```

### 2. 订单管理系统 ✅ 已完成

#### 订单状态流转
```
订单状态流转图：
pending → paid → processing → completed
   ↓         ↓         ↓
cancelled  failed   refunded
```

#### 订单业务逻辑
```go
// 订单状态更新业务逻辑
func (s *PaymentService) UpdateOrderStatus(orderID, status string) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 更新订单状态
        if err := tx.Model(&models.Order{}).
            Where("id = ?", orderID).
            Update("status", status).Error; err != nil {
            return err
        }

        // 2. 根据状态执行相应业务逻辑
        switch status {
        case "paid":
            // 支付成功：分配权益、扣减库存
            return s.handleOrderPaid(tx, orderID)
        case "cancelled":
            // 订单取消：释放库存、退还优惠券
            return s.handleOrderCancelled(tx, orderID)
        case "refunded":
            // 订单退款：撤销权益、恢复库存
            return s.handleOrderRefunded(tx, orderID)
        }

        return nil
    })
}
```

### 3. 退款处理流程 ✅ 已完成

#### 退款记录模型
```go
// Refund 退款记录模型
type Refund struct {
    ID              string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID          string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    OrderID         string          `json:"order_id" gorm:"type:varchar(50);not null;index"`
    PaymentID       string          `json:"payment_id" gorm:"type:varchar(50);not null;index"`
    RefundNumber    string          `json:"refund_number" gorm:"type:varchar(50);uniqueIndex;not null"`
    RefundAmount    decimal.Decimal `json:"refund_amount" gorm:"type:decimal(10,2);not null"`
    RefundReason    string          `json:"refund_reason" gorm:"type:varchar(200);not null"`
    Status          string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
    ProviderRefundID *string        `json:"provider_refund_id" gorm:"type:varchar(100)"`
    ProcessedAt     *time.Time      `json:"processed_at"`
    ProcessedBy     *string         `json:"processed_by" gorm:"type:varchar(50)"`
    FailureReason   *string         `json:"failure_reason" gorm:"type:text"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

#### 退款处理流程
```
退款处理业务流程：
1. 用户申请退款 → 2. 系统验证退款条件 → 3. 创建退款记录
4. 管理员审核 → 5. 调用支付接口退款 → 6. 更新退款状态
7. 撤销用户权益 → 8. 恢复商品库存 → 9. 发送退款通知
```

### 4. 账单管理系统 ✅ 已完成

#### 账单记录模型
```go
// Bill 账单记录模型
type Bill struct {
    ID          string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID      string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    BillNumber  string          `json:"bill_number" gorm:"type:varchar(50);uniqueIndex;not null"`
    BillType    string          `json:"bill_type" gorm:"type:varchar(20);not null"` // order, refund, commission
    Amount      decimal.Decimal `json:"amount" gorm:"type:decimal(10,2);not null"`
    Currency    string          `json:"currency" gorm:"type:varchar(10);default:'CNY'"`
    Description string          `json:"description" gorm:"type:varchar(200);not null"`
    RelatedID   *string         `json:"related_id" gorm:"type:varchar(50)"` // 关联的订单ID、退款ID等
    Status      string          `json:"status" gorm:"type:varchar(20);default:'confirmed'"`
    BillDate    time.Time       `json:"bill_date"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
}
```

---

## 🎓 课程系统管理功能 (预留)

### 核心设计思路
课程系统为AI生态平台的教育模块，支持在线课程、学习进度跟踪、认证考试和证书颁发功能。

### 1. 课程管理系统 ❌ 未开发

#### 课程模型设计 (预留)
```go
// Course 课程模型 (预留设计)
type Course struct {
    ID          string          `json:"id" gorm:"type:varchar(50);primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    Title       string          `json:"title" gorm:"type:varchar(200);not null"`
    Description *string         `json:"description" gorm:"type:text"`
    Category    string          `json:"category" gorm:"type:varchar(50);not null"`
    Level       string          `json:"level" gorm:"type:varchar(20);not null"` // beginner, intermediate, advanced
    Price       decimal.Decimal `json:"price" gorm:"type:decimal(10,2);not null"`
    Duration    int             `json:"duration" gorm:"not null"` // 课程时长(分钟)
    CoverImage  *string         `json:"cover_image" gorm:"type:varchar(500)"`
    Tags        JSON            `json:"tags" gorm:"type:jsonb"`
    Status      string          `json:"status" gorm:"type:varchar(20);default:'draft'"`
    PublishedAt *time.Time      `json:"published_at"`
    CreatedBy   string          `json:"created_by" gorm:"type:varchar(50);not null"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
}
```

### 2. 学习进度跟踪 ❌ 未开发

### 3. 认证考试系统 ❌ 未开发

### 4. 证书颁发系统 ❌ 未开发

---

## 📊 功能完成度统计

### 已完成功能模块 (85%)

#### ✅ 完全完成 (7个模块)
1. **🔐 用户认证管理**: 100% - 注册、登录、令牌管理、权限控制
2. **💎 会员营销管理**: 95% - 等级体系、权益配置、订阅管理
3. **💰 推广赚钱管理**: 100% - 推广链接、佣金计算、团队管理、提现处理
4. **🎯 积分系统管理**: 90% - 积分规则、用户积分、等级体系
5. **💳 支付系统管理**: 100% - 支付接口、订单管理、退款处理、账单管理
6. **👤 用户资料管理**: 100% - 基础信息、扩展资料、安全设置
7. **🔒 安全验证管理**: 100% - 会话管理、设备管理、安全日志

#### 🚧 部分完成 (2个模块)
1. **🛒 商城系统管理**: 70% - 订单处理完成，商品管理和库存管理部分完成
2. **🎁 营销活动管理**: 60% - 优惠券系统完成，活动管理部分完成

### 待开发功能模块 (15%)

#### ❌ 未开发 (1个模块)
1. **🎓 课程系统管理**: 0% - 完全预留功能，未开始开发

### 数据模型统计

| 功能模块 | 数据模型数 | 已完成 | 部分完成 | 未开始 | 完成率 |
|---------|-----------|--------|----------|--------|--------|
| 🔐 用户认证 | 8 | 8 | 0 | 0 | 100% |
| 💎 会员营销 | 6 | 5 | 1 | 0 | 83% |
| 💰 推广赚钱 | 5 | 5 | 0 | 0 | 100% |
| 🎯 积分系统 | 6 | 5 | 1 | 0 | 83% |
| 🛒 商城系统 | 5 | 3 | 2 | 0 | 60% |
| 💳 支付系统 | 4 | 4 | 0 | 0 | 100% |
| 🎓 课程系统 | 6 | 0 | 0 | 6 | 0% |
| **总计** | **40** | **30** | **4** | **6** | **75%** |

---

## 🚀 技术架构优势

### 1. 微服务设计
- **职责单一**: ai-users专注用户相关业务
- **数据隔离**: 多租户数据完全隔离
- **接口标准**: RESTful API设计规范
- **扩展性强**: 支持水平扩展和功能扩展

### 2. 数据模型设计
- **关系清晰**: 外键关联，数据一致性保证
- **字段完整**: 业务字段覆盖全面
- **类型安全**: 使用decimal处理金额，避免精度问题
- **索引优化**: 关键字段建立索引，查询性能优化

### 3. 业务逻辑设计
- **事务保证**: 关键业务使用数据库事务
- **状态管理**: 完整的状态流转机制
- **错误处理**: 完善的错误处理和回滚机制
- **日志记录**: 详细的操作日志和审计跟踪

### 4. 安全性设计
- **认证授权**: JWT令牌机制，安全可靠
- **密码安全**: bcrypt哈希，盐值随机
- **会话管理**: 多设备会话，安全控制
- **数据验证**: 输入验证，防止注入攻击

---

## 📝 总结

AI生态平台ai-users服务具有以下特点：

### 功能完整性
1. **核心功能完备**: 用户认证、会员管理、支付系统等核心功能已完成
2. **业务闭环完整**: 从用户注册到付费使用的完整业务闭环
3. **营销体系成熟**: 推广、积分、优惠券等营销功能齐全
4. **扩展性良好**: 预留了课程系统等扩展功能

### 技术架构优势
1. **Go语言实现**: 高性能、高并发、内存安全
2. **微服务架构**: 职责清晰、易于维护和扩展
3. **数据模型完善**: 关系清晰、字段完整、索引优化
4. **安全性强**: 多层安全防护、数据加密、权限控制

### 业务价值体现
1. **用户体验优秀**: 注册登录流畅、会员权益清晰
2. **商业模式成熟**: 会员订阅、推广分销、积分营销
3. **运营支持完善**: 数据统计、用户分析、财务管理
4. **扩展能力强**: 支持多租户、多语言、多币种

### 设计思路亮点
1. **配额组装思路**: 灵活的权益配置和会员等级设计
2. **多级分销模式**: 完整的推广营销和佣金计算体系
3. **积分闭环设计**: 获取、消费、兑换、等级的完整积分体系
4. **统一支付接口**: 支持多种支付方式的统一管理

ai-users服务为AI生态平台提供了坚实的用户基础层支撑，支撑平台的商业化运营和用户增长。