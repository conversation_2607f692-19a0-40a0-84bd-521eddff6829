# 🚀 AI生态平台战略级双模式推广佣金体系完整开发方案

## 📊 项目概述

### 🎯 核心业务逻辑重新定义

基于您提出的4个关键业务要求，重新设计推广佣金体系：

#### 1. **充值与消费分离原则**
```
用户充值1000元 → 无佣金产生
用户消费100元购买智能体 → 开始计算推广佣金
```

#### 2. **多业务线佣金计算模式**
```
智能体市场：按次/按时间/一次性授权 → 即时佣金结算
AI插件工具：按次/按量/按时间 → 即时佣金结算
大模型API：按使用量计费 → 按天批量佣金结算
会员卡销售：一次性结算 → 即时佣金结算（包含API配额）
```

#### 3. **会员卡特殊处理机制**
```
夏季论文会员卡100元 = Claude4配额100万tokens + 论文智能体1年
佣金计算：按100元一次性结算，不按API使用量分摊
```

#### 4. **双重佣金计算模式**
```
比例佣金模式：按订单金额百分比计算
智能体A售价100元 → 推广用户B:30元(30%) + 推广用户A:10元(10%) + 代理商:8元(8%)

固定金额佣金模式：按预设固定金额分配
智能体A售价100元 → 推广用户B:20元(固定) + 推广用户A:8元(固定) + 代理商:12元(固定)
```

#### 5. **产品级佣金配置**
```
智能体上架：可设置该智能体的专属佣金规则（比例或固定金额）
AI插件上架：可设置该插件的专属佣金规则（比例或固定金额）
大模型API渠道：可设置该模型的专属佣金规则（比例或固定金额）
```

### 🏗️ 双模式推广体系架构

### 🔑 租户ID管理体系

#### 1. 租户ID生成规范
```sql
-- 租户ID生成规则
CREATE TABLE tenant_id_sequences (
    id SERIAL PRIMARY KEY,
    tenant_prefix VARCHAR(10) NOT NULL DEFAULT '2025',    -- 年份前缀
    business_type VARCHAR(20) NOT NULL,                   -- 业务类型标识
    current_sequence INTEGER NOT NULL DEFAULT 1,          -- 当前序号
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_prefix, business_type)
);

-- 租户ID生成示例
INSERT INTO tenant_id_sequences (tenant_prefix, business_type, current_sequence) VALUES
('2025', 'CeesAiDr', 716),     -- 2025CeesAiDr0716 (AI医生代理商)
('2025', 'CeesAgent', 1),      -- 2025CeesAgent0001 (智能体代理商)
('2025', 'CeesPlugin', 1),     -- 2025CeesPlugin0001 (插件代理商)
('2025', 'CeesApi', 1);        -- 2025CeesApi0001 (API代理商)

-- 租户ID格式：{年份}{业务标识}{4位序号}
-- 示例：2025CeesAiDr0716, 2025CeesAgent0001
```

#### 2. 推广员ID生成规范
```sql
-- 推广员ID序列表
CREATE TABLE referrer_id_sequences (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,                       -- 租户ID
    current_sequence INTEGER NOT NULL DEFAULT 10000,      -- 当前序号（从10001开始）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id)
);

-- 推广员ID生成函数
CREATE OR REPLACE FUNCTION generate_referrer_id(p_tenant_id VARCHAR(16))
RETURNS VARCHAR(20) AS $$
DECLARE
    next_sequence INTEGER;
    referrer_id VARCHAR(20);
BEGIN
    -- 获取并更新序号
    UPDATE referrer_id_sequences
    SET current_sequence = current_sequence + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE tenant_id = p_tenant_id
    RETURNING current_sequence INTO next_sequence;

    -- 如果租户不存在，创建新记录
    IF next_sequence IS NULL THEN
        INSERT INTO referrer_id_sequences (tenant_id, current_sequence)
        VALUES (p_tenant_id, 10001)
        RETURNING current_sequence INTO next_sequence;
    END IF;

    -- 生成推广员ID：租户ID + 推广员序号
    referrer_id := p_tenant_id || '_REF' || LPAD(next_sequence::TEXT, 5, '0');

    RETURN referrer_id;
END;
$$ LANGUAGE plpgsql;

-- 推广员ID格式示例：
-- 2025CeesAiDr0716_REF10001
-- 2025CeesAiDr0716_REF10002
-- 2025CeesAgent0001_REF10001
```

#### 3. 租户隔离机制
```sql
-- 租户信息表
CREATE TABLE tenants (
    tenant_id VARCHAR(16) PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(50) NOT NULL,                   -- 'ai_doctor', 'agent_market', 'plugin_tools', 'api_service'
    agent_level INTEGER NOT NULL,                         -- 1=一级代理商, 2=二级代理商
    referral_mode INTEGER NOT NULL DEFAULT 1,             -- 推广模式

    -- 联系信息
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),

    -- 业务配置
    domain VARCHAR(100),                                   -- 专属域名
    logo_url VARCHAR(200),                                -- 品牌Logo
    theme_color VARCHAR(10) DEFAULT '#1976D2',           -- 主题色

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',                  -- 'active', 'suspended', 'inactive'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX(business_type),
    INDEX(agent_level),
    INDEX(status)
);

-- 租户示例数据
INSERT INTO tenants (tenant_id, tenant_name, business_type, agent_level, contact_person, domain) VALUES
('2025CeesAiDr0716', 'AI医生智能助手', 'ai_doctor', 1, '张医生', 'aidoctor.cees.cc'),
('2025CeesAgent0001', '智能体应用商店', 'agent_market', 2, '李经理', 'agents.cees.cc'),
('2025CeesPlugin0001', 'AI插件工具平台', 'plugin_tools', 1, '王总监', 'plugins.cees.cc'),
('2025CeesApi0001', '大模型API服务', 'api_service', 2, '赵工程师', 'api.cees.cc');
```

### 🏢 双模式推广体系架构

#### 模型1：一级代理商模式（3级推广链）
```
租户：2025CeesAiDr0716 (AI医生代理商)
一级代理商推广体系：
├── SAAS代理商 (tenant_id: 2025CeesAiDr0716) - 团队销售佣金 8% + 缺失层级佣金
├── 推广员A (referrer_id: 2025CeesAiDr0716_REF10001) - 二级推广佣金 10%
├── 推广员B (referrer_id: 2025CeesAiDr0716_REF10002) - 一级推广佣金 30%
└── 消费者C (user_id: 2025CeesAiDr0716_USER00001) - 实际消费用户

限制：代理商不能推广代理商，只能推广用户
推广链接：https://aidoctor.cees.cc?ref=2025CeesAiDr0716_REF10002&mode=1
```

#### 模型2：二级代理商模式（5级推广链）
```
租户：2025CeesAgent0001 (智能体代理商)
二级代理商推广体系：
├── 合伙人代理商A (tenant_id: 2025CeesAgent0001) - 总销售佣金 3%
├── SAAS代理商B (tenant_id: 2025CeesAgent0002) - 团队销售佣金 5% + 缺失层级佣金
├── 推广员A (referrer_id: 2025CeesAgent0002_REF10001) - 二级推广佣金 10%
├── 推广员B (referrer_id: 2025CeesAgent0002_REF10002) - 一级推广佣金 30%
└── 消费者C (user_id: 2025CeesAgent0002_USER00001) - 实际消费用户

权限：代理商可以推广代理商，也可以推广用户
推广链接：https://agents.cees.cc?ref=2025CeesAgent0002_REF10002&mode=2
```

## 🔗 全站推广链接体系设计

### 🎯 核心设计原则

#### 1. 全站推广链接机制
```
设计原则：平台的每一个页面链接都必须包含租户ID和用户身份信息
目标：确保推广员复制任何页面链接都能获得推广佣金
成功率：100%的推广关系绑定成功率
```

#### 2. URL结构设计规范
```
基础URL格式：
https://{tenant_domain}/{page_path}?tenant={tenant_id}&ref={referrer_id}&uid={user_id}

示例URL：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001&uid=2025CeesAiDr0716_USER00001

参数说明：
- tenant: 租户ID（必需）
- ref: 推广员ID（可选，用于推广关系绑定）
- uid: 当前用户ID（必需，用于权限验证）
```

#### 3. 不同用户角色的URL格式

##### 代理商访问URL
```
代理商管理后台：
https://aidoctor.cees.cc/admin/dashboard?tenant=2025CeesAiDr0716&uid=2025CeesAiDr0716_ADMIN

代理商查看智能体：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&uid=2025CeesAiDr0716_ADMIN

代理商分享链接（自动包含推广信息）：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716&uid=2025CeesAiDr0716_ADMIN
```

##### 推广员访问URL
```
推广员个人中心：
https://aidoctor.cees.cc/referrer/dashboard?tenant=2025CeesAiDr0716&uid=2025CeesAiDr0716_REF10001

推广员查看智能体：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001&uid=2025CeesAiDr0716_REF10001

推广员分享链接（自动包含推广信息）：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001&uid=2025CeesAiDr0716_REF10001
```

##### 普通用户访问URL
```
用户注册页面：
https://aidoctor.cees.cc/register?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001

用户登录后访问：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&uid=2025CeesAiDr0716_USER00001

用户分享链接（保持推广关系）：
https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001&uid=2025CeesAiDr0716_USER00001
```

### 🔧 技术实现方案

#### 1. 前端路由中间件
```javascript
// 全站URL参数处理中间件
class URLParameterMiddleware {
    constructor() {
        this.tenantId = null;
        this.referrerId = null;
        this.userId = null;
    }

    // 初始化URL参数
    initializeFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        this.tenantId = urlParams.get('tenant');
        this.referrerId = urlParams.get('ref');
        this.userId = urlParams.get('uid');

        // 存储到localStorage，确保页面跳转时保持
        if (this.tenantId) localStorage.setItem('tenant_id', this.tenantId);
        if (this.referrerId) localStorage.setItem('referrer_id', this.referrerId);
        if (this.userId) localStorage.setItem('user_id', this.userId);
    }

    // 获取当前参数
    getCurrentParams() {
        return {
            tenant: this.tenantId || localStorage.getItem('tenant_id'),
            ref: this.referrerId || localStorage.getItem('referrer_id'),
            uid: this.userId || localStorage.getItem('user_id')
        };
    }

    // 生成带参数的URL
    generateURL(path, additionalParams = {}) {
        const params = this.getCurrentParams();
        const urlParams = new URLSearchParams();

        // 添加必需参数
        if (params.tenant) urlParams.set('tenant', params.tenant);
        if (params.uid) urlParams.set('uid', params.uid);

        // 添加推广参数（如果存在）
        if (params.ref) urlParams.set('ref', params.ref);

        // 添加额外参数
        Object.keys(additionalParams).forEach(key => {
            urlParams.set(key, additionalParams[key]);
        });

        return `${path}?${urlParams.toString()}`;
    }

    // 更新浏览器URL（不刷新页面）
    updateBrowserURL(path) {
        const newURL = this.generateURL(path);
        window.history.replaceState({}, '', newURL);
    }
}

// 全局实例
const urlManager = new URLParameterMiddleware();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    urlManager.initializeFromURL();
});

// 路由跳转拦截器
function navigateTo(path, additionalParams = {}) {
    const fullURL = urlManager.generateURL(path, additionalParams);
    window.location.href = fullURL;
}

// 链接生成器
function generateShareLink(path) {
    return urlManager.generateURL(path);
}
```

#### 2. 后端路由中间件
```go
// URLParameterMiddleware 全站URL参数中间件
type URLParameterMiddleware struct {
    tenantService   *TenantService
    referrerService *ReferrerService
}

// ProcessURLParameters 处理URL参数中间件
func (m *URLParameterMiddleware) ProcessURLParameters() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 提取URL参数
        tenantID := c.Query("tenant")
        referrerID := c.Query("ref")
        userID := c.Query("uid")

        // 2. 验证租户ID
        if tenantID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "缺少租户ID参数"})
            c.Abort()
            return
        }

        // 验证租户是否存在
        tenant, err := m.tenantService.GetTenant(tenantID)
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "无效的租户ID"})
            c.Abort()
            return
        }

        // 3. 验证用户ID（如果提供）
        if userID != "" {
            if !m.validateUserBelongsToTenant(userID, tenantID) {
                c.JSON(http.StatusForbidden, gin.H{"error": "用户不属于该租户"})
                c.Abort()
                return
            }
        }

        // 4. 处理推广关系绑定
        if referrerID != "" && userID != "" {
            // 验证推广员是否属于该租户
            if m.validateReferrerBelongsToTenant(referrerID, tenantID) {
                // 绑定推广关系（如果用户还没有推广关系）
                m.bindReferralRelationship(referrerID, userID, tenantID, c)
            }
        }

        // 5. 设置上下文变量
        c.Set("tenant_id", tenantID)
        c.Set("tenant", tenant)
        c.Set("referrer_id", referrerID)
        c.Set("user_id", userID)

        // 6. 继续处理请求
        c.Next()
    }
}

// validateUserBelongsToTenant 验证用户是否属于租户
func (m *URLParameterMiddleware) validateUserBelongsToTenant(userID, tenantID string) bool {
    return strings.HasPrefix(userID, tenantID+"_")
}

// validateReferrerBelongsToTenant 验证推广员是否属于租户
func (m *URLParameterMiddleware) validateReferrerBelongsToTenant(referrerID, tenantID string) bool {
    return strings.HasPrefix(referrerID, tenantID+"_REF") || referrerID == tenantID
}

// bindReferralRelationship 绑定推广关系
func (m *URLParameterMiddleware) bindReferralRelationship(referrerID, userID, tenantID string, c *gin.Context) {
    // 检查用户是否已有推广关系
    existingRelation, err := m.referrerService.GetUserReferralRelation(userID)
    if err == nil && existingRelation != nil {
        return // 已有推广关系，不重复绑定
    }

    // 创建新的推广关系
    relation := &ReferralRelationship{
        TenantID:         tenantID,
        ReferrerID:       referrerID,
        ReferredUserID:   userID,
        ReferredUserType: "user",
        ReferralSource:   "url_link",
        ReferralIP:       c.ClientIP(),
        ReferralUA:       c.GetHeader("User-Agent"),
        Status:           "active",
    }

    m.referrerService.CreateReferralRelationship(relation)
}
```

#### 3. 全站链接生成服务
```go
// URLGeneratorService 全站链接生成服务
type URLGeneratorService struct {
    baseURL string
}

// GenerateUserURL 为用户生成带参数的URL
func (s *URLGeneratorService) GenerateUserURL(tenantID, userID, path string, params map[string]string) string {
    u, _ := url.Parse(s.baseURL + path)
    q := u.Query()

    // 必需参数
    q.Set("tenant", tenantID)
    q.Set("uid", userID)

    // 如果用户是推广员，自动添加推广参数
    if strings.Contains(userID, "_REF") {
        q.Set("ref", userID)
    }

    // 添加额外参数
    for key, value := range params {
        q.Set(key, value)
    }

    u.RawQuery = q.Encode()
    return u.String()
}

// GenerateReferralURL 生成推广链接
func (s *URLGeneratorService) GenerateReferralURL(tenantID, referrerID, path string) string {
    u, _ := url.Parse(s.baseURL + path)
    q := u.Query()

    q.Set("tenant", tenantID)
    q.Set("ref", referrerID)

    u.RawQuery = q.Encode()
    return u.String()
}

// GenerateShareURL 生成分享链接（保持推广关系）
func (s *URLGeneratorService) GenerateShareURL(tenantID, currentUserID, path string) string {
    u, _ := url.Parse(s.baseURL + path)
    q := u.Query()

    q.Set("tenant", tenantID)
    q.Set("uid", currentUserID)

    // 如果当前用户是推广员，添加推广参数
    if strings.Contains(currentUserID, "_REF") {
        q.Set("ref", currentUserID)
    } else {
        // 如果是普通用户，查找其推广员并保持推广关系
        referrerID := s.getUserReferrer(currentUserID)
        if referrerID != "" {
            q.Set("ref", referrerID)
        }
    }

    u.RawQuery = q.Encode()
    return u.String()
}
```

#### 4. 前端组件实现

##### 全站导航组件
```javascript
// NavigationComponent.js - 全站导航组件
class NavigationComponent {
    constructor() {
        this.urlManager = new URLParameterMiddleware();
    }

    // 渲染导航菜单
    renderNavigation() {
        const params = this.urlManager.getCurrentParams();
        const userType = this.getUserType(params.uid);

        return `
            <nav class="md-navigation-bar">
                ${this.renderNavItems(userType, params)}
            </nav>
        `;
    }

    // 根据用户类型渲染导航项
    renderNavItems(userType, params) {
        const baseItems = [
            { path: '/dashboard', label: '首页', icon: 'home' },
            { path: '/agents', label: '智能体', icon: 'smart_toy' },
            { path: '/plugins', label: '插件', icon: 'extension' },
            { path: '/api', label: 'API', icon: 'api' }
        ];

        let additionalItems = [];

        if (userType === 'referrer') {
            additionalItems = [
                { path: '/referrer/dashboard', label: '推广中心', icon: 'share' },
                { path: '/referrer/earnings', label: '佣金收益', icon: 'payments' },
                { path: '/referrer/team', label: '我的团队', icon: 'people' }
            ];
        } else if (userType === 'admin') {
            additionalItems = [
                { path: '/admin/dashboard', label: '管理后台', icon: 'admin_panel_settings' },
                { path: '/admin/referrers', label: '推广员管理', icon: 'people' },
                { path: '/admin/commission', label: '佣金管理', icon: 'payments' }
            ];
        }

        const allItems = [...baseItems, ...additionalItems];

        return allItems.map(item => `
            <md-navigation-tab href="${this.urlManager.generateURL(item.path)}">
                <md-icon slot="icon">${item.icon}</md-icon>
                ${item.label}
            </md-navigation-tab>
        `).join('');
    }

    // 判断用户类型
    getUserType(uid) {
        if (!uid) return 'guest';
        if (uid.includes('_REF')) return 'referrer';
        if (uid.includes('_ADMIN')) return 'admin';
        if (uid.includes('_USER')) return 'user';
        return 'guest';
    }
}
```

##### 分享链接组件
```javascript
// ShareLinkComponent.js - 分享链接组件
class ShareLinkComponent {
    constructor() {
        this.urlManager = new URLParameterMiddleware();
    }

    // 渲染分享按钮
    renderShareButton(targetPath) {
        return `
            <md-filled-button class="share-button" onclick="shareLink('${targetPath}')">
                <md-icon slot="icon">share</md-icon>
                分享链接
            </md-filled-button>
        `;
    }

    // 分享链接功能
    shareLink(targetPath) {
        const shareURL = this.urlManager.generateURL(targetPath);

        // 显示分享对话框
        this.showShareDialog(shareURL);
    }

    // 显示分享对话框
    showShareDialog(shareURL) {
        const dialog = document.createElement('md-dialog');
        dialog.innerHTML = `
            <div slot="headline">分享链接</div>
            <div slot="content">
                <md-outlined-text-field
                    label="分享链接"
                    value="${shareURL}"
                    readonly>
                    <md-icon-button slot="trailing-icon" onclick="copyToClipboard('${shareURL}')">
                        <md-icon>content_copy</md-icon>
                    </md-icon-button>
                </md-outlined-text-field>

                <div class="share-options">
                    <md-text-button onclick="shareToWeChat('${shareURL}')">
                        <md-icon slot="icon">chat</md-icon>
                        微信分享
                    </md-text-button>
                    <md-text-button onclick="shareToQQ('${shareURL}')">
                        <md-icon slot="icon">forum</md-icon>
                        QQ分享
                    </md-text-button>
                    <md-text-button onclick="generateQRCode('${shareURL}')">
                        <md-icon slot="icon">qr_code</md-icon>
                        生成二维码
                    </md-text-button>
                </div>
            </div>
            <div slot="actions">
                <md-text-button onclick="closeDialog()">关闭</md-text-button>
            </div>
        `;

        document.body.appendChild(dialog);
        dialog.show();
    }

    // 复制到剪贴板
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('链接已复制到剪贴板');
        });
    }

    // 显示提示消息
    showToast(message) {
        const toast = document.createElement('md-snackbar');
        toast.textContent = message;
        document.body.appendChild(toast);
        toast.show();

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }
}
```

### 👥 用户ID生成规范
```sql
-- 用户ID序列表
CREATE TABLE user_id_sequences (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,                       -- 租户ID
    current_sequence INTEGER NOT NULL DEFAULT 0,          -- 当前序号（从1开始）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id)
);

-- 用户ID生成函数
CREATE OR REPLACE FUNCTION generate_user_id(p_tenant_id VARCHAR(16))
RETURNS VARCHAR(25) AS $$
DECLARE
    next_sequence INTEGER;
    user_id VARCHAR(25);
BEGIN
    -- 获取并更新序号
    UPDATE user_id_sequences
    SET current_sequence = current_sequence + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE tenant_id = p_tenant_id
    RETURNING current_sequence INTO next_sequence;

    -- 如果租户不存在，创建新记录
    IF next_sequence IS NULL THEN
        INSERT INTO user_id_sequences (tenant_id, current_sequence)
        VALUES (p_tenant_id, 1)
        RETURNING current_sequence INTO next_sequence;
    END IF;

    -- 生成用户ID：租户ID + 用户序号
    user_id := p_tenant_id || '_USER' || LPAD(next_sequence::TEXT, 5, '0');

    RETURN user_id;
END;
$$ LANGUAGE plpgsql;

-- 用户ID格式示例：
-- 2025CeesAiDr0716_USER00001
-- 2025CeesAiDr0716_USER00002
-- 2025CeesAgent0001_USER00001
```

## 🔐 推广权限管理体系设计

### 🎯 核心业务逻辑

#### 1. 推广权限控制原则
```
权限控制：只有开通推广权限的用户才能获得推广佣金
角色定义：普通用户 → 申请推广权限 → 审核通过 → 成为推广员
佣金归属：未开通推广权限的层级佣金归平台所有
权限管理：管理员可在用户列表中设置用户的推广权限
```

#### 2. 推广链路佣金分配规则
```
5级推广链路：代理商A → 代理商B → 推广用户A → 推广用户B → 用户C

情况1：所有层级都开通推广权限
- 代理商A：获得总销售佣金 3%
- 代理商B：获得团队销售佣金 5%
- 推广用户A：获得二级推广佣金 10%
- 推广用户B：获得一级推广佣金 30%

情况2：推广用户A未开通推广权限
- 代理商A：获得总销售佣金 3%
- 代理商B：获得团队销售佣金 5% + 推广用户A的佣金 10%（归平台后重新分配）
- 推广用户A：无佣金（权限未开通）
- 推广用户B：获得一级推广佣金 30%
```

### 📊 用户推广权限管理表
```sql
-- 用户推广权限表
CREATE TABLE user_referral_permissions (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,                       -- 租户ID
    user_id VARCHAR(25) NOT NULL,                         -- 用户ID

    -- 权限状态
    referral_enabled BOOLEAN DEFAULT FALSE,               -- 是否开通推广权限
    permission_status VARCHAR(20) DEFAULT 'disabled',     -- 'disabled', 'pending', 'approved', 'rejected', 'suspended'

    -- 申请信息
    application_reason TEXT,                              -- 申请理由
    applied_at TIMESTAMP,                                 -- 申请时间

    -- 审核信息
    reviewed_by VARCHAR(50),                              -- 审核人
    reviewed_at TIMESTAMP,                                -- 审核时间
    review_comment TEXT,                                  -- 审核备注

    -- 推广员信息（审核通过后生成）
    referrer_id VARCHAR(20),                              -- 推广员ID
    referral_code VARCHAR(20),                            -- 推广码

    -- 权限配置
    can_promote_users BOOLEAN DEFAULT TRUE,               -- 是否可以推广用户
    can_promote_agents BOOLEAN DEFAULT FALSE,             -- 是否可以推广代理商
    max_referral_levels INTEGER DEFAULT 2,                -- 最大推广层级

    -- 佣金配置
    commission_rate_override DECIMAL(5,4),                -- 佣金率覆盖（可选）
    commission_enabled BOOLEAN DEFAULT TRUE,              -- 是否启用佣金

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    UNIQUE(tenant_id, user_id),
    UNIQUE(referrer_id),
    UNIQUE(referral_code),
    INDEX(tenant_id),
    INDEX(user_id),
    INDEX(referral_enabled),
    INDEX(permission_status),
    INDEX(referrer_id)
);
```

### 📊 推广员信息表（升级版）
```sql
-- 推广员信息表（基于权限表的视图）
CREATE TABLE referrers (
    id SERIAL PRIMARY KEY,
    referrer_id VARCHAR(20) NOT NULL UNIQUE,              -- 推广员ID
    tenant_id VARCHAR(16) NOT NULL,                       -- 所属租户
    user_id VARCHAR(25) NOT NULL,                         -- 关联用户ID

    -- 基本信息
    referrer_name VARCHAR(50) NOT NULL,                   -- 推广员姓名
    referrer_phone VARCHAR(20),                           -- 手机号
    referrer_email VARCHAR(100),                          -- 邮箱

    -- 推广信息
    referral_code VARCHAR(20) NOT NULL UNIQUE,            -- 推广码（短码）
    referral_link TEXT,                                   -- 推广链接
    qr_code_url VARCHAR(200),                             -- 二维码链接

    -- 权限信息
    permission_status VARCHAR(20) DEFAULT 'approved',     -- 权限状态
    referral_enabled BOOLEAN DEFAULT TRUE,                -- 推广权限是否启用
    commission_enabled BOOLEAN DEFAULT TRUE,              -- 佣金权限是否启用

    -- 层级信息
    referrer_level INTEGER DEFAULT 1,                     -- 推广员等级
    parent_referrer_id VARCHAR(20),                       -- 上级推广员ID
    referral_path TEXT,                                   -- 推广路径

    -- 统计信息
    total_referrals INTEGER DEFAULT 0,                    -- 总推广人数
    total_orders INTEGER DEFAULT 0,                       -- 总订单数
    total_commission DECIMAL(12,2) DEFAULT 0,             -- 总佣金收入

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',                  -- 'active', 'suspended', 'inactive'
    approved_at TIMESTAMP,                                -- 审核通过时间
    approved_by VARCHAR(50),                              -- 审核人

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    FOREIGN KEY (user_id, tenant_id) REFERENCES user_referral_permissions(user_id, tenant_id),
    INDEX(tenant_id),
    INDEX(user_id),
    INDEX(referrer_id),
    INDEX(referral_code),
    INDEX(permission_status),
    INDEX(referral_enabled),
    INDEX(commission_enabled),
    INDEX(parent_referrer_id),
    INDEX(status)
);

-- 推广权限示例数据
INSERT INTO user_referral_permissions (tenant_id, user_id, referral_enabled, permission_status, referrer_id, referral_code, reviewed_by, reviewed_at) VALUES
('2025CeesAiDr0716', '2025CeesAiDr0716_USER00001', TRUE, 'approved', '2025CeesAiDr0716_REF10001', 'AIDOC001', 'admin', NOW()),
('2025CeesAiDr0716', '2025CeesAiDr0716_USER00002', TRUE, 'approved', '2025CeesAiDr0716_REF10002', 'AIDOC002', 'admin', NOW()),
('2025CeesAiDr0716', '2025CeesAiDr0716_USER00003', FALSE, 'disabled', NULL, NULL, NULL, NULL),
('2025CeesAgent0001', '2025CeesAgent0001_USER00001', TRUE, 'approved', '2025CeesAgent0001_REF10001', 'AGENT001', 'admin', NOW());

-- 推广员示例数据
INSERT INTO referrers (referrer_id, tenant_id, user_id, referrer_name, referral_code, permission_status, referral_enabled, commission_enabled) VALUES
('2025CeesAiDr0716_REF10001', '2025CeesAiDr0716', '2025CeesAiDr0716_USER00001', '张推广员', 'AIDOC001', 'approved', TRUE, TRUE),
('2025CeesAiDr0716_REF10002', '2025CeesAiDr0716', '2025CeesAiDr0716_USER00002', '李推广员', 'AIDOC002', 'approved', TRUE, TRUE),
('2025CeesAgent0001_REF10001', '2025CeesAgent0001', '2025CeesAgent0001_USER00001', '王推广员', 'AGENT001', 'approved', TRUE, TRUE);
```

## 🔧 推广权限管理服务

### 1. 推广权限管理服务
```go
// ReferralPermissionService 推广权限管理服务
type ReferralPermissionService struct {
    db *gorm.DB
}

// NewReferralPermissionService 创建推广权限管理服务
func NewReferralPermissionService(db *gorm.DB) *ReferralPermissionService {
    return &ReferralPermissionService{db: db}
}

// ApplyReferralPermission 申请推广权限
func (s *ReferralPermissionService) ApplyReferralPermission(
    tenantID, userID, reason string) error {

    // 检查是否已有权限记录
    var existing UserReferralPermission
    err := s.db.Where("tenant_id = ? AND user_id = ?", tenantID, userID).First(&existing).Error

    if err == gorm.ErrRecordNotFound {
        // 创建新的权限申请
        permission := &UserReferralPermission{
            TenantID:           tenantID,
            UserID:             userID,
            ReferralEnabled:    false,
            PermissionStatus:   "pending",
            ApplicationReason:  &reason,
            AppliedAt:          &time.Time{},
        }
        *permission.AppliedAt = time.Now()

        return s.db.Create(permission).Error
    } else if err != nil {
        return err
    }

    // 更新现有记录为申请状态
    if existing.PermissionStatus == "disabled" || existing.PermissionStatus == "rejected" {
        updates := map[string]interface{}{
            "permission_status":   "pending",
            "application_reason":  reason,
            "applied_at":         time.Now(),
            "updated_at":         time.Now(),
        }
        return s.db.Model(&existing).Updates(updates).Error
    }

    return fmt.Errorf("用户已有有效的推广权限申请")
}

// ApproveReferralPermission 审核通过推广权限
func (s *ReferralPermissionService) ApproveReferralPermission(
    tenantID, userID, reviewerID, comment string) error {

    var permission UserReferralPermission
    err := s.db.Where("tenant_id = ? AND user_id = ? AND permission_status = 'pending'",
        tenantID, userID).First(&permission).Error
    if err != nil {
        return fmt.Errorf("未找到待审核的权限申请: %v", err)
    }

    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 生成推广员ID和推广码
        referrerID, err := s.generateReferrerID(tenantID)
        if err != nil {
            return err
        }

        referralCode, err := s.generateReferralCode(tenantID)
        if err != nil {
            return err
        }

        // 2. 更新权限状态
        updates := map[string]interface{}{
            "referral_enabled":   true,
            "permission_status":  "approved",
            "referrer_id":       referrerID,
            "referral_code":     referralCode,
            "reviewed_by":       reviewerID,
            "reviewed_at":       time.Now(),
            "review_comment":    comment,
            "updated_at":        time.Now(),
        }

        if err := tx.Model(&permission).Updates(updates).Error; err != nil {
            return err
        }

        // 3. 创建推广员记录
        referrer := &Referrer{
            ReferrerID:        referrerID,
            TenantID:          tenantID,
            UserID:            userID,
            ReferralCode:      referralCode,
            PermissionStatus:  "approved",
            ReferralEnabled:   true,
            CommissionEnabled: true,
            Status:           "active",
            ApprovedAt:       &time.Time{},
            ApprovedBy:       &reviewerID,
        }
        *referrer.ApprovedAt = time.Now()

        return tx.Create(referrer).Error
    })
}

// RejectReferralPermission 拒绝推广权限申请
func (s *ReferralPermissionService) RejectReferralPermission(
    tenantID, userID, reviewerID, comment string) error {

    updates := map[string]interface{}{
        "permission_status": "rejected",
        "reviewed_by":      reviewerID,
        "reviewed_at":      time.Now(),
        "review_comment":   comment,
        "updated_at":       time.Now(),
    }

    return s.db.Model(&UserReferralPermission{}).
        Where("tenant_id = ? AND user_id = ? AND permission_status = 'pending'", tenantID, userID).
        Updates(updates).Error
}

// ToggleReferralPermission 切换推广权限状态
func (s *ReferralPermissionService) ToggleReferralPermission(
    tenantID, userID string, enabled bool) error {

    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 更新权限表
        updates := map[string]interface{}{
            "referral_enabled": enabled,
            "permission_status": func() string {
                if enabled {
                    return "approved"
                }
                return "suspended"
            }(),
            "updated_at": time.Now(),
        }

        err := tx.Model(&UserReferralPermission{}).
            Where("tenant_id = ? AND user_id = ?", tenantID, userID).
            Updates(updates).Error
        if err != nil {
            return err
        }

        // 2. 更新推广员表
        return tx.Model(&Referrer{}).
            Where("tenant_id = ? AND user_id = ?", tenantID, userID).
            Updates(map[string]interface{}{
                "referral_enabled": enabled,
                "status": func() string {
                    if enabled {
                        return "active"
                    }
                    return "suspended"
                }(),
                "updated_at": time.Now(),
            }).Error
    })
}

// CheckReferralPermission 检查用户是否有推广权限
func (s *ReferralPermissionService) CheckReferralPermission(tenantID, userID string) (bool, error) {
    var permission UserReferralPermission
    err := s.db.Where("tenant_id = ? AND user_id = ? AND referral_enabled = true AND permission_status = 'approved'",
        tenantID, userID).First(&permission).Error

    if err == gorm.ErrRecordNotFound {
        return false, nil
    } else if err != nil {
        return false, err
    }

    return true, nil
}

// generateReferrerID 生成推广员ID
func (s *ReferralPermissionService) generateReferrerID(tenantID string) (string, error) {
    var sequence ReferrerIDSequence
    err := s.db.Where("tenant_id = ?", tenantID).First(&sequence).Error

    if err == gorm.ErrRecordNotFound {
        // 创建新的序列
        sequence = ReferrerIDSequence{
            TenantID:        tenantID,
            CurrentSequence: 10001,
        }
        if err := s.db.Create(&sequence).Error; err != nil {
            return "", err
        }
    } else if err != nil {
        return "", err
    } else {
        // 更新序列
        sequence.CurrentSequence++
        if err := s.db.Save(&sequence).Error; err != nil {
            return "", err
        }
    }

    return fmt.Sprintf("%s_REF%05d", tenantID, sequence.CurrentSequence), nil
}

// generateReferralCode 生成推广码
func (s *ReferralPermissionService) generateReferralCode(tenantID string) (string, error) {
    // 生成6位随机推广码
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    code := make([]byte, 6)

    for {
        for i := range code {
            code[i] = charset[rand.Intn(len(charset))]
        }

        codeStr := string(code)

        // 检查是否重复
        var existing UserReferralPermission
        err := s.db.Where("referral_code = ?", codeStr).First(&existing).Error
        if err == gorm.ErrRecordNotFound {
            return codeStr, nil
        } else if err != nil {
            return "", err
        }
        // 如果重复，继续循环生成新的
    }
}
```

### 2. 佣金权限验证服务
```go
// CommissionPermissionValidator 佣金权限验证服务
type CommissionPermissionValidator struct {
    db                    *gorm.DB
    permissionService     *ReferralPermissionService
}

// NewCommissionPermissionValidator 创建佣金权限验证服务
func NewCommissionPermissionValidator(
    db *gorm.DB,
    permissionService *ReferralPermissionService) *CommissionPermissionValidator {
    return &CommissionPermissionValidator{
        db:                db,
        permissionService: permissionService,
    }
}

// ValidateCommissionChain 验证佣金链权限
func (v *CommissionPermissionValidator) ValidateCommissionChain(
    tenantID, consumerUserID string) (*CommissionChainValidation, error) {

    // 1. 查找用户的推广链
    var chain DualModeReferralChain
    err := v.db.Where("consumer_user_id = ? AND consumer_tenant_id = ?",
        consumerUserID, tenantID).First(&chain).Error
    if err != nil {
        return &CommissionChainValidation{
            HasReferralChain: false,
            ValidLevels:     []CommissionLevel{},
        }, nil
    }

    validation := &CommissionChainValidation{
        HasReferralChain: true,
        ValidLevels:     []CommissionLevel{},
        InvalidLevels:   []CommissionLevel{},
    }

    // 2. 验证每个层级的权限
    levels := []struct {
        Level      int
        Type       *string
        ID         *string
        TenantID   *string
    }{
        {1, chain.Level1Type, chain.Level1ID, chain.Level1TenantID},
        {2, chain.Level2Type, chain.Level2ID, chain.Level2TenantID},
        {3, chain.Level3Type, chain.Level3ID, chain.Level3TenantID},
        {4, chain.Level4Type, chain.Level4ID, chain.Level4TenantID},
        {5, chain.Level5Type, chain.Level5ID, chain.Level5TenantID},
    }

    for _, level := range levels {
        if level.Type == nil || level.ID == nil || level.TenantID == nil {
            continue
        }

        commissionLevel := CommissionLevel{
            Level:    level.Level,
            Type:     *level.Type,
            ID:       *level.ID,
            TenantID: *level.TenantID,
        }

        if *level.Type == "user" {
            // 验证用户推广权限
            hasPermission, err := v.permissionService.CheckReferralPermission(*level.TenantID, *level.ID)
            if err != nil {
                commissionLevel.ValidationError = err.Error()
                validation.InvalidLevels = append(validation.InvalidLevels, commissionLevel)
                continue
            }

            if hasPermission {
                commissionLevel.HasPermission = true
                validation.ValidLevels = append(validation.ValidLevels, commissionLevel)
            } else {
                commissionLevel.HasPermission = false
                commissionLevel.ValidationError = "用户未开通推广权限"
                validation.InvalidLevels = append(validation.InvalidLevels, commissionLevel)
            }
        } else if *level.Type == "agent" {
            // 代理商默认有权限
            commissionLevel.HasPermission = true
            validation.ValidLevels = append(validation.ValidLevels, commissionLevel)
        }
    }

    return validation, nil
}

// CalculateCommissionWithPermissionValidation 带权限验证的佣金计算
func (v *CommissionPermissionValidator) CalculateCommissionWithPermissionValidation(
    req *CommissionCalculationRequest) ([]*DualModeCommissionAllocation, error) {

    // 1. 验证佣金链权限
    validation, err := v.ValidateCommissionChain(req.TenantID, req.UserID)
    if err != nil {
        return nil, fmt.Errorf("佣金链权限验证失败: %v", err)
    }

    if !validation.HasReferralChain {
        return []*DualModeCommissionAllocation{}, nil // 无推广链，无佣金
    }

    // 2. 获取佣金配置
    config, err := v.getCommissionConfig(req)
    if err != nil {
        return nil, fmt.Errorf("获取佣金配置失败: %v", err)
    }

    // 3. 计算有权限层级的佣金
    allocations := make([]*DualModeCommissionAllocation, 0)
    platformCommission := decimal.Zero

    for _, level := range validation.ValidLevels {
        if level.HasPermission {
            // 计算该层级佣金
            allocation := v.calculateLevelCommission(req, &level, config)
            if allocation != nil {
                allocations = append(allocations, allocation)
            }
        }
    }

    // 4. 计算无权限层级的佣金（归平台）
    for _, level := range validation.InvalidLevels {
        if !level.HasPermission {
            // 计算应该给该层级的佣金，但归平台所有
            commission := v.calculateLevelCommissionAmount(req, &level, config)
            platformCommission = platformCommission.Add(commission)
        }
    }

    // 5. 如果有平台佣金，创建平台佣金记录
    if platformCommission.GreaterThan(decimal.Zero) {
        platformAllocation := &DualModeCommissionAllocation{
            OrderID:             req.OrderID,
            OrderAmount:         req.Amount,
            ConsumerUserID:      req.UserID,
            ConsumerTenantID:    req.TenantID,
            ReferralMode:        validation.ReferralMode,
            BeneficiaryType:     "platform",
            BeneficiaryID:       "PLATFORM",
            BeneficiaryTenantID: req.TenantID,
            CommissionLevel:     0,
            CommissionType:      "platform_reclaim",
            CommissionRate:      decimal.Zero,
            CommissionAmount:    platformCommission,
            Status:              "confirmed",
            BusinessLine:        req.BusinessLine,
            OrderType:           req.OrderType,
            CalculationMode:     config.CalculationMode,
        }
        allocations = append(allocations, platformAllocation)
    }

    return allocations, nil
}

// CommissionChainValidation 佣金链验证结果
type CommissionChainValidation struct {
    HasReferralChain bool              `json:"has_referral_chain"`
    ReferralMode     int               `json:"referral_mode"`
    ValidLevels      []CommissionLevel `json:"valid_levels"`
    InvalidLevels    []CommissionLevel `json:"invalid_levels"`
}

// CommissionLevel 佣金层级信息
type CommissionLevel struct {
    Level           int    `json:"level"`
    Type            string `json:"type"`           // "user", "agent"
    ID              string `json:"id"`
    TenantID        string `json:"tenant_id"`
    HasPermission   bool   `json:"has_permission"`
    ValidationError string `json:"validation_error,omitempty"`
}
```

### 3. 升级佣金计算服务
```go
// 升级BusinessLineCommissionService，集成权限验证
func (s *BusinessLineCommissionService) CalculateCommissionWithPermission(
    req *CommissionCalculationRequest) error {

    // 1. 检查订单是否符合佣金计算条件
    if !s.isCommissionEligible(req) {
        return nil
    }

    // 2. 使用权限验证器计算佣金
    validator := NewCommissionPermissionValidator(s.db, s.permissionService)
    allocations, err := validator.CalculateCommissionWithPermissionValidation(req)
    if err != nil {
        return fmt.Errorf("权限验证佣金计算失败: %v", err)
    }

    // 3. 保存佣金分配记录
    return s.db.Transaction(func(tx *gorm.DB) error {
        for _, allocation := range allocations {
            if err := tx.Create(allocation).Error; err != nil {
                return err
            }
        }

        // 4. 更新订单佣金计算状态
        return tx.Model(&models.Order{}).
            Where("id = ?", req.OrderID).
            Updates(map[string]interface{}{
                "commission_calculated": true,
                "commission_settlement_date": time.Now().Format("2006-01-02"),
            }).Error
    })
}
```

### 🔗 推广关系管理表
```sql
-- 推广关系表
CREATE TABLE referral_relationships (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,                       -- 租户ID
    referrer_id VARCHAR(20) NOT NULL,                     -- 推广员ID
    referred_user_id VARCHAR(25) NOT NULL,                -- 被推广用户ID
    referred_user_type VARCHAR(20) NOT NULL,              -- 被推广用户类型：'user', 'referrer'

    -- 推广信息
    referral_code VARCHAR(20) NOT NULL,                   -- 使用的推广码
    referral_source VARCHAR(50),                          -- 推广来源：'link', 'qr_code', 'manual'
    referral_ip VARCHAR(45),                              -- 推广IP
    referral_ua TEXT,                                     -- 用户代理

    -- 转化信息
    first_order_id VARCHAR(50),                           -- 首次订单ID
    first_order_amount DECIMAL(10,2),                     -- 首次订单金额
    first_order_time TIMESTAMP,                           -- 首次下单时间

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',                  -- 'active', 'invalid'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    FOREIGN KEY (referrer_id) REFERENCES referrers(referrer_id),
    INDEX(tenant_id),
    INDEX(referrer_id),
    INDEX(referred_user_id),
    INDEX(referral_code),
    INDEX(status)
);
```

### 🔄 推广关系绑定流程

#### 1. 访客追踪表
```sql
-- 推广访客追踪表
CREATE TABLE referral_visits (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,                       -- 租户ID
    referrer_id VARCHAR(20) NOT NULL,                     -- 推广员ID
    visitor_id VARCHAR(50) NOT NULL,                      -- 访客ID（临时）

    -- 访问信息
    visitor_ip VARCHAR(45) NOT NULL,                      -- 访客IP
    user_agent TEXT,                                      -- 用户代理
    visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,       -- 访问时间
    landing_page TEXT,                                    -- 落地页面

    -- 转化信息
    referred_user_id VARCHAR(25),                         -- 转化后的用户ID
    converted_at TIMESTAMP,                               -- 转化时间
    first_order_id VARCHAR(50),                           -- 首次订单ID

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending',                 -- 'pending', 'converted', 'expired'
    expires_at TIMESTAMP,                                 -- 过期时间（7天）

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    FOREIGN KEY (referrer_id) REFERENCES referrers(referrer_id),
    INDEX(tenant_id),
    INDEX(referrer_id),
    INDEX(visitor_id),
    INDEX(visitor_ip),
    INDEX(status),
    INDEX(expires_at)
);
```

#### 2. 用户访问流程
```
步骤1：推广员分享任意页面链接
链接格式：https://aidoctor.cees.cc/agents/detail/AGENT_001?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001&uid=2025CeesAiDr0716_REF10001

步骤2：新用户点击链接访问
系统自动提取：tenant=2025CeesAiDr0716, ref=2025CeesAiDr0716_REF10001
生成访客ID：VISITOR_20240115_001
记录到referral_visits表，设置7天过期时间

步骤3：用户注册时自动绑定推广关系
注册URL：https://aidoctor.cees.cc/register?tenant=2025CeesAiDr0716&ref=2025CeesAiDr0716_REF10001
系统查找访客记录，创建正式推广关系

步骤4：用户购买时自动计算佣金
购买URL：https://aidoctor.cees.cc/purchase/agent/AGENT_001?tenant=2025CeesAiDr0716&uid=2025CeesAiDr0716_USER00001
系统查找用户的推广关系，自动计算佣金给推广员
```

#### 3. 推广关系持久化服务
```go
// ReferralRelationshipPersistence 推广关系持久化
type ReferralRelationshipPersistence struct {
    db *gorm.DB
}

// BindReferralFromURL 从URL绑定推广关系
func (p *ReferralRelationshipPersistence) BindReferralFromURL(
    tenantID, referrerID, visitorIP, userAgent, landingPage string) string {

    // 1. 生成临时访客ID
    visitorID := p.generateVisitorID(tenantID, visitorIP)

    // 2. 记录访问记录
    visit := &ReferralVisit{
        TenantID:    tenantID,
        ReferrerID:  referrerID,
        VisitorID:   visitorID,
        VisitorIP:   visitorIP,
        UserAgent:   userAgent,
        LandingPage: landingPage,
        VisitTime:   time.Now(),
        ExpiresAt:   time.Now().Add(7 * 24 * time.Hour), // 7天过期
        Status:      "pending",
    }

    p.db.Create(visit)

    return visitorID
}

// ConvertVisitorToUser 访客转换为用户时绑定推广关系
func (p *ReferralRelationshipPersistence) ConvertVisitorToUser(
    visitorID, userID string) error {

    // 1. 查找访客的推广记录
    var visit ReferralVisit
    err := p.db.Where("visitor_id = ? AND status = 'pending' AND expires_at > ?",
        visitorID, time.Now()).First(&visit).Error
    if err != nil {
        return nil // 没有有效的推广记录，正常注册
    }

    // 2. 检查用户是否已有推广关系
    var existingRelation ReferralRelationship
    err = p.db.Where("referred_user_id = ?", userID).First(&existingRelation).Error
    if err == nil {
        return nil // 用户已有推广关系，不重复绑定
    }

    // 3. 创建正式的推广关系
    relationship := &ReferralRelationship{
        TenantID:         visit.TenantID,
        ReferrerID:       visit.ReferrerID,
        ReferredUserID:   userID,
        ReferredUserType: "user",
        ReferralSource:   "url_link",
        ReferralIP:       visit.VisitorIP,
        ReferralUA:       visit.UserAgent,
        Status:           "active",
    }

    // 4. 事务处理
    return p.db.Transaction(func(tx *gorm.DB) error {
        // 创建推广关系
        if err := tx.Create(relationship).Error; err != nil {
            return err
        }

        // 更新访问记录状态
        return tx.Model(&visit).Updates(map[string]interface{}{
            "referred_user_id": userID,
            "status":          "converted",
            "converted_at":    time.Now(),
        }).Error
    })
}

// generateVisitorID 生成访客ID
func (p *ReferralRelationshipPersistence) generateVisitorID(tenantID, visitorIP string) string {
    timestamp := time.Now().Format("20060102_150405")
    hash := fmt.Sprintf("%x", md5.Sum([]byte(tenantID+visitorIP+timestamp)))
    return fmt.Sprintf("VISITOR_%s_%s", timestamp, hash[:8])
}

// CleanExpiredVisits 清理过期的访客记录
func (p *ReferralRelationshipPersistence) CleanExpiredVisits() error {
    return p.db.Where("status = 'pending' AND expires_at < ?", time.Now()).
        Updates(map[string]interface{}{
            "status":     "expired",
            "updated_at": time.Now(),
        }).Error
}
```

### 🎯 100%推广关系绑定保证机制

#### 1. 多重绑定策略
```go
// MultipleBindingStrategy 多重绑定策略
type MultipleBindingStrategy struct {
    persistence *ReferralRelationshipPersistence
}

// EnsureReferralBinding 确保推广关系绑定
func (s *MultipleBindingStrategy) EnsureReferralBinding(
    tenantID, referrerID, userID, visitorIP, userAgent string) error {

    // 策略1：URL参数绑定
    if referrerID != "" {
        err := s.bindFromURLParameter(tenantID, referrerID, userID)
        if err == nil {
            return nil
        }
    }

    // 策略2：Cookie绑定
    err := s.bindFromCookie(tenantID, userID, visitorIP)
    if err == nil {
        return nil
    }

    // 策略3：IP地址绑定（24小时内）
    err = s.bindFromIPAddress(tenantID, userID, visitorIP)
    if err == nil {
        return nil
    }

    // 策略4：设备指纹绑定
    err = s.bindFromDeviceFingerprint(tenantID, userID, userAgent)
    if err == nil {
        return nil
    }

    return nil // 无推广关系，正常注册
}

// bindFromURLParameter 从URL参数绑定
func (s *MultipleBindingStrategy) bindFromURLParameter(
    tenantID, referrerID, userID string) error {

    // 验证推广员是否存在且属于该租户
    var referrer Referrer
    err := s.persistence.db.Where("referrer_id = ? AND tenant_id = ? AND status = 'active'",
        referrerID, tenantID).First(&referrer).Error
    if err != nil {
        return err
    }

    // 创建推广关系
    relationship := &ReferralRelationship{
        TenantID:         tenantID,
        ReferrerID:       referrerID,
        ReferredUserID:   userID,
        ReferredUserType: "user",
        ReferralSource:   "url_parameter",
        Status:           "active",
    }

    return s.persistence.db.Create(relationship).Error
}

// bindFromCookie 从Cookie绑定
func (s *MultipleBindingStrategy) bindFromCookie(
    tenantID, userID, visitorIP string) error {

    // 查找最近的访客记录
    var visit ReferralVisit
    err := s.persistence.db.Where("tenant_id = ? AND visitor_ip = ? AND status = 'pending' AND expires_at > ?",
        tenantID, visitorIP, time.Now()).
        Order("visit_time DESC").
        First(&visit).Error
    if err != nil {
        return err
    }

    // 转换访客为用户
    return s.persistence.ConvertVisitorToUser(visit.VisitorID, userID)
}
```

#### 2. 前端Cookie管理
```javascript
// CookieManager.js - Cookie管理
class CookieManager {
    // 设置推广Cookie
    setReferralCookie(tenantId, referrerId, visitorId) {
        const cookieData = {
            tenant: tenantId,
            referrer: referrerId,
            visitor: visitorId,
            timestamp: Date.now()
        };

        // 设置7天过期的Cookie
        const expires = new Date();
        expires.setDate(expires.getDate() + 7);

        document.cookie = `referral_data=${JSON.stringify(cookieData)}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
    }

    // 获取推广Cookie
    getReferralCookie() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'referral_data') {
                try {
                    return JSON.parse(decodeURIComponent(value));
                } catch (e) {
                    return null;
                }
            }
        }
        return null;
    }

    // 清除推广Cookie
    clearReferralCookie() {
        document.cookie = 'referral_data=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }
}
```

## 🏗️ 业务线佣金计算体系设计

### 1. 订单类型分类体系
```sql
-- 订单类型枚举
CREATE TYPE order_type_enum AS ENUM (
    'recharge',           -- 充值订单（不参与佣金）
    'agent_purchase',     -- 智能体购买（即时佣金）
    'plugin_purchase',    -- 插件购买（即时佣金）
    'api_consumption',    -- API消费（按天批量佣金）
    'membership_card',    -- 会员卡购买（即时佣金）
    'quota_package'       -- 配额包购买（即时佣金）
);

-- 佣金结算模式枚举
CREATE TYPE commission_settlement_mode_enum AS ENUM (
    'immediate',          -- 即时结算
    'daily_batch',        -- 按天批量结算
    'no_commission'       -- 不参与佣金
);

-- 佣金计算模式枚举
CREATE TYPE commission_calculation_mode_enum AS ENUM (
    'percentage',         -- 按比例计算佣金
    'fixed_amount'        -- 按固定金额计算佣金
);
```

### 2. 业务线佣金配置表（升级版）
```sql
-- 业务线佣金配置表
CREATE TABLE business_line_commission_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    business_line VARCHAR(50) NOT NULL,        -- 'agent_market', 'plugin_tools', 'llm_api', 'membership_card'
    order_type order_type_enum NOT NULL,
    settlement_mode commission_settlement_mode_enum NOT NULL,
    referral_mode INTEGER NOT NULL,            -- 1=一级代理商模式, 2=二级代理商模式
    calculation_mode commission_calculation_mode_enum DEFAULT 'percentage', -- 佣金计算模式

    -- 比例佣金配置
    user_direct_rate DECIMAL(5,4) DEFAULT 0.3000,      -- 用户一级推广佣金率 30%
    user_indirect_rate DECIMAL(5,4) DEFAULT 0.1000,    -- 用户二级推广佣金率 10%
    agent_team_rate DECIMAL(5,4),                      -- 代理商团队佣金率
    agent_total_rate DECIMAL(5,4),                     -- 合伙人总佣金率

    -- 固定金额佣金配置
    user_direct_amount DECIMAL(10,2),                  -- 用户一级推广固定佣金金额
    user_indirect_amount DECIMAL(10,2),                -- 用户二级推广固定佣金金额
    agent_team_amount DECIMAL(10,2),                   -- 代理商团队固定佣金金额
    agent_total_amount DECIMAL(10,2),                  -- 合伙人总固定佣金金额

    -- 特殊配置
    min_commission_amount DECIMAL(10,2) DEFAULT 0.01,  -- 最小佣金金额
    max_commission_amount DECIMAL(10,2),               -- 最大佣金金额
    commission_delay_hours INTEGER DEFAULT 0,          -- 佣金延迟发放小时数

    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, business_line, order_type, referral_mode),
    INDEX(tenant_id),
    INDEX(business_line),
    INDEX(order_type),
    INDEX(settlement_mode),
    INDEX(calculation_mode)
);

-- 插入默认配置
INSERT INTO business_line_commission_configs (tenant_id, business_line, order_type, settlement_mode, referral_mode, agent_team_rate, agent_total_rate, description) VALUES
-- 智能体市场配置
('default', 'agent_market', 'agent_purchase', 'immediate', 1, 0.0800, NULL, '一级代理商模式-智能体购买即时佣金'),
('default', 'agent_market', 'agent_purchase', 'immediate', 2, 0.0500, 0.0300, '二级代理商模式-智能体购买即时佣金'),

-- AI插件工具配置
('default', 'plugin_tools', 'plugin_purchase', 'immediate', 1, 0.0800, NULL, '一级代理商模式-插件购买即时佣金'),
('default', 'plugin_tools', 'plugin_purchase', 'immediate', 2, 0.0500, 0.0300, '二级代理商模式-插件购买即时佣金'),

-- 大模型API配置
('default', 'llm_api', 'api_consumption', 'daily_batch', 1, 0.0800, NULL, '一级代理商模式-API消费按天批量佣金'),
('default', 'llm_api', 'api_consumption', 'daily_batch', 2, 0.0500, 0.0300, '二级代理商模式-API消费按天批量佣金'),

-- 会员卡销售配置
('default', 'membership_card', 'membership_card', 'immediate', 1, 0.0800, NULL, '一级代理商模式-会员卡销售即时佣金'),
('default', 'membership_card', 'membership_card', 'immediate', 2, 0.0500, 0.0300, '二级代理商模式-会员卡销售即时佣金'),

-- 充值订单配置（不参与佣金）
('default', 'recharge', 'recharge', 'no_commission', 1, 0.0000, NULL, '充值订单不参与佣金分配'),
('default', 'recharge', 'recharge', 'no_commission', 2, 0.0000, 0.0000, '充值订单不参与佣金分配');
```

### 3. 产品级佣金配置表（新增）
```sql
-- 产品级佣金配置表
CREATE TABLE product_commission_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    product_type VARCHAR(50) NOT NULL,         -- 'agent', 'plugin', 'api_model'
    product_id VARCHAR(100) NOT NULL,          -- 产品ID（智能体ID、插件ID、模型ID）
    product_name VARCHAR(200) NOT NULL,        -- 产品名称

    -- 佣金配置开关
    commission_enabled BOOLEAN DEFAULT FALSE,  -- 是否启用佣金
    calculation_mode commission_calculation_mode_enum DEFAULT 'percentage', -- 佣金计算模式
    referral_mode INTEGER NOT NULL,            -- 1=一级代理商模式, 2=二级代理商模式

    -- 比例佣金配置
    user_direct_rate DECIMAL(5,4),             -- 用户一级推广佣金率
    user_indirect_rate DECIMAL(5,4),           -- 用户二级推广佣金率
    agent_team_rate DECIMAL(5,4),              -- 代理商团队佣金率
    agent_total_rate DECIMAL(5,4),             -- 合伙人总佣金率

    -- 固定金额佣金配置
    user_direct_amount DECIMAL(10,2),          -- 用户一级推广固定佣金金额
    user_indirect_amount DECIMAL(10,2),        -- 用户二级推广固定佣金金额
    agent_team_amount DECIMAL(10,2),           -- 代理商团队固定佣金金额
    agent_total_amount DECIMAL(10,2),          -- 合伙人总固定佣金金额

    -- 特殊配置
    min_order_amount DECIMAL(10,2),            -- 最小订单金额（低于此金额不参与佣金）
    max_commission_per_order DECIMAL(10,2),    -- 单笔订单最大佣金金额
    commission_valid_days INTEGER,             -- 佣金有效天数

    -- 创建者信息
    created_by VARCHAR(50),                    -- 创建者ID（产品上架者）
    approved_by VARCHAR(50),                   -- 审核者ID
    approved_at TIMESTAMP,                     -- 审核时间

    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, product_type, product_id, referral_mode),
    INDEX(tenant_id),
    INDEX(product_type),
    INDEX(product_id),
    INDEX(commission_enabled),
    INDEX(calculation_mode),
    INDEX(referral_mode),
    INDEX(created_by)
);

-- 产品佣金配置示例数据
INSERT INTO product_commission_configs (tenant_id, product_type, product_id, product_name, commission_enabled, calculation_mode, referral_mode, user_direct_amount, user_indirect_amount, agent_team_amount, agent_total_amount, created_by) VALUES
-- 智能体固定金额佣金示例
('2025CeesAiDr0716', 'agent', 'AGENT_001', '论文写作助手', TRUE, 'fixed_amount', 1, 20.00, 8.00, 12.00, NULL, 'admin'),
('2025CeesAiDr0716', 'agent', 'AGENT_001', '论文写作助手', TRUE, 'fixed_amount', 2, 20.00, 8.00, 10.00, 5.00, 'admin'),

-- AI插件比例佣金示例
('2025CeesAiDr0716', 'plugin', 'PLUGIN_001', '短视频生成工具', TRUE, 'percentage', 1, NULL, NULL, NULL, NULL, 'admin'),
('2025CeesAiDr0716', 'plugin', 'PLUGIN_001', '短视频生成工具', TRUE, 'percentage', 2, NULL, NULL, NULL, NULL, 'admin'),

-- 大模型API固定金额佣金示例
('2025CeesAiDr0716', 'api_model', 'claude-4-sonnet', 'Claude 4 Sonnet', TRUE, 'fixed_amount', 1, 0.50, 0.20, 0.30, NULL, 'admin'),
('2025CeesAiDr0716', 'api_model', 'claude-4-sonnet', 'Claude 4 Sonnet', TRUE, 'fixed_amount', 2, 0.50, 0.20, 0.25, 0.15, 'admin');
```

### 4. 升级订单表支持产品级佣金
```sql
-- 升级现有orders表
ALTER TABLE orders ADD COLUMN business_line VARCHAR(50);
ALTER TABLE orders ADD COLUMN settlement_mode commission_settlement_mode_enum DEFAULT 'immediate';
ALTER TABLE orders ADD COLUMN commission_eligible BOOLEAN DEFAULT TRUE;
ALTER TABLE orders ADD COLUMN commission_calculated BOOLEAN DEFAULT FALSE;
ALTER TABLE orders ADD COLUMN commission_settlement_date DATE;

-- 新增产品级佣金相关字段
ALTER TABLE orders ADD COLUMN product_type VARCHAR(50);           -- 产品类型
ALTER TABLE orders ADD COLUMN product_id VARCHAR(100);            -- 产品ID
ALTER TABLE orders ADD COLUMN product_name VARCHAR(200);          -- 产品名称
ALTER TABLE orders ADD COLUMN commission_mode VARCHAR(20);        -- 佣金模式：'default', 'product_custom'
ALTER TABLE orders ADD COLUMN calculation_mode commission_calculation_mode_enum; -- 佣金计算模式

-- 添加索引
CREATE INDEX idx_orders_business_line ON orders(business_line);
CREATE INDEX idx_orders_settlement_mode ON orders(settlement_mode);
CREATE INDEX idx_orders_commission_eligible ON orders(commission_eligible);
CREATE INDEX idx_orders_commission_calculated ON orders(commission_calculated);
CREATE INDEX idx_orders_commission_settlement_date ON orders(commission_settlement_date);
CREATE INDEX idx_orders_product_type ON orders(product_type);
CREATE INDEX idx_orders_product_id ON orders(product_id);
CREATE INDEX idx_orders_commission_mode ON orders(commission_mode);
CREATE INDEX idx_orders_calculation_mode ON orders(calculation_mode);

-- 更新现有数据
UPDATE orders SET
    business_line = CASE
        WHEN order_type = 'recharge' THEN 'recharge'
        WHEN order_type = 'membership' THEN 'membership_card'
        WHEN product_type = 'agent' THEN 'agent_market'
        WHEN product_type = 'plugin' THEN 'plugin_tools'
        WHEN product_type = 'api' THEN 'llm_api'
        ELSE 'unknown'
    END,
    settlement_mode = CASE
        WHEN order_type = 'recharge' THEN 'no_commission'
        WHEN product_type = 'api' THEN 'daily_batch'
        ELSE 'immediate'
    END,
    commission_eligible = CASE
        WHEN order_type = 'recharge' THEN FALSE
        ELSE TRUE
    END,
    commission_mode = 'default',               -- 默认使用业务线配置
    calculation_mode = 'percentage'            -- 默认按比例计算
WHERE business_line IS NULL;
```

### 5. API使用量日统计表（升级版）
```sql
-- API使用量日统计表
CREATE TABLE daily_api_usage_stats (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    tenant_id VARCHAR(16) NOT NULL,
    usage_date DATE NOT NULL,

    -- API使用统计
    model_name VARCHAR(100) NOT NULL,
    total_requests INTEGER DEFAULT 0,
    total_input_tokens BIGINT DEFAULT 0,
    total_output_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,

    -- 计费信息
    input_token_price DECIMAL(8,6),            -- 输入token单价
    output_token_price DECIMAL(8,6),           -- 输出token单价
    request_price DECIMAL(8,4),                -- 请求单价

    -- 产品级佣金配置
    commission_mode VARCHAR(20) DEFAULT 'default',     -- 佣金模式：'default', 'product_custom'
    calculation_mode commission_calculation_mode_enum DEFAULT 'percentage', -- 佣金计算模式

    -- 佣金相关
    commission_calculated BOOLEAN DEFAULT FALSE,
    commission_settlement_date DATE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, tenant_id, usage_date, model_name),
    INDEX(user_id),
    INDEX(tenant_id),
    INDEX(usage_date),
    INDEX(model_name),
    INDEX(commission_mode),
    INDEX(calculation_mode),
    INDEX(commission_calculated),
    INDEX(commission_settlement_date)
);
```

### 6. 会员卡组装信息表（升级版）
```sql
-- 会员卡组装信息表
CREATE TABLE membership_card_compositions (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    card_id VARCHAR(50) NOT NULL,              -- 会员卡ID
    card_name VARCHAR(100) NOT NULL,           -- 会员卡名称
    card_price DECIMAL(10,2) NOT NULL,         -- 会员卡价格

    -- 组装的权益信息
    benefit_id VARCHAR(50) NOT NULL,           -- 权益ID
    benefit_name VARCHAR(100) NOT NULL,        -- 权益名称
    benefit_type VARCHAR(50) NOT NULL,         -- 权益类型：'agent_quota', 'api_quota', 'plugin_quota', 'promotion_quota'

    -- 配额信息
    quota_id VARCHAR(50),                      -- 配额ID
    quota_type VARCHAR(50),                    -- 配额类型
    quota_amount BIGINT,                       -- 配额数量
    quota_unit VARCHAR(20),                    -- 配额单位：'times', 'tokens', 'days', 'months', 'years'
    quota_value DECIMAL(10,2),                 -- 配额价值（用于佣金计算参考）

    -- 佣金配置
    is_api_quota BOOLEAN DEFAULT FALSE,        -- 是否为API配额
    commission_mode VARCHAR(20) DEFAULT 'lump_sum', -- 佣金模式：'lump_sum'=一次性, 'usage_based'=按使用量
    calculation_mode commission_calculation_mode_enum DEFAULT 'percentage', -- 佣金计算模式

    -- 会员卡级别的佣金配置（可选）
    card_commission_enabled BOOLEAN DEFAULT FALSE,  -- 是否启用会员卡级别佣金配置
    card_user_direct_rate DECIMAL(5,4),            -- 会员卡用户一级推广佣金率
    card_user_indirect_rate DECIMAL(5,4),          -- 会员卡用户二级推广佣金率
    card_agent_team_rate DECIMAL(5,4),             -- 会员卡代理商团队佣金率
    card_agent_total_rate DECIMAL(5,4),            -- 会员卡合伙人总佣金率

    card_user_direct_amount DECIMAL(10,2),         -- 会员卡用户一级推广固定佣金金额
    card_user_indirect_amount DECIMAL(10,2),       -- 会员卡用户二级推广固定佣金金额
    card_agent_team_amount DECIMAL(10,2),          -- 会员卡代理商团队固定佣金金额
    card_agent_total_amount DECIMAL(10,2),         -- 会员卡合伙人总固定佣金金额

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX(tenant_id),
    INDEX(card_id),
    INDEX(benefit_id),
    INDEX(quota_id),
    INDEX(benefit_type),
    INDEX(is_api_quota),
    INDEX(commission_mode),
    INDEX(calculation_mode),
    INDEX(card_commission_enabled)
);
```

## 🔧 Go服务层设计

### 1. 业务线佣金计算服务
```go
// BusinessLineCommissionService 业务线佣金计算服务
type BusinessLineCommissionService struct {
    db    *gorm.DB
    redis *redis.Client
}

// NewBusinessLineCommissionService 创建业务线佣金计算服务
func NewBusinessLineCommissionService(db *gorm.DB, redis *redis.Client) *BusinessLineCommissionService {
    return &BusinessLineCommissionService{
        db:    db,
        redis: redis,
    }
}

// CommissionCalculationRequest 佣金计算请求
type CommissionCalculationRequest struct {
    OrderID         string          `json:"order_id"`
    UserID          string          `json:"user_id"`
    TenantID        string          `json:"tenant_id"`
    BusinessLine    string          `json:"business_line"`
    OrderType       string          `json:"order_type"`
    Amount          decimal.Decimal `json:"amount"`
    ProductInfo     map[string]interface{} `json:"product_info"`

    // 产品级佣金配置
    ProductType     string          `json:"product_type"`     -- 'agent', 'plugin', 'api_model'
    ProductID       string          `json:"product_id"`       -- 产品ID
    ProductName     string          `json:"product_name"`     -- 产品名称
    CommissionMode  string          `json:"commission_mode"`  -- 'default', 'product_custom'
    CalculationMode string          `json:"calculation_mode"` -- 'percentage', 'fixed_amount'
}

// CalculateCommission 计算业务线佣金（升级版）
func (s *BusinessLineCommissionService) CalculateCommission(req *CommissionCalculationRequest) error {
    // 1. 检查订单是否符合佣金计算条件
    if !s.isCommissionEligible(req) {
        return nil // 不符合条件，无需计算佣金
    }

    // 2. 获取佣金配置（优先产品级配置，其次业务线配置）
    config, err := s.getCommissionConfig(req)
    if err != nil {
        return fmt.Errorf("获取佣金配置失败: %v", err)
    }

    // 3. 根据结算模式处理佣金
    switch config.SettlementMode {
    case "immediate":
        return s.calculateImmediateCommission(req, config)
    case "daily_batch":
        return s.recordDailyUsage(req, config)
    case "no_commission":
        return nil // 不参与佣金
    default:
        return fmt.Errorf("未知的结算模式: %s", config.SettlementMode)
    }
}

// getCommissionConfig 获取佣金配置（产品级优先）
func (s *BusinessLineCommissionService) getCommissionConfig(req *CommissionCalculationRequest) (*CommissionConfig, error) {
    // 1. 如果指定使用产品级配置，优先查询产品级配置
    if req.CommissionMode == "product_custom" && req.ProductType != "" && req.ProductID != "" {
        productConfig, err := s.getProductCommissionConfig(req.TenantID, req.ProductType, req.ProductID)
        if err == nil && productConfig.CommissionEnabled {
            return s.convertProductConfigToCommissionConfig(productConfig), nil
        }
    }

    // 2. 使用业务线默认配置
    businessConfig, err := s.getBusinessLineConfig(req.TenantID, req.BusinessLine, req.OrderType)
    if err != nil {
        return nil, err
    }

    return s.convertBusinessConfigToCommissionConfig(businessConfig), nil
}

// getProductCommissionConfig 获取产品级佣金配置
func (s *BusinessLineCommissionService) getProductCommissionConfig(
    tenantID, productType, productID string) (*ProductCommissionConfig, error) {

    var config ProductCommissionConfig
    err := s.db.Where("tenant_id = ? AND product_type = ? AND product_id = ? AND is_active = true",
        tenantID, productType, productID).First(&config).Error
    if err != nil {
        return nil, fmt.Errorf("产品级佣金配置不存在: %v", err)
    }

    return &config, nil
}

// CommissionConfig 统一佣金配置接口
type CommissionConfig struct {
    SettlementMode   string
    CalculationMode  string
    ReferralMode     int

    // 比例佣金配置
    UserDirectRate   decimal.Decimal
    UserIndirectRate decimal.Decimal
    AgentTeamRate    decimal.Decimal
    AgentTotalRate   *decimal.Decimal

    // 固定金额佣金配置
    UserDirectAmount   *decimal.Decimal
    UserIndirectAmount *decimal.Decimal
    AgentTeamAmount    *decimal.Decimal
    AgentTotalAmount   *decimal.Decimal

    MinCommissionAmount decimal.Decimal
    MaxCommissionAmount *decimal.Decimal
}

// convertProductConfigToCommissionConfig 转换产品配置为统一配置
func (s *BusinessLineCommissionService) convertProductConfigToCommissionConfig(
    productConfig *ProductCommissionConfig) *CommissionConfig {

    config := &CommissionConfig{
        SettlementMode:   "immediate", // 产品级配置默认即时结算
        CalculationMode:  productConfig.CalculationMode,
        ReferralMode:     productConfig.ReferralMode,
        UserDirectRate:   productConfig.UserDirectRate,
        UserIndirectRate: productConfig.UserIndirectRate,
        AgentTeamRate:    productConfig.AgentTeamRate,
    }

    if productConfig.AgentTotalRate != nil {
        config.AgentTotalRate = productConfig.AgentTotalRate
    }

    if productConfig.UserDirectAmount != nil {
        config.UserDirectAmount = productConfig.UserDirectAmount
    }

    if productConfig.UserIndirectAmount != nil {
        config.UserIndirectAmount = productConfig.UserIndirectAmount
    }

    if productConfig.AgentTeamAmount != nil {
        config.AgentTeamAmount = productConfig.AgentTeamAmount
    }

    if productConfig.AgentTotalAmount != nil {
        config.AgentTotalAmount = productConfig.AgentTotalAmount
    }

    return config
}

// isCommissionEligible 检查是否符合佣金计算条件
func (s *BusinessLineCommissionService) isCommissionEligible(req *CommissionCalculationRequest) bool {
    // 1. 充值订单不参与佣金
    if req.OrderType == "recharge" {
        return false
    }

    // 2. 金额必须大于0
    if req.Amount.LessThanOrEqual(decimal.Zero) {
        return false
    }

    // 3. 检查用户是否有推广关系
    var chain DualModeReferralChain
    err := s.db.Where("consumer_user_id = ?", req.UserID).First(&chain).Error
    if err != nil {
        return false // 无推广关系，无需计算佣金
    }

    return true
}

// calculateImmediateCommission 计算即时佣金
func (s *BusinessLineCommissionService) calculateImmediateCommission(
    req *CommissionCalculationRequest, config *BusinessLineCommissionConfig) error {

    // 1. 查找用户的推广链
    var chain DualModeReferralChain
    err := s.db.Where("consumer_user_id = ?", req.UserID).First(&chain).Error
    if err != nil {
        return nil // 无推广链，无需计算佣金
    }

    // 2. 根据推广模式计算佣金
    var allocations []*DualModeCommissionAllocation

    if chain.ReferralMode == 1 {
        // 一级代理商模式
        allocations = s.calculateLevel1ModeCommission(req, &chain, config)
    } else {
        // 二级代理商模式
        allocations = s.calculateLevel2ModeCommission(req, &chain, config)
    }

    // 3. 批量保存佣金分配记录
    return s.db.Transaction(func(tx *gorm.DB) error {
        for _, allocation := range allocations {
            if err := tx.Create(allocation).Error; err != nil {
                return err
            }
        }

        // 4. 更新订单佣金计算状态
        return tx.Model(&models.Order{}).
            Where("id = ?", req.OrderID).
            Updates(map[string]interface{}{
                "commission_calculated": true,
                "commission_settlement_date": time.Now().Format("2006-01-02"),
            }).Error
    })
}

// recordDailyUsage 记录日使用量（用于API按天批量结算）
func (s *BusinessLineCommissionService) recordDailyUsage(
    req *CommissionCalculationRequest, config *BusinessLineCommissionConfig) error {

    // 解析API使用信息
    modelName, _ := req.ProductInfo["model_name"].(string)
    inputTokens, _ := req.ProductInfo["input_tokens"].(int64)
    outputTokens, _ := req.ProductInfo["output_tokens"].(int64)
    requests, _ := req.ProductInfo["requests"].(int)

    if requests == 0 {
        requests = 1 // 默认至少1次请求
    }

    today := time.Now().Format("2006-01-02")

    // 更新或创建日统计记录
    var usage DailyAPIUsageStats
    err := s.db.Where("user_id = ? AND tenant_id = ? AND usage_date = ? AND model_name = ?",
        req.UserID, req.TenantID, today, modelName).First(&usage).Error

    if err == gorm.ErrRecordNotFound {
        // 创建新记录
        usage = DailyAPIUsageStats{
            UserID:           req.UserID,
            TenantID:         req.TenantID,
            UsageDate:        today,
            ModelName:        modelName,
            TotalRequests:    requests,
            TotalInputTokens: inputTokens,
            TotalOutputTokens: outputTokens,
            TotalCost:        req.Amount,
        }
        return s.db.Create(&usage).Error
    } else if err != nil {
        return err
    } else {
        // 更新现有记录
        updates := map[string]interface{}{
            "total_requests":      usage.TotalRequests + requests,
            "total_input_tokens":  usage.TotalInputTokens + inputTokens,
            "total_output_tokens": usage.TotalOutputTokens + outputTokens,
            "total_cost":          usage.TotalCost.Add(req.Amount),
            "updated_at":          time.Now(),
        }
        return s.db.Model(&usage).Updates(updates).Error
    }
}

// ProcessDailyAPICommissions 处理API日佣金批量结算
func (s *BusinessLineCommissionService) ProcessDailyAPICommissions(targetDate string) error {
    // 1. 查询指定日期未结算的API使用记录
    var usageStats []DailyAPIUsageStats
    err := s.db.Where("usage_date = ? AND commission_calculated = false", targetDate).
        Find(&usageStats).Error
    if err != nil {
        return fmt.Errorf("查询API使用统计失败: %v", err)
    }

    // 2. 为每个用户的API使用计算佣金
    for _, usage := range usageStats {
        req := &CommissionCalculationRequest{
            OrderID:      fmt.Sprintf("API_%s_%s_%s", usage.UserID, usage.ModelName, usage.UsageDate),
            UserID:       usage.UserID,
            TenantID:     usage.TenantID,
            BusinessLine: "llm_api",
            OrderType:    "api_consumption",
            Amount:       usage.TotalCost,
            ProductInfo: map[string]interface{}{
                "model_name":     usage.ModelName,
                "input_tokens":   usage.TotalInputTokens,
                "output_tokens":  usage.TotalOutputTokens,
                "requests":       usage.TotalRequests,
                "usage_date":     usage.UsageDate,
            },
        }

        // 获取配置并计算佣金
        config, err := s.getBusinessLineConfig(usage.TenantID, "llm_api", "api_consumption")
        if err != nil {
            continue // 跳过配置错误的记录
        }

        err = s.calculateImmediateCommission(req, config)
        if err != nil {
            continue // 跳过计算错误的记录
        }

        // 标记为已计算
        s.db.Model(&usage).Updates(map[string]interface{}{
            "commission_calculated":    true,
            "commission_settlement_date": targetDate,
        })
    }

    return nil
}

// ProcessMembershipCardCommission 处理会员卡销售佣金
func (s *BusinessLineCommissionService) ProcessMembershipCardCommission(
    orderID, userID, tenantID, cardID string, cardPrice decimal.Decimal) error {

    // 1. 获取会员卡组装信息
    var compositions []MembershipCardComposition
    err := s.db.Where("tenant_id = ? AND card_id = ?", tenantID, cardID).Find(&compositions).Error
    if err != nil {
        return fmt.Errorf("获取会员卡组装信息失败: %v", err)
    }

    // 2. 检查是否包含API配额
    hasAPIQuota := false
    for _, comp := range compositions {
        if comp.IsAPIQuota {
            hasAPIQuota = true
            break
        }
    }

    // 3. 计算会员卡销售佣金（一次性结算）
    req := &CommissionCalculationRequest{
        OrderID:      orderID,
        UserID:       userID,
        TenantID:     tenantID,
        BusinessLine: "membership_card",
        OrderType:    "membership_card",
        Amount:       cardPrice,
        ProductInfo: map[string]interface{}{
            "card_id":        cardID,
            "has_api_quota":  hasAPIQuota,
            "compositions":   compositions,
        },
    }

    return s.calculateCommission(req)
}

// getBusinessLineConfig 获取业务线佣金配置
func (s *BusinessLineCommissionService) getBusinessLineConfig(
    tenantID, businessLine, orderType string) (*BusinessLineCommissionConfig, error) {

    var config BusinessLineCommissionConfig
    err := s.db.Where("tenant_id IN (?, 'default') AND business_line = ? AND order_type = ? AND is_active = true",
        tenantID, businessLine, orderType).
        Order("tenant_id DESC").
        First(&config).Error
    if err != nil {
        return nil, fmt.Errorf("业务线配置不存在: %v", err)
    }

    return &config, nil
}

// calculateLevel1ModeCommission 计算一级代理商模式佣金（升级版）
func (s *BusinessLineCommissionService) calculateLevel1ModeCommission(
    req *CommissionCalculationRequest, chain *DualModeReferralChain,
    config *CommissionConfig) []*DualModeCommissionAllocation {

    allocations := make([]*DualModeCommissionAllocation, 0)

    // 根据计算模式选择不同的计算方法
    if config.CalculationMode == "fixed_amount" {
        return s.calculateLevel1FixedAmountCommission(req, chain, config)
    } else {
        return s.calculateLevel1PercentageCommission(req, chain, config)
    }
}

// calculateLevel1PercentageCommission 计算一级代理商模式比例佣金
func (s *BusinessLineCommissionService) calculateLevel1PercentageCommission(
    req *CommissionCalculationRequest, chain *DualModeReferralChain,
    config *CommissionConfig) []*DualModeCommissionAllocation {

    allocations := make([]*DualModeCommissionAllocation, 0)

    // Level 1: 一级推广佣金
    if chain.Level1ID != nil && chain.Level1Type != nil && *chain.Level1Type == "user" {
        amount := req.Amount.Mul(config.UserDirectRate)

        allocation := &DualModeCommissionAllocation{
            OrderID:             req.OrderID,
            OrderAmount:         req.Amount,
            ConsumerUserID:      req.UserID,
            ConsumerTenantID:    req.TenantID,
            ReferralMode:        1,
            BeneficiaryType:     "user",
            BeneficiaryID:       *chain.Level1ID,
            BeneficiaryTenantID: *chain.Level1TenantID,
            CommissionLevel:     1,
            CommissionType:      "direct",
            CommissionRate:      config.UserDirectRate,
            CommissionAmount:    amount,
            Status:              "pending",
            BusinessLine:        req.BusinessLine,
            OrderType:           req.OrderType,
            CalculationMode:     "percentage",
        }
        allocations = append(allocations, allocation)
    }

    // Level 2: 二级推广佣金
    if chain.Level2ID != nil && chain.Level2Type != nil && *chain.Level2Type == "user" {
        amount := req.Amount.Mul(config.UserIndirectRate)

        allocation := &DualModeCommissionAllocation{
            OrderID:             req.OrderID,
            OrderAmount:         req.Amount,
            ConsumerUserID:      req.UserID,
            ConsumerTenantID:    req.TenantID,
            ReferralMode:        1,
            BeneficiaryType:     "user",
            BeneficiaryID:       *chain.Level2ID,
            BeneficiaryTenantID: *chain.Level2TenantID,
            CommissionLevel:     2,
            CommissionType:      "indirect",
            CommissionRate:      config.UserIndirectRate,
            CommissionAmount:    amount,
            Status:              "pending",
            BusinessLine:        req.BusinessLine,
            OrderType:           req.OrderType,
            CalculationMode:     "percentage",
        }
        allocations = append(allocations, allocation)
    }

    // 代理商团队佣金 + 缺失层级佣金
    agentAllocation := s.calculateLevel1AgentPercentageCommission(req, chain, config)
    if agentAllocation != nil {
        allocations = append(allocations, agentAllocation)
    }

    return allocations
}

// calculateLevel1FixedAmountCommission 计算一级代理商模式固定金额佣金
func (s *BusinessLineCommissionService) calculateLevel1FixedAmountCommission(
    req *CommissionCalculationRequest, chain *DualModeReferralChain,
    config *CommissionConfig) []*DualModeCommissionAllocation {

    allocations := make([]*DualModeCommissionAllocation, 0)

    // Level 1: 一级推广固定佣金
    if chain.Level1ID != nil && chain.Level1Type != nil && *chain.Level1Type == "user" && config.UserDirectAmount != nil {
        allocation := &DualModeCommissionAllocation{
            OrderID:             req.OrderID,
            OrderAmount:         req.Amount,
            ConsumerUserID:      req.UserID,
            ConsumerTenantID:    req.TenantID,
            ReferralMode:        1,
            BeneficiaryType:     "user",
            BeneficiaryID:       *chain.Level1ID,
            BeneficiaryTenantID: *chain.Level1TenantID,
            CommissionLevel:     1,
            CommissionType:      "direct",
            CommissionRate:      decimal.Zero, // 固定金额模式下比例为0
            CommissionAmount:    *config.UserDirectAmount,
            Status:              "pending",
            BusinessLine:        req.BusinessLine,
            OrderType:           req.OrderType,
            CalculationMode:     "fixed_amount",
        }
        allocations = append(allocations, allocation)
    }

    // Level 2: 二级推广固定佣金
    if chain.Level2ID != nil && chain.Level2Type != nil && *chain.Level2Type == "user" && config.UserIndirectAmount != nil {
        allocation := &DualModeCommissionAllocation{
            OrderID:             req.OrderID,
            OrderAmount:         req.Amount,
            ConsumerUserID:      req.UserID,
            ConsumerTenantID:    req.TenantID,
            ReferralMode:        1,
            BeneficiaryType:     "user",
            BeneficiaryID:       *chain.Level2ID,
            BeneficiaryTenantID: *chain.Level2TenantID,
            CommissionLevel:     2,
            CommissionType:      "indirect",
            CommissionRate:      decimal.Zero, // 固定金额模式下比例为0
            CommissionAmount:    *config.UserIndirectAmount,
            Status:              "pending",
            BusinessLine:        req.BusinessLine,
            OrderType:           req.OrderType,
            CalculationMode:     "fixed_amount",
        }
        allocations = append(allocations, allocation)
    }

    // 代理商团队固定佣金
    agentAllocation := s.calculateLevel1AgentFixedAmountCommission(req, chain, config)
    if agentAllocation != nil {
        allocations = append(allocations, agentAllocation)
    }

    return allocations
}

// calculateLevel1AgentFixedAmountCommission 计算一级代理商团队固定佣金
func (s *BusinessLineCommissionService) calculateLevel1AgentFixedAmountCommission(
    req *CommissionCalculationRequest, chain *DualModeReferralChain,
    config *CommissionConfig) *DualModeCommissionAllocation {

    // 找到代理商层级
    var agentLevel int
    var agentID, agentTenantID string

    for level := 1; level <= 3; level++ {
        var levelType, levelID, levelTenantID *string

        switch level {
        case 1:
            levelType, levelID, levelTenantID = chain.Level1Type, chain.Level1ID, chain.Level1TenantID
        case 2:
            levelType, levelID, levelTenantID = chain.Level2Type, chain.Level2ID, chain.Level2TenantID
        case 3:
            levelType, levelID, levelTenantID = chain.Level3Type, chain.Level3ID, chain.Level3TenantID
        }

        if levelType != nil && *levelType == "agent" {
            agentLevel = level
            agentID = *levelID
            agentTenantID = *levelTenantID
            break
        }
    }

    if agentLevel == 0 || config.AgentTeamAmount == nil {
        return nil // 没有代理商层级或未配置固定佣金
    }

    // 计算缺失层级的固定佣金补偿
    totalAmount := *config.AgentTeamAmount
    missingAmount := s.calculateLevel1MissingFixedAmount(chain, agentLevel, config)
    totalAmount = totalAmount.Add(missingAmount)

    return &DualModeCommissionAllocation{
        OrderID:             req.OrderID,
        OrderAmount:         req.Amount,
        ConsumerUserID:      req.UserID,
        ConsumerTenantID:    req.TenantID,
        ReferralMode:        1,
        BeneficiaryType:     "agent",
        BeneficiaryID:       agentID,
        BeneficiaryTenantID: agentTenantID,
        CommissionLevel:     agentLevel,
        CommissionType:      "team",
        CommissionRate:      decimal.Zero, // 固定金额模式下比例为0
        CommissionAmount:    totalAmount,
        Status:              "pending",
        BusinessLine:        req.BusinessLine,
        OrderType:           req.OrderType,
        CalculationMode:     "fixed_amount",
    }
}

// calculateLevel1MissingFixedAmount 计算一级代理商模式缺失层级固定佣金
func (s *BusinessLineCommissionService) calculateLevel1MissingFixedAmount(
    chain *DualModeReferralChain, agentLevel int, config *CommissionConfig) decimal.Decimal {

    missingAmount := decimal.Zero

    // 如果代理商前面缺少用户推广层级，代理商获得这些层级的固定佣金
    if agentLevel > 1 {
        // 缺少Level 1用户推广固定佣金
        if (chain.Level1Type == nil || *chain.Level1Type != "user") && config.UserDirectAmount != nil {
            missingAmount = missingAmount.Add(*config.UserDirectAmount)
        }
    }

    if agentLevel > 2 {
        // 缺少Level 2用户推广固定佣金
        if (chain.Level2Type == nil || *chain.Level2Type != "user") && config.UserIndirectAmount != nil {
            missingAmount = missingAmount.Add(*config.UserIndirectAmount)
        }
    }

    return missingAmount
}

// calculateLevel2ModeCommission 计算二级代理商模式佣金
func (s *BusinessLineCommissionService) calculateLevel2ModeCommission(
    req *CommissionCalculationRequest, chain *DualModeReferralChain,
    config *BusinessLineCommissionConfig) []*DualModeCommissionAllocation {

    allocations := make([]*DualModeCommissionAllocation, 0)

    // 用户层级佣金
    userAllocations := s.calculateLevel2UserCommissions(req, chain, config)
    allocations = append(allocations, userAllocations...)

    // 代理商层级佣金
    agentAllocations := s.calculateLevel2AgentCommissions(req, chain, config)
    allocations = append(allocations, agentAllocations...)

    return allocations
}
```

### 2. 数据模型定义
```go
// BusinessLineCommissionConfig 业务线佣金配置模型（升级版）
type BusinessLineCommissionConfig struct {
    ID                     uint            `json:"id" gorm:"primaryKey"`
    TenantID               string          `json:"tenant_id" gorm:"type:varchar(16);not null;index"`
    BusinessLine           string          `json:"business_line" gorm:"type:varchar(50);not null"`
    OrderType              string          `json:"order_type" gorm:"type:varchar(50);not null"`
    SettlementMode         string          `json:"settlement_mode" gorm:"type:varchar(20);not null"`
    ReferralMode           int             `json:"referral_mode" gorm:"not null;index"`
    CalculationMode        string          `json:"calculation_mode" gorm:"type:varchar(20);default:'percentage';index"`

    // 比例佣金配置
    UserDirectRate         decimal.Decimal `json:"user_direct_rate" gorm:"type:decimal(5,4);default:0.3000"`
    UserIndirectRate       decimal.Decimal `json:"user_indirect_rate" gorm:"type:decimal(5,4);default:0.1000"`
    AgentTeamRate          decimal.Decimal `json:"agent_team_rate" gorm:"type:decimal(5,4)"`
    AgentTotalRate         *decimal.Decimal `json:"agent_total_rate" gorm:"type:decimal(5,4)"`

    // 固定金额佣金配置
    UserDirectAmount       *decimal.Decimal `json:"user_direct_amount" gorm:"type:decimal(10,2)"`
    UserIndirectAmount     *decimal.Decimal `json:"user_indirect_amount" gorm:"type:decimal(10,2)"`
    AgentTeamAmount        *decimal.Decimal `json:"agent_team_amount" gorm:"type:decimal(10,2)"`
    AgentTotalAmount       *decimal.Decimal `json:"agent_total_amount" gorm:"type:decimal(10,2)"`

    MinCommissionAmount    decimal.Decimal `json:"min_commission_amount" gorm:"type:decimal(10,2);default:0.01"`
    MaxCommissionAmount    *decimal.Decimal `json:"max_commission_amount" gorm:"type:decimal(10,2)"`
    CommissionDelayHours   int             `json:"commission_delay_hours" gorm:"default:0"`

    Description            *string         `json:"description" gorm:"type:text"`
    IsActive               bool            `json:"is_active" gorm:"default:true"`
    CreatedAt              time.Time       `json:"created_at"`
    UpdatedAt              time.Time       `json:"updated_at"`
}

func (BusinessLineCommissionConfig) TableName() string {
    return "business_line_commission_configs"
}

// ProductCommissionConfig 产品级佣金配置模型
type ProductCommissionConfig struct {
    ID                     uint            `json:"id" gorm:"primaryKey"`
    TenantID               string          `json:"tenant_id" gorm:"type:varchar(16);not null;index"`
    ProductType            string          `json:"product_type" gorm:"type:varchar(50);not null"`
    ProductID              string          `json:"product_id" gorm:"type:varchar(100);not null;index"`
    ProductName            string          `json:"product_name" gorm:"type:varchar(200);not null"`

    CommissionEnabled      bool            `json:"commission_enabled" gorm:"default:false;index"`
    CalculationMode        string          `json:"calculation_mode" gorm:"type:varchar(20);default:'percentage';index"`
    ReferralMode           int             `json:"referral_mode" gorm:"not null;index"`

    // 比例佣金配置
    UserDirectRate         decimal.Decimal `json:"user_direct_rate" gorm:"type:decimal(5,4)"`
    UserIndirectRate       decimal.Decimal `json:"user_indirect_rate" gorm:"type:decimal(5,4)"`
    AgentTeamRate          decimal.Decimal `json:"agent_team_rate" gorm:"type:decimal(5,4)"`
    AgentTotalRate         *decimal.Decimal `json:"agent_total_rate" gorm:"type:decimal(5,4)"`

    // 固定金额佣金配置
    UserDirectAmount       *decimal.Decimal `json:"user_direct_amount" gorm:"type:decimal(10,2)"`
    UserIndirectAmount     *decimal.Decimal `json:"user_indirect_amount" gorm:"type:decimal(10,2)"`
    AgentTeamAmount        *decimal.Decimal `json:"agent_team_amount" gorm:"type:decimal(10,2)"`
    AgentTotalAmount       *decimal.Decimal `json:"agent_total_amount" gorm:"type:decimal(10,2)"`

    MinOrderAmount         *decimal.Decimal `json:"min_order_amount" gorm:"type:decimal(10,2)"`
    MaxCommissionPerOrder  *decimal.Decimal `json:"max_commission_per_order" gorm:"type:decimal(10,2)"`
    CommissionValidDays    *int            `json:"commission_valid_days"`

    CreatedBy              *string         `json:"created_by" gorm:"type:varchar(50);index"`
    ApprovedBy             *string         `json:"approved_by" gorm:"type:varchar(50)"`
    ApprovedAt             *time.Time      `json:"approved_at"`

    Description            *string         `json:"description" gorm:"type:text"`
    IsActive               bool            `json:"is_active" gorm:"default:true"`
    CreatedAt              time.Time       `json:"created_at"`
    UpdatedAt              time.Time       `json:"updated_at"`
}

func (ProductCommissionConfig) TableName() string {
    return "product_commission_configs"
}

// DailyAPIUsageStats API使用量日统计模型
type DailyAPIUsageStats struct {
    ID                      uint            `json:"id" gorm:"primaryKey"`
    UserID                  string          `json:"user_id" gorm:"type:varchar(50);not null;index"`
    TenantID                string          `json:"tenant_id" gorm:"type:varchar(16);not null;index"`
    UsageDate               string          `json:"usage_date" gorm:"type:date;not null;index"`

    ModelName               string          `json:"model_name" gorm:"type:varchar(100);not null"`
    TotalRequests           int             `json:"total_requests" gorm:"default:0"`
    TotalInputTokens        int64           `json:"total_input_tokens" gorm:"default:0"`
    TotalOutputTokens       int64           `json:"total_output_tokens" gorm:"default:0"`
    TotalCost               decimal.Decimal `json:"total_cost" gorm:"type:decimal(10,4);default:0"`

    InputTokenPrice         *decimal.Decimal `json:"input_token_price" gorm:"type:decimal(8,6)"`
    OutputTokenPrice        *decimal.Decimal `json:"output_token_price" gorm:"type:decimal(8,6)"`
    RequestPrice            *decimal.Decimal `json:"request_price" gorm:"type:decimal(8,4)"`

    CommissionCalculated    bool            `json:"commission_calculated" gorm:"default:false;index"`
    CommissionSettlementDate *string        `json:"commission_settlement_date" gorm:"type:date;index"`

    CreatedAt               time.Time       `json:"created_at"`
    UpdatedAt               time.Time       `json:"updated_at"`
}

func (DailyAPIUsageStats) TableName() string {
    return "daily_api_usage_stats"
}

// MembershipCardComposition 会员卡组装信息模型
type MembershipCardComposition struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(16);not null;index"`
    CardID          string          `json:"card_id" gorm:"type:varchar(50);not null;index"`
    CardName        string          `json:"card_name" gorm:"type:varchar(100);not null"`
    CardPrice       decimal.Decimal `json:"card_price" gorm:"type:decimal(10,2);not null"`

    BenefitID       string          `json:"benefit_id" gorm:"type:varchar(50);not null"`
    BenefitName     string          `json:"benefit_name" gorm:"type:varchar(100);not null"`
    BenefitType     string          `json:"benefit_type" gorm:"type:varchar(50);not null"`

    QuotaID         *string         `json:"quota_id" gorm:"type:varchar(50)"`
    QuotaType       *string         `json:"quota_type" gorm:"type:varchar(50)"`
    QuotaAmount     *int64          `json:"quota_amount"`
    QuotaUnit       *string         `json:"quota_unit" gorm:"type:varchar(20)"`
    QuotaValue      *decimal.Decimal `json:"quota_value" gorm:"type:decimal(10,2)"`

    IsAPIQuota      bool            `json:"is_api_quota" gorm:"default:false;index"`
    CommissionMode  string          `json:"commission_mode" gorm:"type:varchar(20);default:'lump_sum'"`

    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}

func (MembershipCardComposition) TableName() string {
    return "membership_card_compositions"
}

// 升级DualModeCommissionAllocation模型，添加业务线字段
type DualModeCommissionAllocation struct {
    // ... 原有字段 ...

    // 新增字段
    BusinessLine    string `json:"business_line" gorm:"type:varchar(50);index"`
    OrderType       string `json:"order_type" gorm:"type:varchar(50);index"`
    SettlementDate  *string `json:"settlement_date" gorm:"type:date;index"`

    // ... 其他字段 ...
}
```

## 🌐 API接口设计

### 1. 产品级佣金配置接口（新增）
```go
// ProductCommissionHandler 产品级佣金配置处理器
type ProductCommissionHandler struct {
    commissionService *BusinessLineCommissionService
}

// CreateProductCommissionConfig 创建产品级佣金配置
// POST /api/v1/product-commission/configs
func (h *ProductCommissionHandler) CreateProductCommissionConfig(c *gin.Context) {
    tenantID := c.GetString("tenantID")
    userID := c.GetString("userID")

    var req struct {
        ProductType         string  `json:"product_type" binding:"required"`
        ProductID           string  `json:"product_id" binding:"required"`
        ProductName         string  `json:"product_name" binding:"required"`
        CommissionEnabled   bool    `json:"commission_enabled"`
        CalculationMode     string  `json:"calculation_mode" binding:"required"`
        ReferralMode        int     `json:"referral_mode" binding:"required,min=1,max=2"`

        // 比例佣金配置
        UserDirectRate      *float64 `json:"user_direct_rate"`
        UserIndirectRate    *float64 `json:"user_indirect_rate"`
        AgentTeamRate       *float64 `json:"agent_team_rate"`
        AgentTotalRate      *float64 `json:"agent_total_rate"`

        // 固定金额佣金配置
        UserDirectAmount    *float64 `json:"user_direct_amount"`
        UserIndirectAmount  *float64 `json:"user_indirect_amount"`
        AgentTeamAmount     *float64 `json:"agent_team_amount"`
        AgentTotalAmount    *float64 `json:"agent_total_amount"`

        MinOrderAmount      *float64 `json:"min_order_amount"`
        MaxCommissionPerOrder *float64 `json:"max_commission_per_order"`
        CommissionValidDays *int     `json:"commission_valid_days"`
        Description         string   `json:"description"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 验证业务规则
    if req.CalculationMode == "percentage" {
        if req.UserDirectRate == nil || req.UserIndirectRate == nil || req.AgentTeamRate == nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "比例模式必须设置所有比例参数"})
            return
        }
    } else if req.CalculationMode == "fixed_amount" {
        if req.UserDirectAmount == nil || req.UserIndirectAmount == nil || req.AgentTeamAmount == nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "固定金额模式必须设置所有固定金额参数"})
            return
        }
    }

    if req.ReferralMode == 2 && req.CalculationMode == "percentage" && req.AgentTotalRate == nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "二级代理商比例模式必须设置总佣金率"})
        return
    }

    if req.ReferralMode == 2 && req.CalculationMode == "fixed_amount" && req.AgentTotalAmount == nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "二级代理商固定金额模式必须设置总固定佣金"})
        return
    }

    config := &ProductCommissionConfig{
        TenantID:          tenantID,
        ProductType:       req.ProductType,
        ProductID:         req.ProductID,
        ProductName:       req.ProductName,
        CommissionEnabled: req.CommissionEnabled,
        CalculationMode:   req.CalculationMode,
        ReferralMode:      req.ReferralMode,
        CreatedBy:         &userID,
        Description:       &req.Description,
        IsActive:          true,
    }

    // 设置比例佣金配置
    if req.UserDirectRate != nil {
        config.UserDirectRate = decimal.NewFromFloat(*req.UserDirectRate)
    }
    if req.UserIndirectRate != nil {
        config.UserIndirectRate = decimal.NewFromFloat(*req.UserIndirectRate)
    }
    if req.AgentTeamRate != nil {
        config.AgentTeamRate = decimal.NewFromFloat(*req.AgentTeamRate)
    }
    if req.AgentTotalRate != nil {
        rate := decimal.NewFromFloat(*req.AgentTotalRate)
        config.AgentTotalRate = &rate
    }

    // 设置固定金额佣金配置
    if req.UserDirectAmount != nil {
        amount := decimal.NewFromFloat(*req.UserDirectAmount)
        config.UserDirectAmount = &amount
    }
    if req.UserIndirectAmount != nil {
        amount := decimal.NewFromFloat(*req.UserIndirectAmount)
        config.UserIndirectAmount = &amount
    }
    if req.AgentTeamAmount != nil {
        amount := decimal.NewFromFloat(*req.AgentTeamAmount)
        config.AgentTeamAmount = &amount
    }
    if req.AgentTotalAmount != nil {
        amount := decimal.NewFromFloat(*req.AgentTotalAmount)
        config.AgentTotalAmount = &amount
    }

    // 设置其他配置
    if req.MinOrderAmount != nil {
        amount := decimal.NewFromFloat(*req.MinOrderAmount)
        config.MinOrderAmount = &amount
    }
    if req.MaxCommissionPerOrder != nil {
        amount := decimal.NewFromFloat(*req.MaxCommissionPerOrder)
        config.MaxCommissionPerOrder = &amount
    }
    if req.CommissionValidDays != nil {
        config.CommissionValidDays = req.CommissionValidDays
    }

    if err := h.commissionService.db.Create(config).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, gin.H{"data": config})
}

// GetProductCommissionConfigs 获取产品级佣金配置列表
// GET /api/v1/product-commission/configs
func (h *ProductCommissionHandler) GetProductCommissionConfigs(c *gin.Context) {
    tenantID := c.GetString("tenantID")
    productType := c.Query("product_type")
    productID := c.Query("product_id")
    calculationMode := c.Query("calculation_mode")
    commissionEnabled := c.Query("commission_enabled")

    query := h.commissionService.db.Where("tenant_id = ? AND is_active = true", tenantID)

    if productType != "" {
        query = query.Where("product_type = ?", productType)
    }

    if productID != "" {
        query = query.Where("product_id = ?", productID)
    }

    if calculationMode != "" {
        query = query.Where("calculation_mode = ?", calculationMode)
    }

    if commissionEnabled != "" {
        if enabled, err := strconv.ParseBool(commissionEnabled); err == nil {
            query = query.Where("commission_enabled = ?", enabled)
        }
    }

    var configs []ProductCommissionConfig
    err := query.Order("product_type ASC, product_name ASC, referral_mode ASC").
        Find(&configs).Error
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"data": configs})
}

// UpdateProductCommissionConfig 更新产品级佣金配置
// PUT /api/v1/product-commission/configs/:id
func (h *ProductCommissionHandler) UpdateProductCommissionConfig(c *gin.Context) {
    tenantID := c.GetString("tenantID")
    configID := c.Param("id")

    var config ProductCommissionConfig
    err := h.commissionService.db.Where("id = ? AND tenant_id = ?", configID, tenantID).
        First(&config).Error
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "配置不存在"})
        return
    }

    var req struct {
        CommissionEnabled   *bool    `json:"commission_enabled"`
        CalculationMode     *string  `json:"calculation_mode"`

        // 比例佣金配置
        UserDirectRate      *float64 `json:"user_direct_rate"`
        UserIndirectRate    *float64 `json:"user_indirect_rate"`
        AgentTeamRate       *float64 `json:"agent_team_rate"`
        AgentTotalRate      *float64 `json:"agent_total_rate"`

        // 固定金额佣金配置
        UserDirectAmount    *float64 `json:"user_direct_amount"`
        UserIndirectAmount  *float64 `json:"user_indirect_amount"`
        AgentTeamAmount     *float64 `json:"agent_team_amount"`
        AgentTotalAmount    *float64 `json:"agent_total_amount"`

        Description         *string  `json:"description"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    updates := make(map[string]interface{})

    if req.CommissionEnabled != nil {
        updates["commission_enabled"] = *req.CommissionEnabled
    }

    if req.CalculationMode != nil {
        updates["calculation_mode"] = *req.CalculationMode
    }

    // 更新比例佣金配置
    if req.UserDirectRate != nil {
        updates["user_direct_rate"] = decimal.NewFromFloat(*req.UserDirectRate)
    }
    if req.UserIndirectRate != nil {
        updates["user_indirect_rate"] = decimal.NewFromFloat(*req.UserIndirectRate)
    }
    if req.AgentTeamRate != nil {
        updates["agent_team_rate"] = decimal.NewFromFloat(*req.AgentTeamRate)
    }
    if req.AgentTotalRate != nil {
        updates["agent_total_rate"] = decimal.NewFromFloat(*req.AgentTotalRate)
    }

    // 更新固定金额佣金配置
    if req.UserDirectAmount != nil {
        updates["user_direct_amount"] = decimal.NewFromFloat(*req.UserDirectAmount)
    }
    if req.UserIndirectAmount != nil {
        updates["user_indirect_amount"] = decimal.NewFromFloat(*req.UserIndirectAmount)
    }
    if req.AgentTeamAmount != nil {
        updates["agent_team_amount"] = decimal.NewFromFloat(*req.AgentTeamAmount)
    }
    if req.AgentTotalAmount != nil {
        updates["agent_total_amount"] = decimal.NewFromFloat(*req.AgentTotalAmount)
    }

    if req.Description != nil {
        updates["description"] = *req.Description
    }

    updates["updated_at"] = time.Now()

    err = h.commissionService.db.Model(&config).Updates(updates).Error
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "配置更新成功", "data": config})
}
```

### 2. 业务线佣金管理接口（升级版）
```go
// BusinessLineCommissionHandler 业务线佣金管理处理器
type BusinessLineCommissionHandler struct {
    commissionService *BusinessLineCommissionService
}

// CreateBusinessLineConfig 创建业务线佣金配置
// POST /api/v1/business-line/commission/configs
func (h *BusinessLineCommissionHandler) CreateBusinessLineConfig(c *gin.Context) {
    tenantID := c.GetString("tenantID")

    var req struct {
        BusinessLine         string  `json:"business_line" binding:"required"`
        OrderType           string  `json:"order_type" binding:"required"`
        SettlementMode      string  `json:"settlement_mode" binding:"required"`
        ReferralMode        int     `json:"referral_mode" binding:"required,min=1,max=2"`
        UserDirectRate      float64 `json:"user_direct_rate" binding:"required,min=0,max=1"`
        UserIndirectRate    float64 `json:"user_indirect_rate" binding:"required,min=0,max=1"`
        AgentTeamRate       float64 `json:"agent_team_rate" binding:"required,min=0,max=1"`
        AgentTotalRate      *float64 `json:"agent_total_rate"`
        MinCommissionAmount float64 `json:"min_commission_amount"`
        MaxCommissionAmount *float64 `json:"max_commission_amount"`
        CommissionDelayHours int    `json:"commission_delay_hours"`
        Description         string  `json:"description"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 验证业务规则
    if req.ReferralMode == 2 && req.AgentTotalRate == nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "二级代理商模式必须设置总佣金率"})
        return
    }

    config := &BusinessLineCommissionConfig{
        TenantID:             tenantID,
        BusinessLine:         req.BusinessLine,
        OrderType:            req.OrderType,
        SettlementMode:       req.SettlementMode,
        ReferralMode:         req.ReferralMode,
        UserDirectRate:       decimal.NewFromFloat(req.UserDirectRate),
        UserIndirectRate:     decimal.NewFromFloat(req.UserIndirectRate),
        AgentTeamRate:        decimal.NewFromFloat(req.AgentTeamRate),
        MinCommissionAmount:  decimal.NewFromFloat(req.MinCommissionAmount),
        CommissionDelayHours: req.CommissionDelayHours,
        Description:          &req.Description,
        IsActive:             true,
    }

    if req.AgentTotalRate != nil {
        rate := decimal.NewFromFloat(*req.AgentTotalRate)
        config.AgentTotalRate = &rate
    }

    if req.MaxCommissionAmount != nil {
        amount := decimal.NewFromFloat(*req.MaxCommissionAmount)
        config.MaxCommissionAmount = &amount
    }

    if err := h.commissionService.db.Create(config).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, gin.H{"data": config})
}

// GetBusinessLineConfigs 获取业务线佣金配置列表
// GET /api/v1/business-line/commission/configs
func (h *BusinessLineCommissionHandler) GetBusinessLineConfigs(c *gin.Context) {
    tenantID := c.GetString("tenantID")
    businessLine := c.Query("business_line")
    referralMode := c.Query("referral_mode")

    query := h.commissionService.db.Where("tenant_id = ? AND is_active = true", tenantID)

    if businessLine != "" {
        query = query.Where("business_line = ?", businessLine)
    }

    if referralMode != "" {
        if mode, err := strconv.Atoi(referralMode); err == nil {
            query = query.Where("referral_mode = ?", mode)
        }
    }

    var configs []BusinessLineCommissionConfig
    err := query.Order("business_line ASC, order_type ASC, referral_mode ASC").
        Find(&configs).Error
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"data": configs})
}

// TriggerCommissionCalculation 手动触发佣金计算
// POST /api/v1/business-line/commission/calculate
func (h *BusinessLineCommissionHandler) TriggerCommissionCalculation(c *gin.Context) {
    var req CommissionCalculationRequest

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    err := h.commissionService.CalculateCommission(&req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "佣金计算完成"})
}

// GetDailyAPIUsageStats 获取API日使用统计
// GET /api/v1/business-line/api-usage/daily
func (h *BusinessLineCommissionHandler) GetDailyAPIUsageStats(c *gin.Context) {
    userID := c.GetString("userID")
    tenantID := c.GetString("tenantID")
    startDate := c.Query("start_date")
    endDate := c.Query("end_date")
    modelName := c.Query("model_name")

    query := h.commissionService.db.Model(&DailyAPIUsageStats{}).
        Where("user_id = ? AND tenant_id = ?", userID, tenantID)

    if startDate != "" {
        query = query.Where("usage_date >= ?", startDate)
    }

    if endDate != "" {
        query = query.Where("usage_date <= ?", endDate)
    }

    if modelName != "" {
        query = query.Where("model_name = ?", modelName)
    }

    var stats []DailyAPIUsageStats
    err := query.Order("usage_date DESC, model_name ASC").Find(&stats).Error
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetMembershipCardCompositions 获取会员卡组装信息
// GET /api/v1/business-line/membership-card/compositions/:card_id
func (h *BusinessLineCommissionHandler) GetMembershipCardCompositions(c *gin.Context) {
    tenantID := c.GetString("tenantID")
    cardID := c.Param("card_id")

    var compositions []MembershipCardComposition
    err := h.commissionService.db.Where("tenant_id = ? AND card_id = ?", tenantID, cardID).
        Find(&compositions).Error
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"data": compositions})
}
```

### 2. 定时任务服务
```go
// CommissionSchedulerService 佣金调度服务
type CommissionSchedulerService struct {
    commissionService *BusinessLineCommissionService
    logger           *logrus.Logger
}

// NewCommissionSchedulerService 创建佣金调度服务
func NewCommissionSchedulerService(
    commissionService *BusinessLineCommissionService,
    logger *logrus.Logger) *CommissionSchedulerService {
    return &CommissionSchedulerService{
        commissionService: commissionService,
        logger:           logger,
    }
}

// StartScheduler 启动定时任务调度器
func (s *CommissionSchedulerService) StartScheduler() {
    // 每天凌晨2点执行API佣金批量结算
    c := cron.New()
    c.AddFunc("0 2 * * *", s.ProcessDailyAPICommissions)
    c.Start()

    s.logger.Info("佣金调度器已启动")
}

// ProcessDailyAPICommissions 处理API日佣金批量结算
func (s *CommissionSchedulerService) ProcessDailyAPICommissions() {
    yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

    s.logger.WithField("date", yesterday).Info("开始处理API日佣金批量结算")

    err := s.commissionService.ProcessDailyAPICommissions(yesterday)
    if err != nil {
        s.logger.WithError(err).Error("API日佣金批量结算失败")
        return
    }

    s.logger.WithField("date", yesterday).Info("API日佣金批量结算完成")
}

// ProcessDelayedCommissions 处理延迟发放的佣金
func (s *CommissionSchedulerService) ProcessDelayedCommissions() {
    // 查询需要发放的延迟佣金
    var allocations []DualModeCommissionAllocation
    err := s.commissionService.db.Where("status = 'pending' AND created_at <= ?",
        time.Now().Add(-24*time.Hour)).Find(&allocations).Error
    if err != nil {
        s.logger.WithError(err).Error("查询延迟佣金失败")
        return
    }

    // 批量更新佣金状态为已确认
    for _, allocation := range allocations {
        s.commissionService.db.Model(&allocation).Updates(map[string]interface{}{
            "status":     "confirmed",
            "updated_at": time.Now(),
        })
    }

    s.logger.WithField("count", len(allocations)).Info("延迟佣金处理完成")
}
```

## 📅 详细实施计划

### 阶段1：数据库架构升级（5天）

#### Day 1: 核心表创建
- [ ] 升级 `business_line_commission_configs` 表，添加固定金额佣金字段
- [ ] 创建 `product_commission_configs` 表
- [ ] 升级 `orders` 表，添加产品级佣金相关字段
- [ ] 升级 `daily_api_usage_stats` 表
- [ ] 创建 `referral_visits` 表（访客追踪）

#### Day 2: 推广权限管理表创建
- [ ] 创建 `user_referral_permissions` 表
- [ ] 升级 `referrers` 表，添加权限相关字段
- [ ] 升级 `membership_card_compositions` 表
- [ ] 添加会员卡级别佣金配置字段
- [ ] 建立表间关联关系

#### Day 3: 数据迁移和配置
- [ ] 迁移现有订单数据到新表结构
- [ ] 初始化业务线佣金配置数据（包含固定金额配置）
- [ ] 初始化产品级佣金配置数据
- [ ] 初始化会员卡组装信息数据

#### Day 4: 升级现有推广表
- [ ] 升级 `dual_mode_commission_allocations` 表
- [ ] 添加业务线、订单类型、计算模式字段
- [ ] 迁移现有佣金数据
- [ ] 数据完整性验证

#### Day 5: 性能优化和索引
- [ ] 创建所有必要的索引
- [ ] 数据库查询优化
- [ ] 索引性能测试
- [ ] 分区表设计（如需要）
- [ ] 备份和恢复测试

### 阶段2：Go服务层开发（6天）

#### Day 6: 全站推广链接体系开发
- [ ] 实现URL参数处理中间件
- [ ] 全站链接生成服务
- [ ] 推广关系持久化服务
- [ ] 多重绑定策略实现
- [ ] Cookie管理和访客追踪

#### Day 7: 推广权限管理服务开发
- [ ] 实现 `ReferralPermissionService`
- [ ] 推广权限申请、审核、开通流程
- [ ] 佣金权限验证服务
- [ ] 推广员ID和推广码生成服务

#### Day 8: 产品级佣金服务开发
- [ ] 实现产品级佣金配置管理
- [ ] 产品级与业务线配置优先级处理
- [ ] 固定金额佣金计算逻辑
- [ ] 比例佣金与固定金额佣金统一接口

#### Day 9: 业务线佣金服务升级
- [ ] 升级 `BusinessLineCommissionService`
- [ ] 集成推广权限验证逻辑
- [ ] 双重佣金计算模式支持
- [ ] 即时佣金计算逻辑升级
- [ ] 充值与消费分离逻辑

#### Day 9: API使用量统计服务
- [ ] 实现API使用量日统计
- [ ] 按天批量佣金结算
- [ ] 产品级API佣金配置支持
- [ ] 统计数据查询优化

#### Day 10: 会员卡佣金处理
- [ ] 会员卡组装信息管理
- [ ] 会员卡销售佣金计算
- [ ] API配额一次性结算逻辑
- [ ] 权益组装佣金处理

#### Day 11: 多业务线集成
- [ ] 智能体市场产品级佣金集成
- [ ] AI插件工具产品级佣金集成
- [ ] 大模型API产品级佣金集成
- [ ] 业务线间数据同步

#### Day 12: 定时任务和调度
- [ ] 实现 `CommissionSchedulerService`
- [ ] API日佣金批量结算定时任务
- [ ] 延迟佣金处理定时任务
- [ ] 访客记录清理定时任务
- [ ] 任务监控和日志记录

### 阶段3：API接口开发（4天）

#### Day 13: 产品级佣金配置接口
- [ ] 产品级佣金配置管理接口
- [ ] 智能体上架佣金配置接口
- [ ] AI插件上架佣金配置接口
- [ ] 大模型API渠道佣金配置接口

#### Day 14: 业务线配置接口升级
- [ ] 业务线佣金配置管理接口（支持固定金额）
- [ ] 佣金规则查询接口
- [ ] 配置验证和更新接口
- [ ] 配置历史记录接口

#### Day 15: 统计查询接口
- [ ] API使用量统计查询接口
- [ ] 会员卡组装信息查询接口
- [ ] 业务线佣金统计接口
- [ ] 产品级佣金统计接口
- [ ] 佣金明细查询接口

#### Day 16: 推广权限管理接口
- [ ] 推广权限申请接口
- [ ] 推广权限审核接口（通过/拒绝）
- [ ] 推广权限切换接口
- [ ] 佣金权限管理接口
- [ ] 推广权限查询接口

#### Day 17: 管理和监控接口
- [ ] 手动触发佣金计算接口
- [ ] 佣金结算状态查询接口
- [ ] 业务线数据监控接口
- [ ] 产品级佣金监控接口
- [ ] 推广关系查询和管理接口
- [ ] 异常处理和重试接口

### 阶段4：业务集成和测试（3天）

#### Day 13: 智能体市场集成
- [ ] 智能体购买佣金计算集成
- [ ] 按次/按时间/一次性授权佣金处理
- [ ] 智能体市场订单流程集成
- [ ] 功能测试和验证

#### Day 14: 大模型API集成
- [ ] API使用量实时统计
- [ ] 按天批量佣金结算集成
- [ ] API计费系统对接
- [ ] 批量结算测试

#### Day 15: 会员卡系统集成
- [ ] 会员卡销售佣金集成
- [ ] 权益组装信息同步
- [ ] API配额一次性结算测试
- [ ] 会员营销系统对接

### 阶段5：前端界面开发（6天）

#### Day 17: Material Design 3.0 基础框架
- [ ] 引入Material Design 3.0组件库
- [ ] 建立设计系统和主题配置
- [ ] 响应式布局框架搭建
- [ ] 全站URL参数处理中间件
- [ ] 公共组件开发（导航、卡片、表格、分享等）

#### Day 18-19: 用户前端页面开发
- [ ] 推广分享页面（推广工具、统计展示、全站分享功能）
- [ ] 佣金收益页面（收益概览、明细列表、趋势图）
- [ ] 提现申请页面（提现表单、支付方式选择）
- [ ] 推广团队页面（团队统计、成员列表、层级展示）
- [ ] 智能体详情页面（包含推广链接和佣金信息）

#### Day 20-21: 管理员后台页面开发
- [ ] 代理商管理页面（列表、筛选、操作）
- [ ] 佣金配置管理页面（业务线配置、产品级配置）
- [ ] 推广系统管理页面（推广数据大屏、分销商管理、权限审核）
- [ ] 推广关系管理页面（关系查询、访客追踪）
- [ ] 数据统计大屏页面（核心指标、图表、实时数据）

#### Day 22: 移动端适配和优化
- [ ] 移动端响应式适配
- [ ] 平板端界面优化
- [ ] 触摸交互优化
- [ ] 全站链接分享优化
- [ ] 性能优化和懒加载

### 阶段6：测试和上线（7天）

#### Day 22: 租户管理和ID生成测试
- [ ] 租户ID生成规范测试
- [ ] 推广员ID生成测试（从10001开始）
- [ ] 用户ID生成测试
- [ ] 租户隔离机制测试
- [ ] ID唯一性和并发测试

#### Day 23: 双重佣金计算测试
- [ ] 比例佣金计算测试
- [ ] 固定金额佣金计算测试
- [ ] 产品级佣金配置测试
- [ ] 缺失层级佣金补偿测试
- [ ] 佣金配置优先级测试

#### Day 24: 业务流程综合测试
- [ ] 充值与消费分离测试
- [ ] 多业务线佣金计算测试
- [ ] API按天批量结算测试
- [ ] 会员卡一次性结算测试
- [ ] 推广链接和二维码测试

#### Day 25: 前端界面测试
- [ ] 用户前端功能测试
- [ ] 管理员后台功能测试
- [ ] 响应式设计测试
- [ ] Material Design 3.0组件测试
- [ ] 跨浏览器兼容性测试

#### Day 26: 性能和安全测试
- [ ] 数据库查询性能测试
- [ ] 并发处理能力测试
- [ ] 安全漏洞扫描
- [ ] 数据加密和传输安全测试
- [ ] 租户数据隔离安全测试

#### Day 27: 生产环境部署
- [ ] 生产环境数据库升级
- [ ] 后端服务部署和配置
- [ ] 前端应用部署和CDN配置
- [ ] 定时任务配置
- [ ] 监控和告警配置

#### Day 28: 上线验证和文档交付
- [ ] 生产环境功能验证
- [ ] 数据迁移验证
- [ ] 用户培训和文档交付
- [ ] 运维手册编写
- [ ] 上线验证和回滚准备

## 🎯 关键技术要点

### 1. 充值与消费分离机制
```go
// 充值订单：不参与佣金计算
if req.OrderType == "recharge" {
    return nil // 直接返回，不计算佣金
}

// 消费订单：参与佣金计算
if req.OrderType == "agent_purchase" || req.OrderType == "plugin_purchase" {
    return s.calculateImmediateCommission(req, config)
}
```

### 2. 双重佣金计算模式
```go
// 比例佣金模式
if config.CalculationMode == "percentage" {
    userCommission := orderAmount.Mul(config.UserDirectRate) // 100元 * 30% = 30元
    agentCommission := orderAmount.Mul(config.AgentTeamRate) // 100元 * 8% = 8元
}

// 固定金额佣金模式
if config.CalculationMode == "fixed_amount" {
    userCommission := config.UserDirectAmount  // 固定20元
    agentCommission := config.AgentTeamAmount  // 固定12元
}
```

### 3. 产品级佣金配置优先级
```go
// 产品级配置优先于业务线配置
func (s *BusinessLineCommissionService) getCommissionConfig(req *CommissionCalculationRequest) {
    // 1. 优先查询产品级配置
    if req.CommissionMode == "product_custom" {
        productConfig := s.getProductCommissionConfig(req.ProductType, req.ProductID)
        if productConfig.CommissionEnabled {
            return productConfig
        }
    }

    // 2. 使用业务线默认配置
    return s.getBusinessLineConfig(req.BusinessLine, req.OrderType)
}
```

### 4. 智能体上架佣金配置示例
```go
// 智能体上架时设置佣金配置
POST /api/v1/product-commission/configs
{
    "product_type": "agent",
    "product_id": "AGENT_001",
    "product_name": "论文写作助手",
    "commission_enabled": true,
    "calculation_mode": "fixed_amount",
    "referral_mode": 1,
    "user_direct_amount": 20.00,      // 推广用户B固定获得20元
    "user_indirect_amount": 8.00,     // 推广用户A固定获得8元
    "agent_team_amount": 12.00        // 代理商固定获得12元
}
```

### 5. API按天批量结算
```go
// 实时记录API使用量
func (s *BusinessLineCommissionService) recordDailyUsage(req *CommissionCalculationRequest) {
    // 累加当天使用量，不立即计算佣金
}

// 定时批量计算佣金
func (s *CommissionSchedulerService) ProcessDailyAPICommissions() {
    // 每天凌晨2点批量计算前一天的API佣金
}
```

### 6. 会员卡一次性结算
```go
// 会员卡销售：一次性结算全部佣金
func (s *BusinessLineCommissionService) ProcessMembershipCardCommission(cardPrice decimal.Decimal) {
    // 按会员卡总价格一次性计算佣金，不按API配额分摊
    return s.calculateImmediateCommission(req, config)
}
```

### 7. 租户ID和推广员ID生成机制
```go
// 租户ID生成：{年份}{业务标识}{4位序号}
tenantID := "2025CeesAiDr0716"  // AI医生代理商
tenantID := "2025CeesAgent0001" // 智能体代理商

// 推广员ID生成：{租户ID}_REF{5位序号}
referrerID := "2025CeesAiDr0716_REF10001" // 从10001开始
referrerID := "2025CeesAiDr0716_REF10002" // 递增分配

// 用户ID生成：{租户ID}_USER{5位序号}
userID := "2025CeesAiDr0716_USER00001" // 从1开始
userID := "2025CeesAiDr0716_USER00002" // 递增分配

// 推广链接格式
referralLink := "https://aidoctor.cees.cc?ref=2025CeesAiDr0716_REF10001&mode=1"
```

### 8. 固定金额佣金分配示例
```
智能体A售价100元，设置固定金额佣金：

一级代理商模式（租户：2025CeesAiDr0716）：
- 推广用户B (REF10002)：固定20元（不管订单金额多少）
- 推广用户A (REF10001)：固定8元
- 代理商B (2025CeesAiDr0716)：固定12元 + 缺失层级补偿
总计：40元固定佣金

二级代理商模式（租户：2025CeesAgent0001）：
- 推广用户B (REF10002)：固定20元
- 推广用户A (REF10001)：固定8元
- 代理商B (2025CeesAgent0002)：固定10元
- 代理商A (2025CeesAgent0001)：固定5元
总计：43元固定佣金
```

### 9. Material Design 3.0 前端特性
```css
/* 动态颜色系统 */
.md-surface-container {
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 1025px) {
    .metrics-grid { grid-template-columns: repeat(4, 1fr); }
}

/* Material You 个性化 */
.commission-card {
    background: var(--md-sys-color-primary-container);
    border-radius: 16px;
    elevation: 1;
}
```

### 10. 全站推广链接体系
```javascript
// 全站URL参数处理
class URLParameterMiddleware {
    generateURL(path, additionalParams = {}) {
        const params = this.getCurrentParams();
        const urlParams = new URLSearchParams();

        // 必需参数
        if (params.tenant) urlParams.set('tenant', params.tenant);
        if (params.uid) urlParams.set('uid', params.uid);

        // 推广参数（如果存在）
        if (params.ref) urlParams.set('ref', params.ref);

        return `${path}?${urlParams.toString()}`;
    }
}

// 100%推广关系绑定保证
func (s *MultipleBindingStrategy) EnsureReferralBinding(
    tenantID, referrerID, userID, visitorIP, userAgent string) error {

    // 策略1：URL参数绑定
    // 策略2：Cookie绑定
    // 策略3：IP地址绑定（24小时内）
    // 策略4：设备指纹绑定

    return nil // 多重策略确保100%绑定成功率
}
```

### 11. 推广权限管理机制
```go
// 推广权限申请
func (s *ReferralPermissionService) ApplyReferralPermission(
    tenantID, userID, reason string) error {
    // 用户申请推广权限，状态为pending
}

// 推广权限审核
func (s *ReferralPermissionService) ApproveReferralPermission(
    tenantID, userID, reviewerID, comment string) error {
    // 管理员审核通过，生成推广员ID和推广码
    // 状态变更为approved，推广权限开通
}

// 佣金权限验证
func (v *CommissionPermissionValidator) ValidateCommissionChain(
    tenantID, consumerUserID string) (*CommissionChainValidation, error) {
    // 验证推广链中每个层级的推广权限
    // 无权限层级的佣金归平台所有
}

// 推广权限控制示例
5级推广链路：代理商A → 代理商B → 推广用户A → 推广用户B → 用户C
如果推广用户A未开通推广权限：
- 推广用户A：无佣金（权限未开通）
- 代理商B：获得团队佣金 + 推广用户A应得佣金（归平台后重新分配）
```

### 12. 租户隔离和数据安全
```go
// 租户数据隔离
func (s *BusinessLineCommissionService) GetCommissionsByTenant(tenantID string) {
    // 所有查询都必须包含租户ID过滤
    query := s.db.Where("tenant_id = ?", tenantID)
}

// 推广员权限验证
func (s *ReferrerService) ValidateReferrerAccess(referrerID, tenantID string) bool {
    // 验证推广员是否属于指定租户
    return strings.HasPrefix(referrerID, tenantID+"_REF")
}

// 跨租户访问防护
func (s *TenantService) CheckTenantAccess(userTenantID, resourceTenantID string) error {
    if userTenantID != resourceTenantID {
        return errors.New("跨租户访问被拒绝")
    }
    return nil
}
```

## 🎉 预期效果

### 业务价值
1. **精准佣金计算**：充值不参与佣金，只有实际消费才计算佣金
2. **双重佣金模式**：支持比例佣金和固定金额佣金两种计算模式
3. **产品级佣金配置**：智能体、插件、API模型可独立设置佣金规则
4. **多业务线支持**：智能体、插件、API、会员卡四大业务线独立佣金规则
5. **灵活结算模式**：即时结算、按天批量结算、不参与佣金三种模式
6. **会员卡特殊处理**：API配额按一次性结算，不按使用量分摊

### 技术优势
1. **双重计算模式**：统一支持比例佣金和固定金额佣金计算
2. **产品级配置**：每个产品可独立设置佣金规则，优先级高于业务线配置
3. **业务线隔离**：每个业务线独立的佣金配置和计算逻辑
4. **高性能处理**：API使用量按天批量处理，减少实时计算压力
5. **灵活配置**：支持不同租户的个性化业务线和产品级佣金配置
6. **完整监控**：实时监控各业务线和产品的佣金计算和结算状态

### 商业价值突破
1. **产品差异化定价**：不同智能体、插件可设置不同的佣金激励
2. **精准营销激励**：高价值产品可设置更高的固定佣金吸引推广
3. **灵活佣金策略**：新产品推广期可设置固定高佣金，成熟产品使用比例佣金
4. **完整生态闭环**：从产品上架到佣金分配的完整商业生态
5. **SAAS多租户支持**：完整的租户隔离和管理体系
6. **现代化用户体验**：Material Design 3.0设计规范，响应式多端适配
7. **全站推广链接**：任何页面链接都能进行推广关系绑定
8. **100%绑定成功率**：多重策略确保推广关系绑定不丢失
9. **推广权限管理**：完整的推广权限申请、审核、开通流程
10. **佣金权限控制**：只有开通推广权限的用户才能获得佣金

### 系统架构优势
1. **租户管理体系**：完整的租户ID生成、推广员ID分配、用户ID管理
2. **数据安全隔离**：严格的租户数据隔离和跨租户访问防护
3. **ID生成规范**：推广员ID从10001开始，全局唯一性保证
4. **现代化前端**：Material Design 3.0组件库，动态颜色系统
5. **响应式设计**：支持手机、平板、桌面多端完美适配
6. **完整用户体验**：从推广分享到佣金提现的完整用户旅程

### 交付成果
1. **完整数据库设计**：10张核心表，支持租户隔离和ID生成
2. **后端服务架构**：Go微服务架构，支持双重佣金计算
3. **前端界面系统**：Material Design 3.0用户前端 + 管理员后台
4. **API接口文档**：完整的RESTful API接口设计
5. **部署运维文档**：详细的部署和运维指南
6. **用户培训材料**：完整的用户使用手册

**总开发时间：32天，完整实现战略级双模式推广佣金体系的所有功能，包含双重佣金计算模式、产品级佣金配置、租户管理体系、推广员ID生成规范、全站推广链接体系、推广权限管理机制和Material Design 3.0前端界面！** 🚀

---

## 📋 文档总结

本文档提供了一个**超级完整的战略级双模式推广佣金体系开发方案**，涵盖：

✅ **4个核心业务需求**：充值消费分离、多业务线佣金、会员卡特殊处理、双重佣金模式
✅ **租户管理体系**：租户ID生成、推广员ID分配（从10001开始）、用户ID管理
✅ **10张数据库表**：完整的数据库架构设计和索引优化
✅ **Go微服务架构**：3个核心服务，支持产品级和业务线级佣金配置
✅ **Material Design 3.0前端**：用户前端 + 管理员后台，响应式多端适配
✅ **完整API接口**：RESTful API设计，支持所有业务场景
✅ **32天实施计划**：详细的开发任务分解和时间安排
✅ **全站推广链接体系**：100%推广关系绑定成功率保证
✅ **多重绑定策略**：URL参数、Cookie、IP地址、设备指纹四重保障
✅ **推广权限管理体系**：完整的权限申请、审核、开通、管理流程
✅ **佣金权限控制机制**：只有开通推广权限的用户才能获得佣金

**这是一个可以直接交给开发团队执行的超级完整技术方案，不仅确保推广员的每一次推广都能获得应有的佣金收益，更通过推广权限管理机制确保佣金分配的合理性和可控性！** 🎯

## 🎨 Material Design 3.0 前端页面设计

### 🎯 设计规范说明

#### 1. Material Design 3.0 核心特性
```
- Dynamic Color：动态颜色系统，支持主题色自适应
- Material You：个性化设计语言
- 组件升级：Button、Card、Navigation等组件全面升级
- 响应式设计：支持手机、平板、桌面多端适配
- 无障碍访问：完整的可访问性支持
```

#### 2. 主题色彩方案
```css
/* 主色调 */
--md-sys-color-primary: #1976D2;           /* 主色 */
--md-sys-color-on-primary: #FFFFFF;        /* 主色上的文字 */
--md-sys-color-primary-container: #E3F2FD; /* 主色容器 */
--md-sys-color-on-primary-container: #0D47A1; /* 主色容器上的文字 */

/* 次要色调 */
--md-sys-color-secondary: #03DAC6;         /* 次要色 */
--md-sys-color-on-secondary: #000000;      /* 次要色上的文字 */
--md-sys-color-secondary-container: #E0F7FA; /* 次要色容器 */

/* 表面色调 */
--md-sys-color-surface: #FEFBFF;           /* 表面色 */
--md-sys-color-on-surface: #1C1B1F;        /* 表面上的文字 */
--md-sys-color-surface-variant: #E7E0EC;   /* 表面变体 */

/* 错误色调 */
--md-sys-color-error: #BA1A1A;             /* 错误色 */
--md-sys-color-on-error: #FFFFFF;          /* 错误色上的文字 */
```

### 📱 用户前端页面设计

#### 1. 推广分享页面
```html
<!-- 推广分享主页 -->
<div class="md-surface-container">
    <!-- 顶部应用栏 -->
    <md-top-app-bar>
        <md-icon-button slot="navigation">
            <md-icon>menu</md-icon>
        </md-icon-button>
        <div slot="headline">推广分享</div>
        <md-icon-button slot="trailing">
            <md-icon>notifications</md-icon>
        </md-icon-button>
    </md-top-app-bar>

    <!-- 推广统计卡片 -->
    <md-card class="stats-card">
        <div class="card-content">
            <h2 class="md-typescale-headline-medium">推广统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">128</span>
                    <span class="stat-label">累计推广</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">¥2,580</span>
                    <span class="stat-label">总佣金收入</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">¥680</span>
                    <span class="stat-label">待结算佣金</span>
                </div>
            </div>
        </div>
    </md-card>

    <!-- 推广工具卡片 -->
    <md-card class="tools-card">
        <div class="card-content">
            <h3 class="md-typescale-title-medium">推广工具</h3>

            <!-- 推广链接 -->
            <div class="tool-item">
                <md-icon>link</md-icon>
                <div class="tool-content">
                    <span class="tool-title">推广链接</span>
                    <span class="tool-subtitle">分享链接获得佣金</span>
                </div>
                <md-text-button>复制链接</md-text-button>
            </div>

            <!-- 二维码 -->
            <div class="tool-item">
                <md-icon>qr_code</md-icon>
                <div class="tool-content">
                    <span class="tool-title">推广二维码</span>
                    <span class="tool-subtitle">扫码注册获得佣金</span>
                </div>
                <md-text-button>查看二维码</md-text-button>
            </div>

            <!-- 海报分享 */
            <div class="tool-item">
                <md-icon>image</md-icon>
                <div class="tool-content">
                    <span class="tool-title">推广海报</span>
                    <span class="tool-subtitle">精美海报一键分享</span>
                </div>
                <md-text-button>生成海报</md-text-button>
            </div>
        </div>
    </md-card>

    <!-- 浮动操作按钮 -->
    <md-fab class="share-fab">
        <md-icon slot="icon">share</md-icon>
    </md-fab>
</div>
```

#### 2. 佣金收益页面
```html
<!-- 佣金收益页面 -->
<div class="md-surface-container">
    <!-- 顶部应用栏 -->
    <md-top-app-bar>
        <md-icon-button slot="navigation">
            <md-icon>arrow_back</md-icon>
        </md-icon-button>
        <div slot="headline">佣金收益</div>
        <md-icon-button slot="trailing">
            <md-icon>filter_list</md-icon>
        </md-icon-button>
    </md-top-app-bar>

    <!-- 收益概览 -->
    <md-card class="earnings-overview">
        <div class="card-content">
            <div class="earnings-header">
                <h2 class="md-typescale-headline-small">本月收益</h2>
                <md-chip-set>
                    <md-filter-chip selected>全部</md-filter-chip>
                    <md-filter-chip>智能体</md-filter-chip>
                    <md-filter-chip>插件</md-filter-chip>
                    <md-filter-chip>API</md-filter-chip>
                </md-chip-set>
            </div>

            <div class="earnings-amount">
                <span class="amount-primary">¥1,280.50</span>
                <span class="amount-change positive">+15.2%</span>
            </div>

            <!-- 收益趋势图 -->
            <div class="earnings-chart">
                <canvas id="earningsChart"></canvas>
            </div>
        </div>
    </md-card>

    <!-- 佣金明细列表 -->
    <md-list class="commission-list">
        <md-list-item>
            <md-icon slot="start">smart_toy</md-icon>
            <div slot="headline">智能体购买佣金</div>
            <div slot="supporting-text">用户购买论文写作助手</div>
            <div slot="trailing-supporting-text">2024-01-15</div>
            <div slot="end">+¥20.00</div>
        </md-list-item>

        <md-list-item>
            <md-icon slot="start">extension</md-icon>
            <div slot="headline">插件购买佣金</div>
            <div slot="supporting-text">用户购买短视频生成工具</div>
            <div slot="trailing-supporting-text">2024-01-14</div>
            <div slot="end">+¥15.50</div>
        </md-list-item>

        <md-list-item>
            <md-icon slot="start">api</md-icon>
            <div slot="headline">API使用佣金</div>
            <div slot="supporting-text">Claude 4 API日结算</div>
            <div slot="trailing-supporting-text">2024-01-13</div>
            <div slot="end">+¥8.30</div>
        </md-list-item>
    </md-list>

    <!-- 提现按钮 -->
    <div class="withdraw-section">
        <md-filled-button class="withdraw-button">
            <md-icon slot="icon">account_balance_wallet</md-icon>
            申请提现
        </md-filled-button>
    </div>
</div>
```

#### 3. 提现申请页面
```html
<!-- 提现申请页面 -->
<div class="md-surface-container">
    <!-- 顶部应用栏 -->
    <md-top-app-bar>
        <md-icon-button slot="navigation">
            <md-icon>arrow_back</md-icon>
        </md-icon-button>
        <div slot="headline">申请提现</div>
    </md-top-app-bar>

    <!-- 可提现金额 -->
    <md-card class="available-balance">
        <div class="card-content">
            <h3 class="md-typescale-title-medium">可提现金额</h3>
            <div class="balance-amount">¥1,280.50</div>
            <div class="balance-note">最低提现金额：¥100</div>
        </div>
    </md-card>

    <!-- 提现表单 -->
    <md-card class="withdraw-form">
        <div class="card-content">
            <h3 class="md-typescale-title-medium">提现信息</h3>

            <!-- 提现金额 -->
            <md-outlined-text-field
                label="提现金额"
                type="number"
                prefix-text="¥"
                supporting-text="请输入提现金额">
            </md-outlined-text-field>

            <!-- 提现方式 -->
            <div class="withdraw-method">
                <h4 class="md-typescale-title-small">提现方式</h4>
                <md-radio-group>
                    <md-radio name="method" value="alipay" checked>
                        <md-icon slot="icon">payment</md-icon>
                        支付宝
                    </md-radio>
                    <md-radio name="method" value="wechat">
                        <md-icon slot="icon">chat</md-icon>
                        微信支付
                    </md-radio>
                    <md-radio name="method" value="bank">
                        <md-icon slot="icon">account_balance</md-icon>
                        银行卡
                    </md-radio>
                </md-radio-group>
            </div>

            <!-- 收款账户 -->
            <md-outlined-text-field
                label="收款账户"
                supporting-text="请输入支付宝账号">
            </md-outlined-text-field>

            <!-- 提现说明 -->
            <md-card class="withdraw-notice">
                <div class="card-content">
                    <md-icon>info</md-icon>
                    <div class="notice-content">
                        <h4>提现说明</h4>
                        <ul>
                            <li>工作日申请，1-3个工作日到账</li>
                            <li>周末及节假日申请，顺延至工作日处理</li>
                            <li>单笔提现手续费：2元</li>
                        </ul>
                    </div>
                </div>
            </md-card>

            <!-- 提交按钮 -->
            <md-filled-button class="submit-button">
                提交申请
            </md-filled-button>
        </div>
    </md-card>
</div>
```

#### 4. 推广团队页面
```html
<!-- 推广团队页面 -->
<div class="md-surface-container">
    <!-- 顶部应用栏 -->
    <md-top-app-bar>
        <md-icon-button slot="navigation">
            <md-icon>arrow_back</md-icon>
        </md-icon-button>
        <div slot="headline">我的团队</div>
        <md-icon-button slot="trailing">
            <md-icon>search</md-icon>
        </md-icon-button>
    </md-top-app-bar>

    <!-- 团队统计 -->
    <md-card class="team-stats">
        <div class="card-content">
            <h3 class="md-typescale-title-medium">团队统计</h3>
            <div class="stats-row">
                <div class="stat-item">
                    <span class="stat-number">68</span>
                    <span class="stat-label">直推用户</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">156</span>
                    <span class="stat-label">团队总人数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">12</span>
                    <span class="stat-label">活跃推广员</span>
                </div>
            </div>
        </div>
    </md-card>

    <!-- 团队层级导航 -->
    <md-tabs>
        <md-primary-tab>一级团队</md-primary-tab>
        <md-primary-tab>二级团队</md-primary-tab>
        <md-primary-tab>全部团队</md-primary-tab>
    </md-tabs>

    <!-- 团队成员列表 -->
    <md-list class="team-list">
        <md-list-item>
            <md-avatar slot="start">
                <img src="avatar1.jpg" alt="用户头像">
            </md-avatar>
            <div slot="headline">张三</div>
            <div slot="supporting-text">注册时间：2024-01-10</div>
            <div slot="trailing-supporting-text">
                <md-chip>一级</md-chip>
            </div>
            <div slot="end">
                <div class="member-stats">
                    <span>推广：5人</span>
                    <span>佣金：¥120</span>
                </div>
            </div>
        </md-list-item>

        <md-list-item>
            <md-avatar slot="start">
                <img src="avatar2.jpg" alt="用户头像">
            </md-avatar>
            <div slot="headline">李四</div>
            <div slot="supporting-text">注册时间：2024-01-08</div>
            <div slot="trailing-supporting-text">
                <md-chip>一级</md-chip>
            </div>
            <div slot="end">
                <div class="member-stats">
                    <span>推广：8人</span>
                    <span>佣金：¥200</span>
                </div>
            </div>
        </md-list-item>
    </md-list>
</div>
```

### 🖥️ 管理员后台页面设计

#### 1. 代理商管理页面
```html
<!-- 代理商管理页面 -->
<div class="admin-container">
    <!-- 侧边导航 -->
    <md-navigation-drawer>
        <div slot="headline">管理后台</div>
        <md-navigation-drawer-item>
            <md-icon slot="icon">dashboard</md-icon>
            <div slot="headline">数据概览</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item selected>
            <md-icon slot="icon">business</md-icon>
            <div slot="headline">代理商管理</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">campaign</md-icon>
            <div slot="headline">推广系统</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">payments</md-icon>
            <div slot="headline">佣金管理</div>
        </md-navigation-drawer-item>
    </md-navigation-drawer>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="md-typescale-headline-large">代理商管理</h1>
            <md-filled-button>
                <md-icon slot="icon">add</md-icon>
                新增代理商
            </md-filled-button>
        </div>

        <!-- 筛选工具栏 -->
        <md-card class="filter-toolbar">
            <div class="card-content">
                <md-outlined-text-field
                    label="搜索代理商"
                    type="search">
                    <md-icon slot="leading-icon">search</md-icon>
                </md-outlined-text-field>

                <md-outlined-select label="业务类型">
                    <md-select-option value="all">全部类型</md-select-option>
                    <md-select-option value="ai_doctor">AI医生</md-select-option>
                    <md-select-option value="agent_market">智能体市场</md-select-option>
                    <md-select-option value="plugin_tools">插件工具</md-select-option>
                </md-outlined-select>

                <md-outlined-select label="代理商等级">
                    <md-select-option value="all">全部等级</md-select-option>
                    <md-select-option value="1">一级代理商</md-select-option>
                    <md-select-option value="2">二级代理商</md-select-option>
                </md-outlined-select>

                <md-outlined-select label="状态">
                    <md-select-option value="all">全部状态</md-select-option>
                    <md-select-option value="active">正常</md-select-option>
                    <md-select-option value="suspended">暂停</md-select-option>
                    <md-select-option value="inactive">停用</md-select-option>
                </md-outlined-select>
            </div>
        </md-card>

        <!-- 代理商列表 -->
        <md-card class="data-table-card">
            <table class="md-data-table">
                <thead>
                    <tr>
                        <th>租户ID</th>
                        <th>代理商名称</th>
                        <th>业务类型</th>
                        <th>等级</th>
                        <th>联系人</th>
                        <th>域名</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2025CeesAiDr0716</td>
                        <td>AI医生智能助手</td>
                        <td>
                            <md-chip>AI医生</md-chip>
                        </td>
                        <td>一级代理商</td>
                        <td>张医生</td>
                        <td>aidoctor.cees.cc</td>
                        <td>
                            <md-chip class="status-active">正常</md-chip>
                        </td>
                        <td>2024-01-01</td>
                        <td>
                            <md-icon-button>
                                <md-icon>edit</md-icon>
                            </md-icon-button>
                            <md-icon-button>
                                <md-icon>visibility</md-icon>
                            </md-icon-button>
                            <md-icon-button>
                                <md-icon>more_vert</md-icon>
                            </md-icon-button>
                        </td>
                    </tr>
                    <tr>
                        <td>2025CeesAgent0001</td>
                        <td>智能体应用商店</td>
                        <td>
                            <md-chip>智能体市场</md-chip>
                        </td>
                        <td>二级代理商</td>
                        <td>李经理</td>
                        <td>agents.cees.cc</td>
                        <td>
                            <md-chip class="status-active">正常</md-chip>
                        </td>
                        <td>2024-01-02</td>
                        <td>
                            <md-icon-button>
                                <md-icon>edit</md-icon>
                            </md-icon-button>
                            <md-icon-button>
                                <md-icon>visibility</md-icon>
                            </md-icon-button>
                            <md-icon-button>
                                <md-icon>more_vert</md-icon>
                            </md-icon-button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 分页 -->
            <div class="table-pagination">
                <span>显示 1-10 条，共 25 条</span>
                <md-icon-button>
                    <md-icon>chevron_left</md-icon>
                </md-icon-button>
                <md-icon-button>
                    <md-icon>chevron_right</md-icon>
                </md-icon-button>
            </div>
        </md-card>
    </div>
</div>
```

#### 2. 佣金配置管理页面
```html
<!-- 佣金配置管理页面 -->
<div class="admin-container">
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="md-typescale-headline-large">佣金配置管理</h1>
            <md-filled-button>
                <md-icon slot="icon">add</md-icon>
                新增配置
            </md-filled-button>
        </div>

        <!-- 配置类型选择 -->
        <md-tabs>
            <md-primary-tab>业务线配置</md-primary-tab>
            <md-primary-tab>产品级配置</md-primary-tab>
            <md-primary-tab>会员卡配置</md-primary-tab>
        </md-tabs>

        <!-- 业务线佣金配置 -->
        <md-card class="config-section">
            <div class="card-content">
                <h3 class="md-typescale-title-medium">业务线佣金配置</h3>

                <!-- 配置表单 -->
                <div class="config-form">
                    <div class="form-row">
                        <md-outlined-select label="业务线">
                            <md-select-option value="agent_market">智能体市场</md-select-option>
                            <md-select-option value="plugin_tools">AI插件工具</md-select-option>
                            <md-select-option value="llm_api">大模型API</md-select-option>
                            <md-select-option value="membership_card">会员卡销售</md-select-option>
                        </md-outlined-select>

                        <md-outlined-select label="推广模式">
                            <md-select-option value="1">一级代理商模式</md-select-option>
                            <md-select-option value="2">二级代理商模式</md-select-option>
                        </md-outlined-select>

                        <md-outlined-select label="计算模式">
                            <md-select-option value="percentage">按比例计算</md-select-option>
                            <md-select-option value="fixed_amount">固定金额</md-select-option>
                        </md-outlined-select>
                    </div>

                    <!-- 比例佣金配置 -->
                    <div class="percentage-config">
                        <h4 class="md-typescale-title-small">比例佣金配置</h4>
                        <div class="form-row">
                            <md-outlined-text-field
                                label="用户一级推广佣金率"
                                type="number"
                                suffix-text="%"
                                value="30">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="用户二级推广佣金率"
                                type="number"
                                suffix-text="%"
                                value="10">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="代理商团队佣金率"
                                type="number"
                                suffix-text="%"
                                value="8">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="合伙人总佣金率"
                                type="number"
                                suffix-text="%"
                                value="3">
                            </md-outlined-text-field>
                        </div>
                    </div>

                    <!-- 固定金额佣金配置 -->
                    <div class="fixed-amount-config">
                        <h4 class="md-typescale-title-small">固定金额佣金配置</h4>
                        <div class="form-row">
                            <md-outlined-text-field
                                label="用户一级推广固定佣金"
                                type="number"
                                prefix-text="¥">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="用户二级推广固定佣金"
                                type="number"
                                prefix-text="¥">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="代理商团队固定佣金"
                                type="number"
                                prefix-text="¥">
                            </md-outlined-text-field>
                            <md-outlined-text-field
                                label="合伙人总固定佣金"
                                type="number"
                                prefix-text="¥">
                            </md-outlined-text-field>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="form-actions">
                        <md-outlined-button>重置</md-outlined-button>
                        <md-filled-button>保存配置</md-filled-button>
                    </div>
                </div>
            </div>
        </md-card>

        <!-- 产品级佣金配置 -->
        <md-card class="product-config-section">
            <div class="card-content">
                <h3 class="md-typescale-title-medium">产品级佣金配置</h3>

                <!-- 产品搜索 -->
                <div class="product-search">
                    <md-outlined-text-field
                        label="搜索产品"
                        type="search">
                        <md-icon slot="leading-icon">search</md-icon>
                    </md-outlined-text-field>
                    <md-outlined-select label="产品类型">
                        <md-select-option value="agent">智能体</md-select-option>
                        <md-select-option value="plugin">插件</md-select-option>
                        <md-select-option value="api_model">API模型</md-select-option>
                    </md-outlined-select>
                </div>

                <!-- 产品列表 -->
                <div class="product-list">
                    <md-card class="product-item">
                        <div class="card-content">
                            <div class="product-info">
                                <h4>论文写作助手</h4>
                                <span class="product-type">智能体</span>
                                <span class="product-id">AGENT_001</span>
                            </div>
                            <div class="commission-status">
                                <md-switch checked></md-switch>
                                <span>已启用佣金</span>
                            </div>
                            <md-text-button>配置佣金</md-text-button>
                        </div>
                    </md-card>

                    <md-card class="product-item">
                        <div class="card-content">
                            <div class="product-info">
                                <h4>短视频生成工具</h4>
                                <span class="product-type">插件</span>
                                <span class="product-id">PLUGIN_001</span>
                            </div>
                            <div class="commission-status">
                                <md-switch></md-switch>
                                <span>未启用佣金</span>
                            </div>
                            <md-text-button>配置佣金</md-text-button>
                        </div>
                    </md-card>
                </div>
            </div>
        </md-card>
    </div>
</div>
```

#### 3. 数据统计大屏页面
```html
<!-- 数据统计大屏页面 -->
<div class="admin-container">
    <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="md-typescale-headline-large">数据统计大屏</h1>
            <div class="time-selector">
                <md-chip-set>
                    <md-filter-chip selected>今日</md-filter-chip>
                    <md-filter-chip>本周</md-filter-chip>
                    <md-filter-chip>本月</md-filter-chip>
                    <md-filter-chip>自定义</md-filter-chip>
                </md-chip-set>
            </div>
        </div>

        <!-- 核心指标卡片 -->
        <div class="metrics-grid">
            <md-card class="metric-card">
                <div class="card-content">
                    <div class="metric-header">
                        <md-icon>people</md-icon>
                        <span>总推广人数</span>
                    </div>
                    <div class="metric-value">12,580</div>
                    <div class="metric-change positive">+8.5%</div>
                </div>
            </md-card>

            <md-card class="metric-card">
                <div class="card-content">
                    <div class="metric-header">
                        <md-icon>shopping_cart</md-icon>
                        <span>总订单数</span>
                    </div>
                    <div class="metric-value">3,240</div>
                    <div class="metric-change positive">+12.3%</div>
                </div>
            </md-card>

            <md-card class="metric-card">
                <div class="card-content">
                    <div class="metric-header">
                        <md-icon>payments</md-icon>
                        <span>总佣金支出</span>
                    </div>
                    <div class="metric-value">¥156,800</div>
                    <div class="metric-change positive">+15.7%</div>
                </div>
            </md-card>

            <md-card class="metric-card">
                <div class="card-content">
                    <div class="metric-header">
                        <md-icon>trending_up</md-icon>
                        <span>转化率</span>
                    </div>
                    <div class="metric-value">25.8%</div>
                    <div class="metric-change negative">-2.1%</div>
                </div>
            </md-card>
        </div>

        <!-- 图表区域 -->
        <div class="charts-grid">
            <!-- 佣金趋势图 -->
            <md-card class="chart-card">
                <div class="card-content">
                    <h3 class="md-typescale-title-medium">佣金支出趋势</h3>
                    <div class="chart-container">
                        <canvas id="commissionTrendChart"></canvas>
                    </div>
                </div>
            </md-card>

            <!-- 业务线分布 -->
            <md-card class="chart-card">
                <div class="card-content">
                    <h3 class="md-typescale-title-medium">业务线佣金分布</h3>
                    <div class="chart-container">
                        <canvas id="businessLineChart"></canvas>
                    </div>
                </div>
            </md-card>

            <!-- 推广员排行 -->
            <md-card class="chart-card">
                <div class="card-content">
                    <h3 class="md-typescale-title-medium">推广员排行榜</h3>
                    <md-list class="ranking-list">
                        <md-list-item>
                            <div slot="start" class="rank-number">1</div>
                            <div slot="headline">张推广员</div>
                            <div slot="supporting-text">2025CeesAiDr0716_REF10001</div>
                            <div slot="end">¥2,580</div>
                        </md-list-item>
                        <md-list-item>
                            <div slot="start" class="rank-number">2</div>
                            <div slot="headline">李推广员</div>
                            <div slot="supporting-text">2025CeesAiDr0716_REF10002</div>
                            <div slot="end">¥2,180</div>
                        </md-list-item>
                        <md-list-item>
                            <div slot="start" class="rank-number">3</div>
                            <div slot="headline">王推广员</div>
                            <div slot="supporting-text">2025CeesAgent0001_REF10001</div>
                            <div slot="end">¥1,950</div>
                        </md-list-item>
                    </md-list>
                </div>
            </md-card>
        </div>

        <!-- 实时数据流 -->
        <md-card class="realtime-data">
            <div class="card-content">
                <h3 class="md-typescale-title-medium">实时数据流</h3>
                <div class="data-stream">
                    <div class="stream-item">
                        <md-icon>person_add</md-icon>
                        <span>用户 2025CeesAiDr0716_USER00156 通过推广注册</span>
                        <span class="timestamp">刚刚</span>
                    </div>
                    <div class="stream-item">
                        <md-icon>shopping_cart</md-icon>
                        <span>订单 ORD_20240115_001 产生佣金 ¥25.50</span>
                        <span class="timestamp">1分钟前</span>
                    </div>
                    <div class="stream-item">
                        <md-icon>account_balance_wallet</md-icon>
                        <span>推广员 REF10001 申请提现 ¥500</span>
                        <span class="timestamp">3分钟前</span>
                    </div>
                </div>
            </div>
        </md-card>
    </div>
</div>
```

### 📱 响应式设计适配

#### 1. 移动端适配
```css
/* 移动端样式 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .md-data-table {
        font-size: 14px;
    }

    .admin-container {
        flex-direction: column;
    }

    .md-navigation-drawer {
        position: fixed;
        z-index: 1000;
    }
}
```

#### 2. 平板端适配
```css
/* 平板端样式 */
@media (min-width: 769px) and (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-row {
        grid-template-columns: repeat(2, 1fr);
    }
}
```

#### 3. 桌面端适配
```css
/* 桌面端样式 */
@media (min-width: 1025px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .metrics-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .charts-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .form-row {
        grid-template-columns: repeat(4, 1fr);
    }

    .admin-container {
        display: flex;
        min-height: 100vh;
    }

    .md-navigation-drawer {
        position: relative;
        width: 280px;
    }

    .main-content {
        flex: 1;
        padding: 24px;
    }
}
```

#### 4. 推广系统管理页面（新增）
```html
<!-- 推广系统管理页面 -->
<div class="admin-container">
    <!-- 侧边导航 -->
    <md-navigation-drawer>
        <div slot="headline">推广系统</div>
        <md-navigation-drawer-item selected>
            <md-icon slot="icon">dashboard</md-icon>
            <div slot="headline">推广数据大屏</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">store</md-icon>
            <div slot="headline">分销商管理</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">link</md-icon>
            <div slot="headline">推广链接管理</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">payments</md-icon>
            <div slot="headline">佣金管理</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">campaign</md-icon>
            <div slot="headline">推广活动</div>
        </md-navigation-drawer-item>
        <md-navigation-drawer-item>
            <md-icon slot="icon">settings</md-icon>
            <div slot="headline">推广设置</div>
        </md-navigation-drawer-item>
    </md-navigation-drawer>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 推广数据大屏 -->
        <div class="page-header">
            <h1 class="md-typescale-headline-large">推广数据大屏</h1>
            <div class="time-selector">
                <md-chip-set>
                    <md-filter-chip selected>今日</md-filter-chip>
                    <md-filter-chip>本周</md-filter-chip>
                    <md-filter-chip>本月</md-filter-chip>
                </md-chip-set>
            </div>
        </div>

        <!-- 推广权限申请审核 -->
        <md-card class="permission-review-card">
            <div class="card-content">
                <h3 class="md-typescale-title-medium">推广权限申请审核</h3>

                <table class="md-data-table">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户姓名</th>
                            <th>申请时间</th>
                            <th>申请理由</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2025CeesAiDr0716_USER00005</td>
                            <td>王小明</td>
                            <td>2024-01-15 10:30</td>
                            <td>希望通过推广获得额外收入</td>
                            <td>
                                <md-chip class="status-pending">待审核</md-chip>
                            </td>
                            <td>
                                <md-filled-button onclick="approvePermission('2025CeesAiDr0716_USER00005')">
                                    通过
                                </md-filled-button>
                                <md-outlined-button onclick="rejectPermission('2025CeesAiDr0716_USER00005')">
                                    拒绝
                                </md-outlined-button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </md-card>

        <!-- 分销商管理 -->
        <md-card class="distributors-table-card">
            <div class="card-content">
                <h3 class="md-typescale-title-medium">分销商管理</h3>

                <table class="md-data-table">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>推广员ID</th>
                            <th>姓名</th>
                            <th>推广码</th>
                            <th>推广权限</th>
                            <th>佣金权限</th>
                            <th>推广人数</th>
                            <th>总佣金</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2025CeesAiDr0716_USER00001</td>
                            <td>2025CeesAiDr0716_REF10001</td>
                            <td>张推广员</td>
                            <td>AIDOC001</td>
                            <td>
                                <md-switch checked onchange="toggleReferralPermission('2025CeesAiDr0716_USER00001', this.checked)"></md-switch>
                            </td>
                            <td>
                                <md-switch checked onchange="toggleCommissionPermission('2025CeesAiDr0716_USER00001', this.checked)"></md-switch>
                            </td>
                            <td>68</td>
                            <td>¥2,580.50</td>
                            <td>
                                <md-chip class="status-active">正常</md-chip>
                            </td>
                            <td>
                                <md-icon-button onclick="viewDistributorDetail('2025CeesAiDr0716_USER00001')">
                                    <md-icon>visibility</md-icon>
                                </md-icon-button>
                            </td>
                        </tr>
                        <tr>
                            <td>2025CeesAiDr0716_USER00003</td>
                            <td>-</td>
                            <td>王普通用户</td>
                            <td>-</td>
                            <td>
                                <md-switch onchange="toggleReferralPermission('2025CeesAiDr0716_USER00003', this.checked)"></md-switch>
                            </td>
                            <td>
                                <md-switch disabled></md-switch>
                            </td>
                            <td>0</td>
                            <td>¥0.00</td>
                            <td>
                                <md-chip class="status-disabled">未开通</md-chip>
                            </td>
                            <td>
                                <md-icon-button onclick="enableReferralPermission('2025CeesAiDr0716_USER00003')">
                                    <md-icon>person_add</md-icon>
                                </md-icon-button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </md-card>
    </div>
</div>

<script>
// 切换推广权限
function toggleReferralPermission(userId, enabled) {
    fetch('/api/v1/referral-permission/toggle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId, enabled: enabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(enabled ? '推广权限已开通' : '推广权限已关闭');
            location.reload();
        } else {
            showToast('操作失败：' + data.error);
        }
    });
}

// 审核通过推广权限
function approvePermission(userId) {
    const comment = prompt('请输入审核备注：');
    if (comment === null) return;

    fetch('/api/v1/referral-permission/approve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId, comment: comment })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('推广权限审核通过');
            location.reload();
        } else {
            showToast('操作失败：' + data.error);
        }
    });
}
</script>
```
