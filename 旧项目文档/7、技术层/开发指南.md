# AI生态平台开发指南

## 📋 目录

1. [🎯 开发环境搭建](#开发环境搭建)
2. [🏗️ 项目结构](#项目结构)
3. [🔧 开发规范](#开发规范)
4. [🚀 新功能开发](#新功能开发)
5. [🧪 测试指南](#测试指南)
6. [📦 构建部署](#构建部署)
7. [🛠️ 调试技巧](#调试技巧)

---

## 🎯 开发环境搭建

### 必需软件
```bash
# 1. 安装Go 1.21+
go version

# 2. 安装Node.js 18+
node --version
npm --version

# 3. 安装Docker Desktop
docker --version
docker-compose --version

# 4. 安装Git
git --version
```

### 开发工具推荐
- **IDE**: VS Code + Go扩展
- **API测试**: Postman 或 Insomnia
- **数据库工具**: DBeaver 或 pgAdmin
- **Redis工具**: RedisInsight

### 环境配置
```bash
# 1. 克隆项目
git clone <repository-url>
cd ai-ecosystem

# 2. 配置环境变量
cp .env.example .env
# 修改.env中的配置

# 3. 启动开发环境
docker-compose up -d postgres redis
```

---

## 🏗️ 项目结构

### 整体结构
```
ai-ecosystem/
├── ai-users/           # 用户服务
├── ai-agents/          # 智能体服务
├── ai-llms/           # 大模型服务
├── ai-tools/          # 工具服务
├── ai-admin/          # 管理服务
├── frontend/          # 前端应用
├── nginx/             # 负载均衡配置
├── docker/            # Docker相关配置
├── docs/              # 项目文档
├── .env               # 环境变量
└── docker-compose.yml # 容器编排
```

### Go微服务结构
```
ai-users/
├── cmd/
│   └── main.go        # 程序入口
├── internal/
│   ├── handler/       # HTTP处理器
│   ├── service/       # 业务逻辑
│   ├── repository/    # 数据访问
│   ├── model/         # 数据模型
│   └── middleware/    # 中间件
├── pkg/
│   ├── config/        # 配置管理
│   ├── database/      # 数据库连接
│   ├── redis/         # Redis连接
│   └── utils/         # 工具函数
├── api/
│   └── openapi.yaml   # API文档
├── Dockerfile         # 容器构建
├── go.mod            # Go模块
└── go.sum            # 依赖锁定
```

### 前端结构
```
frontend/
├── src/
│   ├── components/    # 组件
│   ├── views/         # 页面
│   ├── router/        # 路由
│   ├── stores/        # 状态管理
│   ├── utils/         # 工具函数
│   └── main.ts        # 入口文件
├── public/            # 静态资源
├── package.json       # 依赖配置
├── vite.config.ts     # 构建配置
└── Dockerfile         # 容器构建
```

---

## 🔧 开发规范

### Go代码规范

#### 项目结构规范
```go
// internal/model/user.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID        string    `json:"id" gorm:"primaryKey"`
    TenantID  string    `json:"tenant_id" gorm:"not null;index"`
    Username  string    `json:"username" gorm:"not null"`
    Email     string    `json:"email" gorm:"not null"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// 表名
func (User) TableName() string {
    return "users"
}
```

#### 服务层规范
```go
// internal/service/user.go
package service

import (
    "context"
    "ai-users/internal/model"
    "ai-users/internal/repository"
)

type UserService struct {
    userRepo repository.UserRepository
    redis    *redis.Client
}

func NewUserService(userRepo repository.UserRepository, redis *redis.Client) *UserService {
    return &UserService{
        userRepo: userRepo,
        redis:    redis,
    }
}

func (s *UserService) GetUser(ctx context.Context, tenantID, userID string) (*model.User, error) {
    // 1. 检查缓存
    if user := s.getUserFromCache(userID); user != nil {
        return user, nil
    }
    
    // 2. 查询数据库
    user, err := s.userRepo.GetByID(ctx, tenantID, userID)
    if err != nil {
        return nil, err
    }
    
    // 3. 更新缓存
    s.setUserCache(user)
    
    return user, nil
}
```

#### HTTP处理器规范
```go
// internal/handler/user.go
package handler

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "ai-users/internal/service"
)

type UserHandler struct {
    userService *service.UserService
}

func NewUserHandler(userService *service.UserService) *UserHandler {
    return &UserHandler{
        userService: userService,
    }
}

func (h *UserHandler) GetProfile(c *gin.Context) {
    tenantID := c.GetString("tenant_id")
    userID := c.GetString("user_id")
    
    user, err := h.userService.GetUser(c.Request.Context(), tenantID, userID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            "code":    500,
            "message": "获取用户信息失败",
            "error":   err.Error(),
        })
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "code":    200,
        "message": "success",
        "data":    user,
    })
}
```

### 前端代码规范

#### Vue组件规范
```vue
<template>
  <div class="user-profile">
    <h2>{{ user.nickname }}</h2>
    <p>{{ user.email }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

interface User {
  id: string
  nickname: string
  email: string
}

const userStore = useUserStore()
const user = ref<User | null>(null)

onMounted(async () => {
  try {
    user.value = await userStore.getProfile()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
}
</style>
```

#### API调用规范
```typescript
// src/utils/api.ts
import axios from 'axios'

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // 处理未授权
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default apiClient
```

### 数据库规范

#### 表设计规范
```sql
-- 所有业务表必须包含租户字段
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,  -- 租户隔离
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- 索引
    INDEX idx_users_tenant_id (tenant_id),
    INDEX idx_users_email (email),
    
    -- 约束
    CONSTRAINT uk_users_tenant_username UNIQUE(tenant_id, username),
    CONSTRAINT uk_users_tenant_email UNIQUE(tenant_id, email)
);
```

#### 迁移脚本规范
```sql
-- migrations/001_create_users_table.up.sql
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- migrations/001_create_users_table.down.sql
DROP TABLE IF EXISTS users;
```

---

## 🚀 新功能开发

### 开发流程

#### 1. 需求分析
```markdown
# 功能需求文档模板

## 功能描述
简要描述新功能的目的和价值

## 用户故事
作为[用户角色]，我希望[功能描述]，以便[价值/目标]

## 接受标准
- [ ] 功能点1
- [ ] 功能点2
- [ ] 性能要求
- [ ] 安全要求

## 技术方案
- 涉及的微服务
- 数据库变更
- API设计
- 前端界面
```

#### 2. 分支管理
```bash
# 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/user-profile-enhancement

# 开发完成后
git add .
git commit -m "feat(users): add user profile enhancement"
git push origin feature/user-profile-enhancement

# 创建Pull Request
```

#### 3. 代码实现

**后端API开发**:
```go
// 1. 定义数据模型
type UserProfile struct {
    UserID   string `json:"user_id"`
    Avatar   string `json:"avatar"`
    Bio      string `json:"bio"`
    Settings map[string]interface{} `json:"settings"`
}

// 2. 实现服务层
func (s *UserService) UpdateProfile(ctx context.Context, profile *UserProfile) error {
    // 业务逻辑实现
}

// 3. 实现HTTP处理器
func (h *UserHandler) UpdateProfile(c *gin.Context) {
    // HTTP处理逻辑
}

// 4. 注册路由
router.PUT("/api/v1/users/profile", h.UpdateProfile)
```

**前端界面开发**:
```vue
<template>
  <form @submit.prevent="updateProfile">
    <input v-model="profile.avatar" placeholder="头像URL" />
    <textarea v-model="profile.bio" placeholder="个人简介"></textarea>
    <button type="submit">保存</button>
  </form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { updateUserProfile } from '@/api/user'

const profile = ref({
  avatar: '',
  bio: ''
})

const updateProfile = async () => {
  try {
    await updateUserProfile(profile.value)
    // 成功处理
  } catch (error) {
    // 错误处理
  }
}
</script>
```

### 多租户开发注意事项

#### 数据隔离
```go
// 所有数据操作必须包含租户过滤
func (r *UserRepository) GetUsers(tenantID string) ([]User, error) {
    var users []User
    err := r.db.Where("tenant_id = ?", tenantID).Find(&users).Error
    return users, err
}

// 使用GORM作用域
func TenantScope(tenantID string) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("tenant_id = ?", tenantID)
    }
}

// 应用作用域
users := []User{}
db.Scopes(TenantScope(tenantID)).Find(&users)
```

#### 配置管理
```go
// 租户配置读取
func (s *ConfigService) GetTenantConfig(tenantID, key string) (string, error) {
    var config TenantConfig
    err := s.db.Where("tenant_id = ? AND config_key = ?", tenantID, key).
        First(&config).Error
    if err != nil {
        return "", err
    }
    
    // 解密敏感配置
    if config.IsEncrypted {
        return s.decrypt(config.ConfigValue)
    }
    
    return config.ConfigValue, nil
}
```

---

## 🧪 测试指南

### 单元测试

#### Go单元测试
```go
// internal/service/user_test.go
package service

import (
    "context"
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

func TestUserService_GetUser(t *testing.T) {
    // 准备测试数据
    mockRepo := &MockUserRepository{}
    mockRedis := &MockRedisClient{}
    service := NewUserService(mockRepo, mockRedis)
    
    expectedUser := &model.User{
        ID:       "user_123",
        TenantID: "tenant_001",
        Username: "testuser",
    }
    
    // 设置mock期望
    mockRepo.On("GetByID", mock.Anything, "tenant_001", "user_123").
        Return(expectedUser, nil)
    
    // 执行测试
    user, err := service.GetUser(context.Background(), "tenant_001", "user_123")
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, expectedUser, user)
    mockRepo.AssertExpectations(t)
}
```

#### 前端单元测试
```typescript
// src/components/__tests__/UserProfile.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserProfile from '../UserProfile.vue'

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: {
          id: 'user_123',
          nickname: 'Test User',
          email: '<EMAIL>'
        }
      }
    })
    
    expect(wrapper.text()).toContain('Test User')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
})
```

### 集成测试

#### API集成测试
```go
// test/integration/user_test.go
func TestUserAPI(t *testing.T) {
    // 启动测试服务器
    router := setupTestRouter()
    server := httptest.NewServer(router)
    defer server.Close()
    
    // 测试用户注册
    registerData := map[string]string{
        "username": "testuser",
        "email":    "<EMAIL>",
        "password": "password123",
    }
    
    resp, err := http.Post(
        server.URL+"/api/v1/auth/register",
        "application/json",
        strings.NewReader(toJSON(registerData)),
    )
    
    assert.NoError(t, err)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
}
```

### 端到端测试

#### Cypress测试
```typescript
// cypress/e2e/user-profile.cy.ts
describe('User Profile', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'password123')
    cy.visit('/profile')
  })

  it('should update user profile', () => {
    cy.get('[data-cy=nickname-input]').clear().type('New Nickname')
    cy.get('[data-cy=bio-textarea]').clear().type('New bio description')
    cy.get('[data-cy=save-button]').click()
    
    cy.get('[data-cy=success-message]').should('be.visible')
    cy.get('[data-cy=nickname-display]').should('contain', 'New Nickname')
  })
})
```

---

## 📦 构建部署

### 本地开发
```bash
# 启动后端服务
cd ai-users
go run cmd/main.go

# 启动前端服务
cd frontend
npm run dev

# 启动完整环境
docker-compose up -d
```

### 生产构建
```bash
# 构建所有服务
docker-compose build

# 构建特定服务
docker-compose build ai-users

# 推送到镜像仓库
docker tag ai-ecosystem/ai-users:latest registry.example.com/ai-users:v1.0.0
docker push registry.example.com/ai-users:v1.0.0
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "开始部署AI生态平台..."

# 1. 拉取最新代码
git pull origin main

# 2. 构建镜像
docker-compose build --no-cache

# 3. 停止旧服务
docker-compose down

# 4. 启动新服务
docker-compose up -d

# 5. 健康检查
sleep 30
curl -f http://localhost:8080/health || exit 1

echo "部署完成！"
```

---

## 🛠️ 调试技巧

### Go服务调试

#### 日志调试
```go
import (
    "github.com/sirupsen/logrus"
)

// 结构化日志
logrus.WithFields(logrus.Fields{
    "tenant_id": tenantID,
    "user_id":   userID,
    "action":    "get_profile",
}).Info("Getting user profile")

// 错误日志
logrus.WithFields(logrus.Fields{
    "error":     err.Error(),
    "tenant_id": tenantID,
}).Error("Failed to get user profile")
```

#### 性能分析
```go
import _ "net/http/pprof"

// 在main函数中启用pprof
go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()

// 使用方法
// go tool pprof http://localhost:6060/debug/pprof/profile
```

### 前端调试

#### Vue DevTools
```typescript
// 开发环境配置
if (import.meta.env.DEV) {
  app.config.performance = true
}
```

#### 网络请求调试
```typescript
// 请求日志
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data)
    return config
  }
)

apiClient.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.data)
    return response
  }
)
```

### 数据库调试

#### 查询日志
```go
// GORM日志配置
db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
    Logger: logger.Default.LogMode(logger.Info),
})
```

#### 慢查询分析
```sql
-- PostgreSQL慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;
```

---

## 📚 最佳实践

### 代码质量
- 使用静态分析工具（golangci-lint, ESLint）
- 编写单元测试，覆盖率>80%
- 代码审查（Code Review）
- 遵循SOLID原则

### 性能优化
- 数据库索引优化
- Redis缓存策略
- 连接池配置
- 异步处理

### 安全考虑
- 输入验证和过滤
- SQL注入防护
- XSS防护
- CSRF防护
- 敏感数据加密

**🎯 开发成功要素**：
- 深度理解多租户架构
- 严格遵循开发规范
- 完善的测试覆盖
- 持续集成部署
- 及时的代码审查

**⚠️ 注意事项**：
- 所有功能必须支持多租户
- 数据操作必须包含租户过滤
- 敏感配置不能硬编码
- 及时更新文档
