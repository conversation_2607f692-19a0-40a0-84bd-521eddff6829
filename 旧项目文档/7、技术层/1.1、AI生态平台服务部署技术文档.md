# AI生态平台服务部署技术文档

**版本**: v1.0  
**更新时间**: 2025年7月15日  
**适用范围**: AI生态平台完整部署指南  
**文档类型**: 综合技术文档  

---

## 📋 目录

1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [配置参数说明](#配置参数说明)
4. [数据备份](#数据备份)
5. [一键部署](#一键部署)
6. [容器编排与重构](#容器编排与重构)
7. [监控与运维](#监控与运维)
8. [故障排查](#故障排查)
9. [性能优化](#性能优化)
10. [安全配置](#安全配置)

---

## 🎯 项目概述

### 平台定位
AI生态平台是基于SaaS+AI应用架构的智能体生态平台，采用多租户SaaS模式和微服务容器化架构，实现业务场景、智能体、大模型、知识库、传统工具系统的完整整合。

### 核心特性
- **高性能**: 基于Go语言，QPS可达25K+，平均响应时间15-30ms
- **高并发**: 支持10万+并发连接
- **轻量级**: Docker镜像仅25MB，冷启动时间<100ms
- **多租户**: 支持数据隔离、应用隔离、资源隔离、权限隔离

### 功能模块
- **用户服务** (ai-users): 用户管理、会员营销、积分、支付、课程、商城
- **智能体服务** (ai-agents): 智能体管理、对话引擎、知识库、平台对接
- **工具服务** (ai-tools): AI插件工具、云提示词、短视频发布
- **大模型服务** (ai-llms): 大模型API中转、基于new-api项目

---

## 🏗️ 技术架构

### 整体架构图
```
AI生态平台技术架构 (Go版本)
├── 负载均衡层 (nginx:alpine)
│   ├── HTTP/HTTPS入口 (8080/8443)
│   ├── SSL证书管理
│   └── 反向代理路由
├── 应用服务层
│   ├── 前端服务 (frontend:clean) - Vue.js 3.4 + TypeScript
│   ├── 用户服务 (ai-users:latest) - Go 1.21 + Gin + GORM
│   ├── 智能体服务 (ai-agents:latest) - Go 1.21 + Gin + GORM
│   ├── 工具服务 (ai-tools:clean) - Go 1.21 + Gin + GORM
│   └── 大模型服务 (ai-llms:clean) - Go 1.21 + 基于new-api
├── 数据存储层
│   ├── PostgreSQL 15 (主数据库)
│   ├── Redis 7 (缓存数据库)
│   └── MongoDB 7 (文档数据库)
└── 消息通信层
    └── EMQX 5.10 (MQTT消息队列)
```

### 技术栈详解

#### 后端技术栈
- **语言**: Go 1.21
- **框架**: Gin 1.9 (HTTP框架)
- **ORM**: GORM 1.25 (数据库ORM)
- **数据库**: PostgreSQL 15 (主数据库)
- **缓存**: Redis 7 (缓存数据库)
- **文档数据库**: MongoDB 7 (日志存储)
- **消息队列**: EMQX 5.10 (MQTT消息中间件)
- **认证**: JWT (JSON Web Token)
- **API文档**: Swagger
- **监控**: Prometheus + Grafana

#### 前端技术栈
- **框架**: Vue.js 3.4
- **语言**: TypeScript
- **UI组件**: Element Plus
- **图表**: ECharts
- **构建工具**: Vite
- **状态管理**: Pinia

#### 容器化技术栈
- **容器**: Docker + Docker Compose
- **负载均衡**: Nginx Alpine
- **镜像仓库**: Docker Hub
- **数据持久化**: Docker Volumes

## 🏢 SAAS多租户架构

### 租户ID标准化规范

**统一标准格式**：16位字符串 = 年份(4位) + 随机字母(8位) + 月日(4位)

```go
// 租户ID生成函数
func GenerateTenantID() string {
    now := time.Now()

    // 年份(4位)
    year := fmt.Sprintf("%04d", now.Year())

    // 随机8位英文字母
    letters := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    randomLetters := make([]byte, 8)
    for i := range randomLetters {
        randomLetters[i] = letters[rand.Intn(len(letters))]
    }

    // 月日(4位)：MMDD格式
    monthDay := fmt.Sprintf("%02d%02d", now.Month(), now.Day())

    return year + string(randomLetters) + monthDay
}

// 示例生成的租户ID
// 2025CeesAiDr0716 - 赛斯AI直营平台（2025年7月16日创建）
// 2025AgentAbc1225 - 代理商ABC（2025年12月25日创建）
```

**验证规则**：
```go
func isValidTenantID(tenantID string) bool {
    // 必须是16位字符串
    if len(tenantID) != 16 {
        return false
    }

    // 只允许字母和数字
    for _, char := range tenantID {
        if !((char >= 'a' && char <= 'z') ||
            (char >= 'A' && char <= 'Z') ||
            (char >= '0' && char <= '9')) {
            return false
        }
    }

    return true
}
```

**重要说明**：
- ✅ **无特殊租户ID**：所有租户统一使用16位标准格式
- ✅ **完全数据库化**：默认租户ID存储在`system_configs`表中
- ✅ **严禁硬编码**：任何租户ID都不允许硬编码在代码中

### SAAS推广溯源机制

**核心业务价值**：实现代理商推广佣金分成和用户归属管理

#### 推广溯源数据库架构
```sql
-- 1. 系统配置表（存储默认租户ID）
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 租户配置表
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string',
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_required BOOLEAN DEFAULT FALSE,
    default_value TEXT,
    validation_rule TEXT,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50) DEFAULT 'system',
    updated_by VARCHAR(50) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, config_category, config_key)
);

-- 3. 代理商/分销商表
CREATE TABLE distributors (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    distributor_code VARCHAR(20) UNIQUE NOT NULL,
    distributor_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    distributor_level INTEGER DEFAULT 1,
    commission_rate DECIMAL(5,4) DEFAULT 0.1000,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 推广链接表
CREATE TABLE referral_links (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    distributor_id INTEGER REFERENCES distributors(id),
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    link_url TEXT NOT NULL,
    campaign_name VARCHAR(100),
    click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 用户推广溯源表
CREATE TABLE user_referral_sources (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    tenant_id VARCHAR(16) NOT NULL,
    source_type VARCHAR(20) NOT NULL, -- 'domain', 'referral_code', 'direct'
    source_value VARCHAR(100),
    distributor_id INTEGER REFERENCES distributors(id),
    referral_code VARCHAR(20),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 推广溯源业务流程
```
用户注册推广溯源流程：
1. 用户访问推广链接 → https://www.cees.cc?ref=AGENT001REF
2. 租户解析服务 → 识别推广码并查询代理商信息
3. 用户注册 → 自动归属到代理商租户
4. 推广统计 → 记录点击、转化数据
5. 佣金分成 → 基于用户归属计算代理商佣金
```

#### 配置分类说明
| 分类 | 说明 | 示例配置 |
|------|------|----------|
| `app_basic` | 应用基础配置 | app_name, frontend_domain, logo_url |
| `domain_ssl` | 域名和SSL配置 | primary_domain, ssl_enabled |
| `third_party_coze` | 扣子平台配置 | api_url, space_id, app_id |
| `third_party_dify` | Dify平台配置 | api_endpoint, api_key |
| `third_party_n8n` | n8n平台配置 | webhook_url, api_token |
| `cloud_storage` | 阿里云OSS配置 | endpoint, bucket, access_key |
| `notification_sms` | 短信服务配置 | provider, api_key, template_id |
| `notification_email` | 邮箱服务配置 | smtp_host, smtp_port, username |
| `payment_wechat` | 微信支付配置 | app_id, mch_id, api_key |
| `payment_alipay` | 支付宝支付配置 | app_id, private_key, public_key |

### 智能租户解析机制

#### 1. 租户解析服务架构
```go
// TenantResolverService - 租户解析服务
type TenantResolverService struct {
    db *gorm.DB
}

// TenantResolution - 租户解析结果
type TenantResolution struct {
    TenantID      string `json:"tenant_id"`      // 租户ID
    SourceType    string `json:"source_type"`    // 来源类型
    SourceValue   string `json:"source_value"`   // 来源值
    DistributorID *int   `json:"distributor_id"` // 分销商ID
    ReferralCode  string `json:"referral_code"`  // 推广码
}
```

#### 2. 租户识别优先级（支持推广溯源）
```go
// 智能租户解析中间件
func TenantID(tenantResolver *service.TenantResolverService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 优先从请求头获取租户ID（用于API调用）
        if tenantID := c.GetHeader("X-Tenant-ID"); tenantID != "" {
            if isValidTenantID(tenantID) {
                c.Set("tenantID", tenantID)
                return
            }
        }

        // 2. 从查询参数获取租户ID
        if tenantID := c.Query("tenant_id"); tenantID != "" {
            if isValidTenantID(tenantID) {
                c.Set("tenantID", tenantID)
                return
            }
        }

        // 3. 智能租户解析（域名+推广码）
        host := c.GetHeader("Host")
        referralCode := c.Query("ref") // 推广码参数
        userAgent := c.GetHeader("User-Agent")
        clientIP := c.ClientIP()

        resolution, err := tenantResolver.ResolveTenantFromRequest(
            c.Request.Context(), host, referralCode, userAgent, clientIP)
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{
                "error": "租户解析失败",
                "code":  "TENANT_RESOLUTION_FAILED",
            })
            c.Abort()
            return
        }

        // 将租户信息存储到上下文
        c.Set("tenantID", resolution.TenantID)
        c.Set("tenantResolution", resolution)
    }
}
```

#### 3. 租户解析逻辑实现
```go
// ResolveTenantFromRequest - 从请求中解析租户ID
func (s *TenantResolverService) ResolveTenantFromRequest(
    ctx context.Context, host, referralCode, userAgent, clientIP string) (*TenantResolution, error) {

    // 优先级1：推广码识别
    if referralCode != "" {
        if resolution, err := s.resolveTenantByReferralCode(ctx, referralCode); err == nil {
            return resolution, nil
        }
    }

    // 优先级2：域名识别
    if host != "" {
        if resolution, err := s.resolveTenantByDomain(ctx, host); err == nil {
            return resolution, nil
        }
    }

    // 优先级3：返回默认租户
    return s.getDefaultTenant(ctx)
}
```

#### 4. 安全防护机制

**IP访问阻断**：
```nginx
# nginx-lb配置 - 阻断IP直接访问
server {
    listen 8080;
    server_name _;

    # 阻断IP直接访问，防止DOS攻击
    if ($host ~* "^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+") {
        return 444;  # 直接关闭连接
    }

    # 只允许域名访问
    if ($host !~* "^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$") {
        return 444;
    }

    # 正常代理到后端服务
    location / {
        proxy_pass http://frontend:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**推广码安全验证**：
```go
// 推广码验证和统计
func (s *TenantResolverService) resolveTenantByReferralCode(
    ctx context.Context, referralCode string) (*TenantResolution, error) {

    var result struct {
        TenantID      string `json:"tenant_id"`
        DistributorID int    `json:"distributor_id"`
    }

    // 查询有效的推广码
    err := s.db.WithContext(ctx).
        Table("referral_links rl").
        Select("rl.tenant_id, rl.distributor_id").
        Joins("JOIN distributors d ON rl.distributor_id = d.id").
        Where("rl.referral_code = ? AND rl.status = 'active' AND d.status = 'active'", referralCode).
        Where("rl.expires_at IS NULL OR rl.expires_at > ?", time.Now()).
        First(&result).Error

    if err != nil {
        return nil, fmt.Errorf("推广码不存在或已失效: %v", err)
    }

    // 更新点击统计（防止SQL注入）
    s.db.WithContext(ctx).
        Model(&ReferralLink{}).
        Where("referral_code = ?", referralCode).
        UpdateColumn("click_count", gorm.Expr("click_count + 1"))

    return &TenantResolution{
        TenantID:      result.TenantID,
        SourceType:    "referral_code",
        SourceValue:   referralCode,
        DistributorID: &result.DistributorID,
        ReferralCode:  referralCode,
    }, nil
}
```

#### 5. 第三方服务配置调用机制

**扣子(Coze)平台配置调用**：
```go
// 获取扣子平台配置
func getCozeConfig(tenantID string) (*CozeConfig, error) {
    configs, err := getConfigsByCategory(tenantID, "third_party_coze")
    if err != nil {
        return nil, err
    }

    return &CozeConfig{
        ApiURL:    configs["api_url"],
        SpaceID:   configs["space_id"],
        AppID:     configs["app_id"],
        PublicKey: configs["public_key"],
        PrivateKey: configs["private_key"],
    }, nil
}
```

**阿里云OSS配置调用**：
```go
// 获取OSS配置
func getOSSConfig(tenantID string) (*OSSConfig, error) {
    configs, err := getConfigsByCategory(tenantID, "cloud_storage")
    if err != nil {
        return nil, err
    }

    return &OSSConfig{
        Endpoint:        configs["endpoint"],
        Bucket:          configs["bucket"],
        AccessKeyID:     configs["access_key_id"],
        AccessKeySecret: configs["access_key_secret"],
        CustomDomain:    configs["custom_domain"],
    }, nil
}
```

**支付配置调用**：
```go
// 获取微信支付配置
func getWechatPayConfig(tenantID string) (*WechatPayConfig, error) {
    configs, err := getConfigsByCategory(tenantID, "payment_wechat")
    if err != nil {
        return nil, err
    }

    return &WechatPayConfig{
        AppID:     configs["app_id"],
        MchID:     configs["mch_id"],
        ApiKey:    configs["api_key"],
        NotifyURL: configs["notify_url"],
    }, nil
}
```

#### 4. 配置缓存机制
```go
// Redis缓存配置，减少数据库查询
func getCachedConfig(tenantID, category string) (map[string]string, error) {
    cacheKey := fmt.Sprintf("tenant_config:%s:%s", tenantID, category)

    // 先从Redis获取
    if cached, err := redis.HGetAll(cacheKey).Result(); err == nil && len(cached) > 0 {
        return cached, nil
    }

    // Redis未命中，从数据库获取
    configs, err := getConfigsFromDB(tenantID, category)
    if err != nil {
        return nil, err
    }

    // 写入Redis缓存（TTL: 1小时）
    redis.HMSet(cacheKey, configs)
    redis.Expire(cacheKey, time.Hour)

    return configs, nil
}
```

### 容器架构详情

| 容器名称 | 服务类型 | 端口 | 职责 | 镜像大小 |
|----------|----------|------|------|----------|
| nginx-lb | 负载均衡 | 8080/8443 | 统一入口、路由分发 | 7MB |
| frontend | 前端服务 | 8001 | Vue.js应用 | 50MB |
| ai-users | 用户微服务 | 8002 | 用户管理、认证授权 | 17MB |
| ai-agents | 智能体微服务 | 8003 | 智能体管理 | 38MB |
| ai-tools | 工具微服务 | 8005 | AI工具、文件管理 | 17MB |
| ai-llms | LLM微服务 | 8004 | 大模型中转 | 17MB |
| postgres | 主数据库 | 5432 | 业务数据存储 | 13MB |
| redis | 缓存数据库 | 6379 | 缓存、会话存储 | 11MB |
| mongodb | 文档数据库 | 27017 | 日志、文档存储 | 695MB |
| emqx | 消息队列 | 1883/8083/18083 | 异步任务、消息 | 200MB |

### 网络架构
```yaml
networks:
  app-network:    # 应用服务网络
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  data-network:   # 数据服务网络
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

---

## ⚙️ 配置参数说明

### 配置架构设计升级 🎯
AI生态平台采用**混合配置架构**，结合传统环境变量和现代数据库配置，实现SAAS多租户配置管理：

```
传统配置层（基础设施）:
第1层: .env (基础环境变量)
   ↓
第2层: docker-compose.yml (容器环境变量)
   ↓
第3层: production.yaml (容器内配置文件)
   ↓
第4层: config.go (Go程序配置结构)

SAAS配置层（业务配置）:
数据库配置表 (tenant_configs)
   ↓
配置模板表 (config_templates)
   ↓
前端配置管理界面
   ↓
实时配置生效
```

### 🎯 配置分层原则

#### 基础设施配置（.env文件）
**适用范围**: 容器运行、数据库连接、网络配置等基础设施参数
```bash
# 数据库连接配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=ai_ecosystem_db

# 容器运行配置
TZ=Asia/Shanghai
NODE_ENV=production
LOG_LEVEL=info
```

#### 业务配置（数据库存储）
**适用范围**: 租户个性化配置、支付配置、邮件配置、存储配置等业务参数
```sql
-- 支持66项配置参数，9大配置分类
SELECT config_category, COUNT(*) FROM config_templates
GROUP BY config_category;

-- 结果：
app_basic        | 14  -- 应用基础配置
domain_ssl       | 10  -- 域名与SSL配置
email_service    | 6   -- 邮件服务配置
storage_service  | 7   -- 对象存储配置
llm_service      | 2   -- 大模型API配置
sms_service      | 5   -- 短信服务配置
wechat_pay       | 6   -- 微信支付配置
alipay_service   | 6   -- 支付宝配置
system_advanced  | 10  -- 系统高级配置
```

### 核心配置文件

#### 1. 环境变量配置 (.env) - 仅基础设施配置
**🎯 重要变更**: 业务配置参数已迁移至数据库，.env文件仅保留基础设施配置

```bash
# 🌐 前端域名配置说明
# 前端域名配置已迁移至数据库，支持多租户自定义域名
# 请通过管理后台 -> 系统管理 -> 参数配置 -> 域名与SSL配置 进行设置
# 数据库表：tenant_configs (category: domain_ssl, app_basic)

# 🗄️ 数据库配置（基础设施）
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=ai_ecosystem_db
POSTGRES_USER=ai_user
POSTGRES_PASSWORD=your_secure_password

# 🔴 Redis配置（基础设施）
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 📄 MongoDB配置（基础设施）
MONGO_HOST=mongodb
MONGO_PORT=27017
MONGO_USERNAME=ai_user
MONGO_PASSWORD=your_mongo_password

# 🔌 服务端口配置（基础设施）
AI_USERS_PORT=8002
AI_AGENTS_PORT=8003
AI_LLMS_PORT=8004
AI_TOOLS_PORT=8005
FRONTEND_PORT=8001

# 🔐 安全配置（基础设施）
JWT_SECRET_KEY=your_32_character_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# 🌐 系统运行配置（基础设施）
TZ=Asia/Shanghai                                       # 系统时区
NODE_ENV=production                                    # Node.js环境
SERVER_MODE=production                                 # 服务运行模式
LOG_LEVEL=info                                         # 日志级别
ENVIRONMENT=production                                 # 运行环境
DB_SSL_MODE=disable                                    # 数据库SSL模式
JWT_EXPIRATION_HOURS=24                                # JWT过期时间（小时）
DEFAULT_TENANT_ID=default                              # 默认租户ID

# 🔢 Redis数据库分配（基础设施）
REDIS_DB_USERS=0                                       # 用户服务Redis数据库
REDIS_DB_AGENTS=1                                      # 智能体服务Redis数据库
REDIS_DB_TOOLS=2                                       # 工具服务Redis数据库
REDIS_DB_LLMS=3                                        # 大模型服务Redis数据库

# 📡 EMQX消息队列配置（基础设施）
EMQX_ADMIN_USER=admin                                  # EMQX管理员用户名
EMQX_ADMIN_PASSWORD=ai_password_2024                   # EMQX管理员密码
EMQX_USER=ai_user                                      # EMQX普通用户名
EMQX_PASSWORD=ai_password_2024                         # EMQX普通用户密码
EMQX_NAME=emqx                                         # EMQX节点名称
EMQX_HOST=127.0.0.1                                    # EMQX主机地址

# 📝 配置说明
# 邮件服务、对象存储、短信服务、支付服务等配置已迁移至数据库
# 支持多租户配置，请通过管理后台进行配置
# 数据库配置表：tenant_configs, config_templates
```

#### 2. 数据库配置管理 - 业务配置参数
**🎯 新增功能**: 66项业务配置参数完全数据库化管理

**📊 配置分类统计**:
```sql
-- 查看所有配置分类
SELECT category_key, category_name,
       (SELECT COUNT(*) FROM config_templates ct
        WHERE ct.config_category = cc.category_key) as template_count
FROM config_categories cc
ORDER BY display_order;

-- 结果示例：
app_basic        | 应用基础配置      | 14
domain_ssl       | 域名与SSL配置     | 10
email_service    | 邮件服务配置      | 6
storage_service  | 对象存储配置      | 7
llm_service      | 大模型API配置     | 2
sms_service      | 短信服务配置      | 5
wechat_pay       | 微信支付配置      | 6
alipay_service   | 支付宝配置        | 6
system_advanced  | 系统高级配置      | 10
```

**🎛️ 配置管理界面**:
- **访问地址**: `/admin/system/params-config`
- **功能特性**: 可视化配置编辑、实时生效、变更审计
- **安全特性**: 敏感配置加密存储、权限控制
- **多租户支持**: 每个租户独立配置空间

#### 2. 容器编排配置 (docker-compose.yml)
关键配置段：
```yaml
services:
  ai-users:
    build:
      context: ./ai-users
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - REDIS_HOST=${REDIS_HOST}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### 服务配置详解

#### Redis数据库分配
| 服务 | Redis DB | 用途 |
|------|----------|------|
| ai-users | DB 0 | 用户缓存、会话 |
| ai-agents | DB 1 | 智能体缓存 |
| ai-tools | DB 2 | 工具缓存 |
| ai-llms | DB 3 | LLM缓存 |

#### 数据卷挂载配置
| 数据类型 | 数据卷名 | 容器路径 | 用途 |
|----------|----------|----------|------|
| 用户上传文件 | user_uploads | /app/uploads | 用户头像、文档 |
| 智能体知识库 | agent_knowledge | /app/uploads | 知识库文件 |
| 向量数据 | agent_vectors | /app/data | 向量索引 |
| 工具生成内容 | tools_generated | /app/generated | AI生成内容 |
| LLM缓存 | llms_cache | /app/cache | 模型缓存 |
| SSL证书 | ssl_certificates | /etc/nginx/ssl | SSL证书 |
| 应用日志 | app_logs | /app/logs | 应用日志 |

---

## 💾 数据备份

### 备份策略
AI生态平台采用多层次备份策略，确保数据安全：

#### 1. 数据库备份
- **PostgreSQL**: 每日全量备份 + 实时WAL日志
- **Redis**: 每日RDB快照 + AOF持久化
- **MongoDB**: 每日全量备份 + Oplog增量

#### 2. 文件数据备份
- **用户文件**: 每日增量备份
- **系统配置**: 每次变更后备份
- **日志文件**: 按周归档备份

### 自动备份脚本

#### 数据库备份脚本 (scripts/backup_databases.sh)
```bash
#!/bin/bash
# AI生态平台数据库自动备份脚本

BACKUP_DIR="/www/wwwroot/agents/data_backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 1. 备份PostgreSQL
docker exec postgres pg_dump -U ai_user -d ai_ecosystem_db > \
    "$BACKUP_DIR/$DATE/postgresql_backup.sql"

# 2. 备份Redis
docker exec redis redis-cli -a "$REDIS_PASSWORD" --rdb /data/dump.rdb BGSAVE
docker cp redis:/data/dump.rdb "$BACKUP_DIR/$DATE/redis_backup.rdb"

# 3. 备份MongoDB
docker exec mongodb mongodump --host localhost --port 27017 \
    --username ai_user --password "$MONGO_PASSWORD" \
    --out "/backup/$DATE"
```

#### 数据卷备份脚本
```bash
#!/bin/bash
# 备份关键数据卷

VOLUMES=(
    "agents_user_uploads"
    "agents_agent_knowledge" 
    "agents_agent_vectors"
    "agents_tools_generated"
    "agents_llms_cache"
    "agents_ssl_certificates"
)

for volume in "${VOLUMES[@]}"; do
    docker run --rm -v $volume:/source -v $BACKUP_DIR:/target \
        alpine cp -r /source /target/$volume
done
```

### 备份恢复流程

#### 数据库恢复
```bash
# PostgreSQL恢复
docker exec -i postgres psql -U ai_user -d ai_ecosystem_db < \
    backup/postgresql_backup.sql

# Redis恢复
docker cp backup/redis_backup.rdb redis:/data/dump.rdb
docker restart redis

# MongoDB恢复
docker exec mongodb mongorestore --host localhost --port 27017 \
    --username ai_user --password "$MONGO_PASSWORD" /backup/
```

### 备份监控
- **备份状态检查**: 每日检查备份完整性
- **存储空间监控**: 监控备份存储使用情况
- **恢复测试**: 定期进行恢复演练

---

## 🚀 一键部署

### 部署简化目标（升级版）
通过混合配置架构和自动化脚本，实现：
- **基础设施配置**: 客户只需修改.env文件（基础设施参数）
- **业务配置可视化**: 通过管理后台配置业务参数（66项配置）
- **部署自动化**: 一键脚本完成所有部署步骤
- **配置验证**: 智能检查和友好提示
- **快速部署**: 15分钟内完成基础部署 + 5分钟业务配置

### 客户部署流程

#### 第1步: 获取项目代码
```bash
# 下载项目
git clone https://github.com/your-company/ai-ecosystem.git
cd ai-ecosystem

# 或从压缩包解压
tar -xzf ai-ecosystem-v3.0.tar.gz
cd ai-ecosystem
```

#### 第2步: 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件 (唯一需要手动操作)
vim .env
```

**必须修改的基础设施配置项**:
- `POSTGRES_PASSWORD`: 数据库密码
- `REDIS_PASSWORD`: Redis密码
- `MONGO_PASSWORD`: MongoDB密码
- `JWT_SECRET_KEY`: JWT密钥
- `ENCRYPTION_KEY`: 加密密钥
- `EMQX_ADMIN_PASSWORD`: EMQX管理员密码
- `EMQX_PASSWORD`: EMQX用户密码

**⚠️ 重要变更说明**:
- ❌ `FRONTEND_DOMAIN`: 已迁移至数据库配置
- ❌ `SMTP_*`: 已迁移至数据库配置（邮件服务配置）
- ❌ `OSS_*`: 已迁移至数据库配置（对象存储配置）
- ❌ 支付配置: 已迁移至数据库配置（微信支付、支付宝配置）
- ❌ 短信配置: 已迁移至数据库配置（短信服务配置）

**✅ 业务配置新流程**:
1. 完成基础设施部署（修改.env文件）
2. 访问管理后台：`https://your-domain.com/admin/system/params-config`
3. 在"参数配置"页面完成66项业务配置：
   - 域名与SSL配置（自定义域名、SSL证书）
   - 邮件服务配置（SMTP服务器、认证信息）
   - 对象存储配置（阿里云OSS配置）
   - 支付服务配置（微信支付、支付宝配置）
   - 短信服务配置（阿里云短信配置）
   - 大模型API配置（OpenAI等API配置）
   - 系统高级配置（日志、缓存、备份等）

#### 第3步: 一键部署
```bash
# 检查配置 (推荐)
./scripts/check-config.sh

# 一键部署
./scripts/saas-deploy.sh
```

### 自动化脚本功能

#### 配置检查脚本 (check-config.sh)
- ✅ 检查.env文件完整性
- ✅ 验证必要环境变量
- ✅ 检查密码强度
- ✅ 验证域名格式
- ✅ 检查端口冲突

#### 一键部署脚本 (saas-deploy.sh)
- 🔍 系统环境检查
- 📋 配置文件验证
- 🏗️ 自动生成nginx配置
- 🐳 构建和启动容器
- 🔍 服务健康检查
- 📊 部署结果展示

### 部署时序分析
```
T+0s    开始部署
T+5s    网络和数据卷创建完成
T+10s   官方镜像拉取完成
T+30s   本地镜像构建开始
T+180s  本地镜像构建完成
T+185s  数据服务启动
T+195s  数据服务就绪
T+200s  应用服务启动
T+240s  应用服务健康检查开始
T+250s  应用服务就绪
T+255s  nginx负载均衡启动
T+260s  系统完全就绪
```

---

## 🐳 容器编排与重构

### 容器分类和构建方式

#### 官方镜像容器 (5个) - 直接拉取
```yaml
postgres:    image: postgres:15-alpine      # PostgreSQL数据库
redis:       image: redis:7-alpine          # Redis缓存
mongodb:     image: mongo:7                 # MongoDB文档数据库
emqx:        image: emqx/emqx:5.10.0        # EMQX消息队列
nginx-lb:    image: nginx:alpine            # Nginx负载均衡
```

#### 本地构建容器 (5个) - 需要编译
```yaml
frontend:    build: context: ./frontend     # Vue.js前端
ai-users:    build: context: ./ai-users     # Go用户服务
ai-agents:   build: context: ./ai-agents    # Go智能体服务
ai-tools:    build: context: ./ai-tools     # Go工具服务
ai-llms:     build: context: ./ai-llms      # Go大模型服务
```

### 依赖关系分析

#### 第1层: 数据服务 (无依赖)
```
postgres ← 无依赖
redis    ← 无依赖  
mongodb  ← 无依赖
emqx     ← 无依赖
```

#### 第2层: 应用服务 (依赖数据层)
```
ai-users  ← depends_on: [postgres, redis]
ai-agents ← depends_on: [postgres, redis]
ai-tools  ← depends_on: [postgres, redis]
ai-llms   ← depends_on: [postgres, redis, mongodb]
frontend  ← 无依赖 (独立运行)
```

#### 第3层: 网关服务 (依赖应用层)
```
nginx-lb ← depends_on: [frontend, ai-users, ai-agents, ai-tools]
```

### 重构流程详解

#### 阶段1: 环境准备
```bash
# 网络创建
docker network create agents_app-network
docker network create agents_data-network

# 数据卷创建 (22个)
docker volume create agents_postgres_data
docker volume create agents_redis_data
# ... 其他数据卷
```

#### 阶段2: 镜像构建 (并行执行)
```bash
# 官方镜像拉取
docker pull postgres:15-alpine      # ~13MB
docker pull redis:7-alpine          # ~11MB  
docker pull mongo:7                 # ~695MB
docker pull emqx/emqx:5.10.0        # ~200MB
docker pull nginx:alpine            # ~7MB

# 本地镜像构建 (并行)
docker build -t ai-users:latest ./ai-users      # ~2-3分钟
docker build -t ai-agents:latest ./ai-agents    # ~3-4分钟
docker build -t ai-tools:latest ./ai-tools      # ~2-3分钟
docker build -t ai-llms:latest ./ai-llms        # ~2-3分钟
docker build -t frontend:latest ./frontend      # ~2-3分钟
```

#### 阶段3: 容器启动 (按依赖顺序)
```bash
# 第1层: 数据服务启动 (并行)
docker run postgres:15-alpine
docker run redis:7-alpine
docker run mongo:7
docker run emqx/emqx:5.10.0

# 第2层: 应用服务启动
docker run ai-users:latest          # 等待 postgres + redis
docker run ai-agents:latest         # 等待 postgres + redis
docker run ai-tools:latest          # 等待 postgres + redis  
docker run ai-llms:latest           # 等待 postgres + redis + mongodb
docker run frontend:latest          # 独立启动

# 第3层: 网关服务启动
docker run nginx:alpine             # 等待所有应用服务
```

### 健康检查机制
```yaml
healthcheck:
  test: ["CMD", "wget", "--spider", "http://localhost:8002/health"]
  interval: 30s        # 每30秒检查一次
  timeout: 10s         # 10秒超时
  retries: 3           # 失败3次后标记为unhealthy
  start_period: 40s    # 启动后40秒开始检查
```

### 重构最佳实践

#### 全量重构 (生产环境推荐)
```bash
docker compose down
docker compose build --no-cache
docker compose up -d
```

#### 滚动重构 (开发环境推荐)
```bash
docker compose build ai-users
docker compose up -d --no-deps ai-users
```

#### 蓝绿部署 (高可用环境推荐)
```bash
docker compose -f docker-compose.blue.yml up -d
# 验证后切换
docker compose -f docker-compose.yml down
```

---

## 📊 监控与运维

### 系统监控

#### 容器状态监控
```bash
# 查看所有容器状态
docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

# 实时监控容器资源使用
docker stats

# 查看容器日志
docker compose logs -f [service_name]
```

#### 健康检查监控
```bash
# 检查服务健康状态
curl -s http://localhost:8080/api/v1/users/health    # ai-users
curl -s http://localhost:8080/api/v1/agents/health   # ai-agents
curl -s http://localhost:8080/api/v1/tools/health    # ai-tools
curl -s http://localhost:8080/api/v1/llms/health     # ai-llms
```

#### 数据库监控
```bash
# PostgreSQL连接数监控
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c \
    "SELECT count(*) FROM pg_stat_activity;"

# Redis内存使用监控
docker exec redis redis-cli -a "$REDIS_PASSWORD" info memory

# MongoDB状态监控
docker exec mongodb mongo --eval "db.serverStatus()"
```

### 日志管理

#### 日志收集配置
```yaml
# docker-compose.yml 日志配置
logging:
  driver: "json-file"
  options:
    max-size: "100m"
    max-file: "5"
```

#### 日志查看命令
```bash
# 查看特定服务日志
docker compose logs -f --tail=100 ai-agents

# 查看所有服务日志
docker compose logs -f

# 按时间过滤日志
docker compose logs --since="2025-07-15T10:00:00" ai-users
```

#### 日志轮转和清理
```bash
# 清理旧日志
docker system prune -f

# 清理特定容器日志
truncate -s 0 $(docker inspect --format='{{.LogPath}}' ai-agents)
```

### 性能监控

#### 系统资源监控
```bash
# CPU和内存使用
top -p $(docker inspect -f '{{.State.Pid}}' ai-agents)

# 磁盘使用监控
df -h /var/lib/docker/volumes/

# 网络监控
netstat -i
```

#### 应用性能监控
```bash
# API响应时间监控
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:8080/api/v1/agents/

# 数据库查询性能
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c \
    "SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

### 告警配置

#### 基础告警脚本
```bash
#!/bin/bash
# health-monitor.sh - 健康监控告警脚本

services=("ai-users" "ai-agents" "ai-tools" "ai-llms")
for service in "${services[@]}"; do
    if ! docker compose ps $service | grep -q "healthy"; then
        echo "ALERT: $service is unhealthy"
        # 发送告警通知
        curl -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
            -d "chat_id=$CHAT_ID&text=AI生态平台 $service 服务异常"
    fi
done
```

---

## 🔧 故障排查

### 常见问题诊断

#### 1. 容器启动失败
```bash
# 检查容器状态
docker compose ps

# 查看启动日志
docker compose logs [service_name]

# 检查端口占用
netstat -tlnp | grep :8003
lsof -i :8003

# 检查磁盘空间
df -h
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker compose ps postgres

# 测试数据库连接
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "SELECT 1;"

# 检查网络连接
docker exec ai-users ping postgres

# 查看数据库日志
docker compose logs postgres
```

#### 3. 服务健康检查失败
```bash
# 手动测试健康检查端点
docker exec ai-users curl -f http://localhost:8002/health

# 检查服务进程
docker exec ai-users ps aux

# 检查端口监听
docker exec ai-users netstat -tlnp
```

#### 4. 性能问题排查
```bash
# 检查系统资源
docker stats

# 检查数据库性能
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c \
    "SELECT * FROM pg_stat_activity WHERE state = 'active';"

# 检查Redis性能
docker exec redis redis-cli -a "$REDIS_PASSWORD" info stats
```

### 故障恢复流程

#### 服务重启
```bash
# 重启单个服务
docker compose restart ai-agents

# 重启所有服务
docker compose restart

# 强制重建服务
docker compose up -d --force-recreate ai-agents
```

#### 数据恢复
```bash
# 从备份恢复数据库
docker exec -i postgres psql -U ai_user -d ai_ecosystem_db < \
    backup/postgresql_backup.sql

# 恢复数据卷
docker run --rm -v backup:/source -v agents_user_uploads:/target \
    alpine cp -r /source/* /target/
```

#### 回滚部署
```bash
# 快速回滚到上一版本
docker compose down
git checkout HEAD~1
docker compose up -d
```

### 应急处理预案

#### 1. 数据库故障
- 立即切换到备用数据库
- 启动数据恢复流程
- 通知相关人员

#### 2. 服务大面积故障
- 启用维护页面
- 检查基础设施状态
- 执行应急恢复流程

#### 3. 数据丢失
- 立即停止相关服务
- 评估数据丢失范围
- 从最近备份恢复

---

## ⚡ 性能优化

### 应用层优化

#### Go服务优化
```go
// 连接池配置优化
db.SetMaxOpenConns(100)
db.SetMaxIdleConns(10)
db.SetConnMaxLifetime(time.Hour)

// Redis连接池优化
redis.PoolSize = 100
redis.MinIdleConns = 10
redis.MaxConnAge = time.Hour
```

#### 数据库优化
```sql
-- PostgreSQL索引优化
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_agents_status ON agents(status, created_at);

-- 查询优化
EXPLAIN ANALYZE SELECT * FROM agents WHERE status = 'active';
```

#### 缓存策略优化
```bash
# Redis缓存配置优化
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### 容器层优化

#### 镜像优化
```dockerfile
# 多阶段构建减少镜像大小
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -ldflags="-w -s" -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
COPY --from=builder /app/main .
CMD ["./main"]
```

#### 资源限制配置
```yaml
# docker-compose.yml 资源限制
services:
  ai-agents:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### 系统层优化

#### 内核参数优化
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
vm.swappiness = 10
```

#### 文件系统优化
```bash
# 使用高性能文件系统
mount -t ext4 -o noatime,nodiratime /dev/sdb1 /var/lib/docker

# SSD优化
echo deadline > /sys/block/sda/queue/scheduler
```

### 监控和调优

#### 性能基准测试
```bash
# API性能测试
ab -n 10000 -c 100 http://localhost:8080/api/v1/agents/

# 数据库性能测试
pgbench -i -s 10 ai_ecosystem_db
pgbench -c 10 -j 2 -t 1000 ai_ecosystem_db
```

#### 性能监控指标
- **响应时间**: 平均响应时间 < 100ms
- **吞吐量**: QPS > 1000
- **错误率**: 错误率 < 0.1%
- **资源使用**: CPU < 70%, 内存 < 80%

---

## 🔒 安全配置

### 网络安全

#### 防火墙配置
```bash
# 只开放必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5432/tcp   # 禁止外部访问数据库
ufw enable
```

#### SSL/TLS配置
```nginx
# nginx SSL配置
server {
    listen 443 ssl http2;
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
}
```

### 应用安全

#### 认证和授权
```go
// JWT配置
jwt.SigningMethod = jwt.SigningMethodHS256
jwt.ExpirationTime = time.Hour * 24

// RBAC权限控制
func CheckPermission(userRole, resource, action string) bool {
    return rbac.Enforce(userRole, resource, action)
}
```

#### 输入验证
```go
// 参数验证
type UserRequest struct {
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8"`
}

// SQL注入防护
db.Where("email = ?", email).First(&user)
```

### 数据安全

#### 数据加密
```bash
# 数据库连接加密
POSTGRES_URL="******************************/db?sslmode=require"

# 敏感数据加密存储
echo "sensitive_data" | openssl enc -aes-256-cbc -k "$ENCRYPTION_KEY"
```

#### 备份安全
```bash
# 备份文件加密
tar -czf - backup/ | gpg --cipher-algo AES256 --compress-algo 1 \
    --symmetric --output backup_encrypted.tar.gz.gpg
```

### 安全监控

#### 日志审计
```bash
# 访问日志分析
tail -f /var/log/nginx/access.log | grep -E "(40[0-9]|50[0-9])"

# 登录失败监控
grep "authentication failed" /var/log/auth.log
```

#### 入侵检测
```bash
# 安装fail2ban
apt install fail2ban

# 配置规则
cat > /etc/fail2ban/jail.local << EOF
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600
EOF
```

---

## 📚 总结

### 部署优势
- **简化部署**: 客户只需修改.env文件即可完成部署
- **高可用性**: 微服务架构 + 容器化部署 + 健康检查
- **高性能**: Go语言 + Redis缓存 + 数据库优化
- **易维护**: 统一配置管理 + 自动化脚本 + 完善监控

### 技术特点
- **现代化架构**: 微服务 + 容器化 + 云原生
- **高度自动化**: 一键部署 + 自动备份 + 智能监控
- **安全可靠**: 多层安全防护 + 数据加密 + 访问控制
- **可扩展性**: 水平扩展 + 负载均衡 + 服务治理

### 运维建议
1. **定期备份**: 每日自动备份数据库和关键文件
2. **监控告警**: 配置完善的监控和告警系统
3. **安全更新**: 定期更新系统和应用安全补丁
4. **性能调优**: 根据业务增长调整系统配置
5. **文档维护**: 保持部署和运维文档的及时更新

### 支持联系
- **技术支持**: <EMAIL>
- **文档更新**: <EMAIL>
- **紧急联系**: <EMAIL>

---

**文档结束** - AI生态平台服务部署技术文档 v1.0
