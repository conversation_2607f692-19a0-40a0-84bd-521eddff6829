# AI生态平台架构设计

## 📋 目录

1. [🎯 架构概述](#架构概述)
2. [🏗️ 微服务架构](#微服务架构)
3. [🔧 技术栈](#技术栈)
4. [🌐 网络架构](#网络架构)
5. [💾 数据架构](#数据架构)
6. [🔐 安全架构](#安全架构)
7. [📈 扩展性设计](#扩展性设计)

---

## 🎯 架构概述

AI生态平台采用**9容器微服务架构**，实现多租户SaaS平台的完整解决方案。

### 设计原则
- **微服务化**：按业务领域拆分服务，独立部署和扩展
- **容器化**：Docker容器化部署，环境一致性
- **多租户**：完整的租户隔离和数据安全
- **高可用**：服务冗余和故障恢复
- **可扩展**：水平和垂直扩展能力

### 架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    外部访问层                                │
├─────────────────────────────────────────────────────────────┤
│  HTTPS/HTTP (8080/8443) → Nginx负载均衡器                    │
├─────────────────────────────────────────────────────────────┤
│                    应用服务层                                │
├─────────────────────────────────────────────────────────────┤
│  Frontend(8001)  │  AI-Users(8002)  │  AI-Agents(8003)      │
│  AI-LLMs(8004)   │  AI-Tools(8005)  │  AI-Admin(8006)       │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL(5432)         │         Redis(6379)             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🏗️ 微服务架构

### 服务职责划分

#### 前端服务层
```yaml
frontend (8001):
  技术栈: Vue3 + TypeScript + Nginx
  职责:
    - 用户界面渲染
    - 静态资源服务
    - SPA路由管理
  特点:
    - Material Design 3.0设计
    - 响应式布局
    - PWA支持
```

#### 业务服务层
```yaml
ai-users (8002):
  技术栈: Go 1.21 + Gin
  职责:
    - 用户认证授权
    - 会员管理
    - 营销活动
    - 积分系统
    - 支付处理
    - 租户级分销
  数据库: PostgreSQL + Redis DB0

ai-agents (8003):
  技术栈: Go 1.21 + Gin
  职责:
    - 智能体管理
    - 对话引擎
    - 知识库管理
    - 工作流编排
  数据库: PostgreSQL + Redis DB1

ai-llms (8004):
  技术栈: Go 1.21 + Gin
  职责:
    - 大模型API中转
    - New-API融合
    - 配额管理
    - 模型负载均衡
  数据库: PostgreSQL + Redis DB3

ai-tools (8005):
  技术栈: Go 1.21 + Gin
  职责:
    - AI插件工具
    - 代码生成
    - 文档处理
    - 云提示词
  数据库: PostgreSQL + Redis DB2

ai-admin (8006):
  技术栈: Go 1.21 + Gin
  职责:
    - 超级管理员
    - 租户管理
    - 系统配置
    - 平台级代理商管理
  数据库: PostgreSQL + Redis DB4
```

#### 基础设施层
```yaml
nginx-lb (8080):
  技术栈: Nginx Alpine
  职责:
    - 负载均衡
    - 路由分发
    - SSL终端
    - 静态资源缓存

postgres (5432):
  技术栈: PostgreSQL 15
  职责:
    - 主数据存储
    - 事务处理
    - 数据一致性

redis (6379):
  技术栈: Redis 7
  职责:
    - 缓存服务
    - 会话存储
    - 消息队列
```

### 服务间通讯

#### 同步通讯
```go
// HTTP REST API调用
func (s *UserService) GetUserAgents(tenantID, userID string) ([]Agent, error) {
    url := fmt.Sprintf("http://ai-agents:8003/api/v1/agents?user_id=%s", userID)
    req, _ := http.NewRequest("GET", url, nil)
    req.Header.Set("X-Tenant-ID", tenantID)
    req.Header.Set("Authorization", "Bearer "+s.getServiceToken())
    
    // 发送请求...
}
```

#### 异步通讯
```go
// Redis消息队列
func (s *AgentService) PublishEvent(event AgentEvent) error {
    data, _ := json.Marshal(event)
    return s.redis.Publish("agent:events", data).Err()
}
```

---

## 🔧 技术栈

### 后端技术栈
```yaml
编程语言: Go 1.21
Web框架: 标准库 net/http
数据库ORM: GORM v2
缓存: Redis 7
数据库: PostgreSQL 15
容器化: Docker + Docker Compose
```

### 前端技术栈
```yaml
框架: Vue 3 + TypeScript
构建工具: Vite 4
包管理: pnpm
设计系统: Material Design 3.0
```

### 基础设施
```yaml
负载均衡: Nginx
容器编排: Docker Compose
网络: Docker Bridge Network
存储: Docker Volumes
```

---

## 🌐 网络架构

### 网络拓扑
```
Internet
    ↓
Nginx (8080/8443)
    ↓
Docker Bridge Network (app-network)
    ├── frontend:8001
    ├── ai-users:8002
    ├── ai-agents:8003
    ├── ai-llms:8004
    ├── ai-tools:8005
    ├── ai-admin:8006
    ├── postgres:5432
    └── redis:6379
```

### 路由配置
```nginx
# Nginx路由规则
location /api/v1/auth/   { proxy_pass http://ai-users; }
location /api/v1/users/  { proxy_pass http://ai-users; }
location /api/v1/agents/ { proxy_pass http://ai-agents; }
location /api/v1/llms/   { proxy_pass http://ai-llms; }
location /api/v1/tools/  { proxy_pass http://ai-tools; }
location /api/v1/admin/  { proxy_pass http://ai-admin; }
location /            { proxy_pass http://frontend; }
```

### 服务发现
- **DNS解析**：Docker内置DNS，通过容器名访问
- **健康检查**：每个服务提供/health端点
- **负载均衡**：Nginx upstream配置

---

## 💾 数据架构

### 数据库设计

#### PostgreSQL主数据库
```sql
-- 租户表
CREATE TABLE tenants (
    id VARCHAR(16) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 用户表（多租户）
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT fk_users_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    CONSTRAINT uk_users_tenant_username UNIQUE(tenant_id, username)
);

-- 配置表（多租户）
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    
    CONSTRAINT fk_configs_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    UNIQUE(tenant_id, config_category, config_key)
);
```

#### Redis缓存分布
```yaml
DB0 (ai-users):
  - 用户会话: "session:{user_id}"
  - 登录令牌: "token:{token}"
  - 用户缓存: "user:{user_id}"

DB1 (ai-agents):
  - 智能体缓存: "agent:{agent_id}"
  - 对话历史: "chat:{session_id}"
  - 知识库索引: "kb:{kb_id}"

DB2 (ai-tools):
  - 工具缓存: "tool:{tool_id}"
  - 生成结果: "result:{task_id}"

DB3 (ai-llms):
  - 模型配置: "model:{model_id}"
  - API配额: "quota:{user_id}"

DB4 (ai-admin):
  - 系统配置: "config:{key}"
  - 管理会话: "admin_session:{admin_id}"
```

### 数据一致性

#### 事务管理
```go
// 数据库事务
func (s *UserService) CreateUserWithProfile(user User, profile Profile) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        if err := tx.Create(&user).Error; err != nil {
            return err
        }
        profile.UserID = user.ID
        return tx.Create(&profile).Error
    })
}
```

#### 缓存策略
```go
// 缓存更新策略
func (s *UserService) UpdateUser(user User) error {
    // 1. 更新数据库
    if err := s.db.Save(&user).Error; err != nil {
        return err
    }
    
    // 2. 更新缓存
    cacheKey := fmt.Sprintf("user:%s", user.ID)
    s.redis.Set(cacheKey, user, time.Hour)
    
    // 3. 发布更新事件
    s.publishUserUpdateEvent(user)
    
    return nil
}
```

---

## 🔐 安全架构

### 认证授权

#### JWT认证流程
```
1. 用户登录 → ai-users验证 → 生成JWT
2. 客户端请求 → 携带JWT → Nginx转发
3. 微服务验证 → JWT解析 → 获取用户信息
```

#### 多租户隔离
```go
// 租户中间件
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := extractTenantFromDomain(c.Request.Host)
        if tenantID == "" {
            c.JSON(400, gin.H{"error": "Invalid tenant"})
            c.Abort()
            return
        }
        c.Set("tenant_id", tenantID)
        c.Next()
    }
}

// 数据隔离
func TenantScope(tenantID string) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("tenant_id = ?", tenantID)
    }
}
```

### 数据安全

#### 敏感数据加密
```go
// 配置加密存储
func (s *ConfigService) SetEncryptedConfig(key, value string) error {
    encrypted, err := s.encrypt(value)
    if err != nil {
        return err
    }
    
    config := TenantConfig{
        ConfigKey:   key,
        ConfigValue: encrypted,
        IsEncrypted: true,
    }
    
    return s.db.Save(&config).Error
}
```

#### 网络安全
```yaml
防护措施:
  - HTTPS加密传输
  - 内网容器通讯
  - 防火墙规则
  - 访问控制列表
  - 请求频率限制
```

---

## 📈 扩展性设计

### 水平扩展

#### 微服务扩展
```bash
# 扩展用户服务
docker-compose up -d --scale ai-users=3

# 负载均衡配置
upstream ai-users {
    server ai-users_1:8002;
    server ai-users_2:8002;
    server ai-users_3:8002;
}
```

#### 数据库扩展
```yaml
读写分离:
  主库: 写操作
  从库: 读操作
  
分库分表:
  按租户ID分库
  按时间分表
  
缓存集群:
  Redis Cluster
  一致性哈希
```

### 垂直扩展

#### 资源限制
```yaml
# docker-compose.yml
services:
  ai-users:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

#### 性能优化
```go
// 连接池配置
func setupDB() *gorm.DB {
    db, _ := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    sqlDB, _ := db.DB()
    
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)
    
    return db
}
```

### 监控告警

#### 健康检查
```go
// 服务健康检查
func (h *HealthHandler) Check(c *gin.Context) {
    status := HealthStatus{
        Status:    "healthy",
        Service:   "ai-users",
        Timestamp: time.Now().Unix(),
        Checks: map[string]string{
            "database": h.checkDatabase(),
            "redis":    h.checkRedis(),
            "memory":   h.checkMemory(),
        },
    }
    c.JSON(200, status)
}
```

#### 指标收集
```yaml
监控指标:
  - 请求QPS
  - 响应时间
  - 错误率
  - 资源使用率
  - 数据库连接数
  - 缓存命中率
```

---

## 🔄 部署策略

### 滚动更新
```bash
# 无停机更新
docker-compose pull
docker-compose up -d --no-deps ai-users
```

### 蓝绿部署
```yaml
# 蓝绿环境切换
blue_environment:
  - ai-users-blue:8002
  
green_environment:
  - ai-users-green:8002
```

### 灾难恢复
```yaml
备份策略:
  - 数据库定时备份
  - 配置文件版本控制
  - 容器镜像仓库
  - 日志归档存储

恢复流程:
  1. 环境重建
  2. 数据恢复
  3. 服务启动
  4. 健康检查
```

---

**🎯 架构优势**：
- **高可用性**：服务冗余和故障转移
- **可扩展性**：水平和垂直扩展能力
- **安全性**：多层安全防护
- **可维护性**：微服务独立部署
- **多租户**：完整的租户隔离

**⚠️ 注意事项**：
- 服务间调用需要考虑网络延迟
- 分布式事务需要特殊处理
- 监控和日志聚合很重要
- 配置管理需要统一化
