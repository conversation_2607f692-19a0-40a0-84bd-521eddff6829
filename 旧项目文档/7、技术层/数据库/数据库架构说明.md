# AI生态平台数据库架构说明

**版本**: v1.0  
**用途**: AI提示词优化 - 数据库架构理解  
**更新时间**: 2025-07-20  

---

## 📊 数据库概览

### 数据库技术栈
- **主数据库**: PostgreSQL 15.13 (业务数据存储)
- **缓存数据库**: Redis 6.x (会话、缓存、队列)
- **文档数据库**: MongoDB (日志、分析数据)

### 架构特征
- **多租户SaaS架构**: 基于tenant_id字段的完整数据隔离
- **微服务数据分离**: 5个后端服务独立数据库连接
- **配置数据库化**: 第三方平台配置存储在数据库，支持动态配置

---

## 🏗️ 核心表结构分类

### 用户管理表群 (ai-users服务)
```sql
users                    -- 用户基础信息 [tenant_id]
tenant_configs          -- 租户配置管理 [tenant_id] 
tenant_domains          -- 租户域名映射 [tenant_id]
tenant_permissions      -- 租户权限配置 [tenant_id]
tenant_service_configs  -- 租户服务配置 [tenant_id]
transactions            -- 交易记录 [tenant_id]
```

### 智能体管理表群 (ai-agents服务)
```sql
ai_agents               -- 智能体信息 [tenant_id]
ai_agent_categories     -- 智能体分类 [tenant_id]
```

### 大模型服务表群 (ai-llms服务)
```sql
llmsapi_channels        -- API通道配置 [tenant_id]
llmsapi_models          -- 模型配置 [tenant_id]
llmsapi_tokens          -- 令牌管理 [tenant_id]
llmsapi_audit_logs      -- 审计日志 [tenant_id]
llmsapi_error_logs      -- 错误日志 [tenant_id]
llmsapi_performance_logs -- 性能日志 [tenant_id]
llmsapi_request_logs    -- 请求日志 [tenant_id]
llmsapi_quota_transactions -- 配额交易 [tenant_id] ✅已修复
```

### 代理商管理表群 (ai-admin服务)
```sql
distributors            -- 平台级代理商 [tenant_id]
distributor_tenants     -- 代理商-租户关联 [tenant_id]
domain_tenant_mappings  -- 域名-租户映射 [tenant_id]
```

### 系统管理表群 (ai-admin服务)
```sql
system_configs          -- 系统全局配置 [无tenant_id]
system_logs             -- 系统日志 [tenant_id]
admin_sessions          -- 管理员会话 [无tenant_id]
super_admins            -- 超级管理员 [无tenant_id]
tenants                 -- 租户主表 [无tenant_id]
```

### 文件存储表群
```sql
files                   -- 文件存储记录 [tenant_id] ✅已修复
```

---

## 🔐 多租户隔离机制

### 租户字段规范
```sql
-- 所有业务表必须包含租户隔离字段
tenant_id VARCHAR(50) NOT NULL  -- 16位标准租户ID

-- 租户隔离索引（必须）
CREATE INDEX idx_[table_name]_tenant_id ON [table_name](tenant_id);

-- 复合索引优化（推荐）
CREATE INDEX idx_[table_name]_tenant_[field] ON [table_name](tenant_id, [field]);
```

### 数据查询约束
```sql
-- ✅ 正确：包含租户过滤
SELECT * FROM users WHERE tenant_id = 'tenant123' AND status = 'active';

-- ❌ 错误：缺少租户过滤（数据泄露风险）
SELECT * FROM users WHERE status = 'active';
```

### 租户识别机制
```
域名模式：
- demo.cees.cc → tenant_id: 'demo'
- admin.cees.cc → 超级管理员后台
- custom-domain.com → 查询domain_tenant_mappings表
```

---

## ⚙️ 配置管理体系

### tenant_configs表结构
```sql
CREATE TABLE tenant_configs (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,           -- 租户隔离
    config_category VARCHAR(50) NOT NULL,     -- 配置分类
    config_key VARCHAR(100) NOT NULL,         -- 配置键
    config_value TEXT,                        -- 配置值
    config_type VARCHAR(20) DEFAULT 'string', -- 配置类型
    is_encrypted BOOLEAN DEFAULT FALSE,       -- 是否加密
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    UNIQUE(tenant_id, config_category, config_key)
);
```

### 第三方平台配置分类
```yaml
coze:           # Coze平台配置
  - api_url, space_id, app_id
  - public_key_id [加密], private_key [加密]
  
oss:            # 阿里云OSS配置  
  - endpoint, bucket, domain
  - access_key_id [加密], access_key_secret [加密]
  
sms:            # 短信服务配置
  - provider, sign_name
  - access_key_id [加密], access_key_secret [加密]
  
email:          # 邮件服务配置
  - host, port, from
  - username [加密], password [加密]
```

---

## 🔧 微服务数据边界

### 服务端口与数据职责
```yaml
ai-users (8002):
  数据范围: 用户管理、会员体系、营销推广、积分系统、支付订单、课程管理
  主要表: users, transactions, tenant_configs
  Redis DB: 0

ai-agents (8003):
  数据范围: 智能体管理、对话记录、知识库、工作流
  主要表: ai_agents, ai_agent_categories
  Redis DB: 1

ai-llms (8004):
  数据范围: 大模型API中转、配额管理、使用统计
  主要表: llmsapi_* 系列表
  Redis DB: 3

ai-tools (8005):
  数据范围: AI插件工具、代码生成、文档处理、云提示词
  主要表: [待补充]
  Redis DB: 2

ai-admin (8006):
  数据范围: 超级管理员、租户管理、平台级代理商、系统配置
  主要表: distributors, tenants, system_configs
  Redis DB: 4
```

### 跨服务访问规则
```yaml
✅ 允许:
  - 通过API接口调用其他服务
  - 使用JWT令牌进行服务间认证
  - 共享Redis缓存数据（不同DB）

❌ 禁止:
  - 直接跨服务访问数据库
  - 绕过API直接操作其他服务的表
  - 混用Redis数据库编号
```

---

## 💾 Redis数据库分配

### 标准分配方案
```yaml
DB0: ai-users服务 (REDIS_DB_USERS=0)
  - 用户会话、登录状态
  - 会员缓存、积分缓存
  - 营销活动缓存

DB1: ai-agents服务 (REDIS_DB_AGENTS=1)
  - 智能体配置缓存
  - 对话会话状态
  - 知识库索引缓存

DB2: ai-tools服务 (REDIS_DB_TOOLS=2)
  - 工具配置缓存
  - 任务队列状态
  - 临时文件索引

DB3: ai-llms服务 (REDIS_DB_LLMS=3)
  - 模型配置缓存
  - API调用限流
  - 配额使用统计

DB4: ai-admin服务 (REDIS_DB_ADMIN=4)
  - 管理员会话
  - 系统配置缓存
  - 监控数据缓存
```

### 注意事项
- 各服务严格使用独立的Redis数据库，避免数据冲突
- Redis DB分配已完整配置：DB0-DB4分别对应5个微服务

---

## ⚠️ 关键约束与规范

### 数据安全约束
```yaml
必须遵守:
  - 所有业务表包含tenant_id字段
  - 所有查询包含租户过滤条件
  - 敏感配置必须加密存储
  - 跨服务访问必须通过API

严格禁止:
  - 跨租户数据访问
  - 硬编码第三方配置
  - 跨服务直接数据库访问
  - 绕过租户隔离机制
```

### 性能优化要求
```yaml
索引策略:
  - 所有tenant_id字段必须有索引
  - 高频查询字段建立复合索引
  - 定期分析慢查询并优化

连接池配置:
  - ai-users: max_open_conns=200 (高并发)
  - 其他服务: max_open_conns=100 (标准)
  - 合理设置空闲连接数和超时时间
```

### 数据一致性要求
```yaml
事务处理:
  - 跨表操作必须使用数据库事务
  - 关键业务操作支持回滚
  - 定期备份重要数据

配置同步:
  - 配置变更实时生效
  - 支持配置版本控制
  - 提供配置回滚机制
```

---

## 📋 AI提示词使用指南

### 在AI提示词中引用此架构
```
在处理AI生态平台相关问题时，请遵循以下数据库架构约束：

1. 多租户隔离：所有业务表查询必须包含tenant_id过滤
2. 微服务边界：严格按照服务职责访问对应的数据表
3. 配置管理：第三方配置从tenant_configs表读取，不使用硬编码
4. Redis分离：各服务使用独立的Redis数据库编号
5. 安全规范：敏感数据加密存储，跨服务通过API访问

参考文档：AI生态平台数据库架构说明.md
```

### 常见问题处理模式
```yaml
用户相关操作:
  - 服务: ai-users (8002)
  - 表: users, tenant_configs
  - 必须: 包含tenant_id过滤

智能体操作:
  - 服务: ai-agents (8003)  
  - 表: ai_agents, ai_agent_categories
  - 必须: 包含tenant_id过滤

配置读取:
  - 表: tenant_configs
  - 分类: coze, oss, sms, email等
  - 注意: 敏感配置需要解密

跨服务调用:
  - 方式: HTTP API调用
  - 认证: JWT令牌
  - 禁止: 直接数据库访问
```

---

**重要提醒**: 此文档是AI生态平台数据库架构的核心约束说明，在处理任何数据库相关问题时都必须严格遵循这些规范，确保多租户数据安全和系统架构完整性。
