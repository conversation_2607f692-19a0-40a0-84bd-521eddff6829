# AI生态平台故障排查指南

## 📋 目录

1. [🚨 常见问题快速诊断](#常见问题快速诊断)
2. [🔍 系统监控检查](#系统监控检查)
3. [🐛 服务故障排查](#服务故障排查)
4. [💾 数据库问题](#数据库问题)
5. [🌐 网络连接问题](#网络连接问题)
6. [📊 性能问题分析](#性能问题分析)
7. [🛠️ 恢复操作指南](#恢复操作指南)

---

## 🚨 常见问题快速诊断

### 快速检查清单
```bash
# 1. 检查所有容器状态
docker-compose ps

# 2. 检查系统健康状态
curl http://localhost:8080/health

# 3. 检查磁盘空间
df -h

# 4. 检查内存使用
free -m

# 5. 检查Docker资源
docker system df
```

### 问题分类决策树
```
服务无法访问？
├── 容器未启动 → 检查容器状态
├── 端口冲突 → 检查端口占用
├── 网络问题 → 检查网络配置
└── 服务内部错误 → 查看服务日志

性能缓慢？
├── 资源不足 → 检查CPU/内存
├── 数据库慢查询 → 分析SQL性能
├── 网络延迟 → 检查网络连接
└── 缓存失效 → 检查Redis状态

数据异常？
├── 数据库连接失败 → 检查PostgreSQL
├── 缓存数据不一致 → 清理Redis缓存
├── 多租户数据混乱 → 检查租户隔离
└── 配置错误 → 验证环境变量
```

---

## 🔍 系统监控检查

### 容器状态监控
```bash
# 查看所有容器状态
docker-compose ps

# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect <container_name>

# 查看容器启动日志
docker-compose logs <service_name>

# 实时监控日志
docker-compose logs -f --tail=100
```

### 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

echo "=== AI生态平台健康检查 ==="

# 检查容器状态
echo "1. 检查容器状态..."
CONTAINERS=$(docker-compose ps -q)
for container in $CONTAINERS; do
    status=$(docker inspect --format='{{.State.Status}}' $container)
    name=$(docker inspect --format='{{.Name}}' $container)
    echo "  $name: $status"
done

# 检查服务健康
echo "2. 检查服务健康..."
services=("users" "agents" "llms" "tools" "admin")
for service in "${services[@]}"; do
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/v1/$service/ 2>/dev/null)
    if [ "$response" = "200" ]; then
        echo "  ✅ $service: 正常"
    else
        echo "  ❌ $service: 异常 (HTTP $response)"
    fi
done

# 检查数据库连接
echo "3. 检查数据库连接..."
if docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "SELECT 1;" >/dev/null 2>&1; then
    echo "  ✅ PostgreSQL: 正常"
else
    echo "  ❌ PostgreSQL: 连接失败"
fi

if docker exec redis redis-cli -a testpass456 ping >/dev/null 2>&1; then
    echo "  ✅ Redis: 正常"
else
    echo "  ❌ Redis: 连接失败"
fi

echo "=== 健康检查完成 ==="
```

### 性能监控指标
```bash
# CPU使用率
top -p $(docker inspect --format='{{.State.Pid}}' ai-users)

# 内存使用详情
docker exec ai-users cat /proc/meminfo

# 网络连接数
docker exec ai-users netstat -an | grep ESTABLISHED | wc -l

# 磁盘I/O
iostat -x 1 5
```

---

## 🐛 服务故障排查

### 容器启动失败

#### 问题现象
```bash
$ docker-compose ps
NAME        COMMAND     SERVICE     STATUS      PORTS
ai-users    ...         ai-users    Exited(1)   
```

#### 排查步骤
```bash
# 1. 查看启动日志
docker-compose logs ai-users

# 2. 检查配置文件
cat .env | grep -E "(POSTGRES|REDIS)"

# 3. 检查端口冲突
netstat -tulpn | grep :8002

# 4. 检查镜像构建
docker-compose build --no-cache ai-users

# 5. 手动启动容器调试
docker run -it --rm ai-ecosystem/ai-users:latest sh
```

#### 常见错误及解决方案
```bash
# 错误1: 端口被占用
Error: bind: address already in use
解决: 修改docker-compose.yml中的端口映射

# 错误2: 环境变量缺失
Error: required environment variable not set
解决: 检查.env文件配置

# 错误3: 数据库连接失败
Error: failed to connect to database
解决: 确保PostgreSQL容器已启动并可访问

# 错误4: 权限问题
Error: permission denied
解决: 检查文件权限和Docker用户配置
```

### 服务响应异常

#### HTTP 500错误
```bash
# 1. 查看服务日志
docker-compose logs ai-users --tail=50

# 2. 检查数据库连接
docker exec ai-users wget -qO- http://postgres:5432 || echo "数据库连接失败"

# 3. 检查Redis连接
docker exec ai-users ping -c 1 redis

# 4. 检查内存使用
docker stats ai-users --no-stream
```

#### HTTP 404错误
```bash
# 1. 检查Nginx路由配置
docker exec nginx-lb cat /etc/nginx/nginx.conf

# 2. 检查服务注册
docker exec nginx-lb nslookup ai-users

# 3. 测试直接访问服务
curl http://localhost:8002/health
```

#### 服务超时
```bash
# 1. 检查网络延迟
docker exec ai-users ping -c 5 ai-agents

# 2. 检查服务负载
docker exec ai-users ps aux

# 3. 检查数据库查询性能
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC 
LIMIT 5;"
```

---

## 💾 数据库问题

### PostgreSQL故障排查

#### 连接失败
```bash
# 1. 检查容器状态
docker-compose ps postgres

# 2. 检查端口监听
docker exec postgres netstat -tlnp | grep 5432

# 3. 检查用户权限
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "\du"

# 4. 检查数据库存在
docker exec postgres psql -U ai_user -c "\l"

# 5. 测试连接
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "SELECT version();"
```

#### 性能问题
```sql
-- 查看活跃连接
SELECT pid, usename, application_name, client_addr, state, query_start, query
FROM pg_stat_activity
WHERE state = 'active';

-- 查看慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;

-- 查看锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

#### 数据恢复
```bash
# 1. 备份当前数据
docker exec postgres pg_dump -U ai_user ai_ecosystem_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 恢复数据
docker exec -i postgres psql -U ai_user ai_ecosystem_db < backup.sql

# 3. 检查数据完整性
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables
ORDER BY n_tup_ins DESC;"
```

### Redis故障排查

#### 连接问题
```bash
# 1. 检查Redis状态
docker-compose ps redis

# 2. 测试连接
docker exec redis redis-cli -a testpass456 ping

# 3. 检查内存使用
docker exec redis redis-cli -a testpass456 info memory

# 4. 检查连接数
docker exec redis redis-cli -a testpass456 info clients
```

#### 缓存问题
```bash
# 1. 查看缓存命中率
docker exec redis redis-cli -a testpass456 info stats | grep keyspace

# 2. 查看各数据库使用情况
for db in {0..4}; do
    echo "DB$db:"
    docker exec redis redis-cli -a testpass456 -n $db dbsize
done

# 3. 清理特定缓存
docker exec redis redis-cli -a testpass456 -n 0 flushdb

# 4. 查看慢查询
docker exec redis redis-cli -a testpass456 slowlog get 10
```

---

## 🌐 网络连接问题

### 容器间通信故障

#### 网络诊断
```bash
# 1. 检查Docker网络
docker network ls
docker network inspect ai-ecosystem_app-network

# 2. 测试容器间连通性
docker exec ai-users ping -c 3 postgres
docker exec ai-users ping -c 3 redis
docker exec ai-users ping -c 3 ai-agents

# 3. 检查DNS解析
docker exec ai-users nslookup postgres
docker exec ai-users nslookup ai-agents

# 4. 检查端口可达性
docker exec ai-users telnet postgres 5432
docker exec ai-users telnet redis 6379
```

#### 负载均衡问题
```bash
# 1. 检查Nginx配置
docker exec nginx-lb nginx -t

# 2. 重新加载配置
docker exec nginx-lb nginx -s reload

# 3. 查看Nginx日志
docker-compose logs nginx-lb --tail=50

# 4. 测试上游服务
docker exec nginx-lb curl -s http://ai-users:8002/health
```

### 外部访问问题

#### 端口映射检查
```bash
# 1. 检查端口映射
docker port nginx-lb

# 2. 检查防火墙
sudo ufw status
sudo iptables -L

# 3. 检查系统端口占用
netstat -tulpn | grep :8080

# 4. 测试本地访问
curl -v http://localhost:8080/health
```

---

## 📊 性能问题分析

### 系统资源监控

#### CPU和内存分析
```bash
# 1. 容器资源使用
docker stats --no-stream

# 2. 系统整体资源
htop
free -h
df -h

# 3. 进程分析
docker exec ai-users ps aux --sort=-%cpu
docker exec ai-users ps aux --sort=-%mem
```

#### 应用性能分析
```bash
# 1. Go应用性能分析
# 在应用中启用pprof
curl http://localhost:8002/debug/pprof/profile?seconds=30 > cpu.prof
go tool pprof cpu.prof

# 2. 数据库性能分析
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "
SELECT schemaname, tablename, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch
FROM pg_stat_user_tables
ORDER BY seq_scan DESC;"

# 3. Redis性能分析
docker exec redis redis-cli -a testpass456 --latency-history -i 1
```

### 性能优化建议

#### 数据库优化
```sql
-- 添加缺失索引
CREATE INDEX CONCURRENTLY idx_users_tenant_created 
ON users(tenant_id, created_at);

-- 分析表统计信息
ANALYZE users;

-- 查看查询计划
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM users WHERE tenant_id = 'tenant_001';
```

#### 缓存优化
```bash
# 1. 调整Redis配置
docker exec redis redis-cli -a testpass456 config set maxmemory-policy allkeys-lru

# 2. 监控缓存命中率
docker exec redis redis-cli -a testpass456 info stats | grep keyspace_hits

# 3. 预热关键缓存
curl http://localhost:8080/api/v1/users/cache/warmup
```

---

## 🛠️ 恢复操作指南

### 服务重启流程

#### 单个服务重启
```bash
# 1. 优雅重启
docker-compose restart ai-users

# 2. 强制重启
docker-compose stop ai-users
docker-compose start ai-users

# 3. 重新构建重启
docker-compose stop ai-users
docker-compose build ai-users
docker-compose up -d ai-users
```

#### 全系统重启
```bash
# 1. 保存当前状态
docker-compose ps > system_status_$(date +%Y%m%d_%H%M%S).txt

# 2. 优雅停止
docker-compose down

# 3. 清理资源（可选）
docker system prune -f

# 4. 重新启动
docker-compose up -d

# 5. 验证服务
./health-check.sh
```

### 数据恢复流程

#### 数据库恢复
```bash
# 1. 停止相关服务
docker-compose stop ai-users ai-agents ai-llms ai-tools ai-admin

# 2. 备份当前数据
docker exec postgres pg_dump -U ai_user ai_ecosystem_db > current_backup.sql

# 3. 恢复数据
docker exec -i postgres psql -U ai_user ai_ecosystem_db < backup.sql

# 4. 重启服务
docker-compose start ai-users ai-agents ai-llms ai-tools ai-admin

# 5. 验证数据
curl http://localhost:8080/api/v1/users/
```

#### 缓存重建
```bash
# 1. 清空Redis缓存
for db in {0..4}; do
    docker exec redis redis-cli -a testpass456 -n $db flushdb
done

# 2. 重启应用服务
docker-compose restart ai-users ai-agents ai-llms ai-tools ai-admin

# 3. 预热缓存
curl http://localhost:8080/api/v1/users/cache/warmup
```

### 紧急恢复程序

#### 完全系统恢复
```bash
#!/bin/bash
# emergency-recovery.sh

echo "开始紧急恢复程序..."

# 1. 停止所有服务
docker-compose down

# 2. 清理Docker资源
docker system prune -a -f
docker volume prune -f

# 3. 重新拉取镜像
docker-compose pull

# 4. 重新构建
docker-compose build --no-cache

# 5. 启动基础设施
docker-compose up -d postgres redis

# 6. 等待数据库就绪
sleep 30

# 7. 启动应用服务
docker-compose up -d

# 8. 健康检查
sleep 60
curl -f http://localhost:8080/health || {
    echo "恢复失败，请检查日志"
    docker-compose logs
    exit 1
}

echo "紧急恢复完成！"
```

---

## 📞 技术支持

### 日志收集
```bash
# 收集所有日志
mkdir -p logs/$(date +%Y%m%d_%H%M%S)
docker-compose logs > logs/$(date +%Y%m%d_%H%M%S)/all-services.log

# 收集系统信息
docker version > logs/$(date +%Y%m%d_%H%M%S)/docker-info.txt
docker-compose version >> logs/$(date +%Y%m%d_%H%M%S)/docker-info.txt
df -h > logs/$(date +%Y%m%d_%H%M%S)/disk-usage.txt
free -h > logs/$(date +%Y%m%d_%H%M%S)/memory-usage.txt
```

### 问题报告模板
```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- 操作系统: 
- Docker版本: 
- 项目版本: 

## 重现步骤
1. 
2. 
3. 

## 错误日志
```
[粘贴相关错误日志]
```

## 已尝试的解决方案
- [ ] 重启服务
- [ ] 检查配置
- [ ] 查看日志
- [ ] 其他: 
```

**🎯 故障排查原则**：
- 先检查基础设施（网络、存储、资源）
- 再检查应用服务（日志、配置、依赖）
- 最后检查业务逻辑（数据、缓存、权限）

**⚠️ 注意事项**：
- 生产环境操作前务必备份
- 重要操作需要多人确认
- 记录所有操作步骤
- 及时更新监控告警
