# AI生态平台API文档

## 📋 目录

1. [🎯 API概述](#API概述)
2. [🔐 认证授权](#认证授权)
3. [👥 用户服务API](#用户服务API)
4. [🤖 智能体服务API](#智能体服务API)
5. [🧠 大模型服务API](#大模型服务API)
6. [🛠️ 工具服务API](#工具服务API)
7. [⚙️ 管理服务API](#管理服务API)
8. [📊 系统API](#系统API)

---

## 🎯 API概述

### 基础信息
- **Base URL**: `http://localhost:8080`
- **API版本**: `v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": **********
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息",
  "timestamp": **********
}
```

### HTTP状态码
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

---

## 🔐 认证授权

### 登录认证

#### POST /api/v1/auth/login
用户登录获取访问令牌

**请求参数**:
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user": {
      "id": "user_123",
      "username": "<EMAIL>",
      "tenant_id": "tenant_001"
    }
  }
}
```

#### POST /api/v1/auth/refresh
刷新访问令牌

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  }
}
```

#### POST /api/v1/auth/logout
用户登出

**请求头**:
```
Authorization: Bearer <access_token>
```

---

## 👥 用户服务API

### 用户管理

#### GET /api/v1/users/profile
获取当前用户信息

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "id": "user_123",
    "username": "<EMAIL>",
    "nickname": "用户昵称",
    "avatar": "https://example.com/avatar.jpg",
    "tenant_id": "tenant_001",
    "role": "user",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT /api/v1/users/profile
更新用户信息

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

#### GET /api/v1/users/
获取用户列表（管理员）

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `search`: 搜索关键词

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "users": [
      {
        "id": "user_123",
        "username": "<EMAIL>",
        "nickname": "用户昵称",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 会员管理

#### GET /api/v1/users/membership
获取会员信息

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "level": "premium",
    "expires_at": "2024-12-31T23:59:59Z",
    "features": ["unlimited_agents", "priority_support"],
    "usage": {
      "api_calls": 1000,
      "storage_used": "500MB"
    }
  }
}
```

---

## 🤖 智能体服务API

### 智能体管理

#### GET /api/v1/agents/
获取智能体列表

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `category`: 分类筛选
- `status`: 状态筛选

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "agents": [
      {
        "id": "agent_123",
        "name": "客服助手",
        "description": "智能客服机器人",
        "category": "customer_service",
        "status": "active",
        "model": "gpt-3.5-turbo",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

#### POST /api/v1/agents/
创建智能体

**请求参数**:
```json
{
  "name": "新智能体",
  "description": "智能体描述",
  "category": "assistant",
  "model": "gpt-3.5-turbo",
  "system_prompt": "你是一个有用的助手",
  "config": {
    "temperature": 0.7,
    "max_tokens": 2000
  }
}
```

#### GET /api/v1/agents/{agent_id}
获取智能体详情

#### PUT /api/v1/agents/{agent_id}
更新智能体

#### DELETE /api/v1/agents/{agent_id}
删除智能体

### 对话管理

#### POST /api/v1/agents/{agent_id}/chat
与智能体对话

**请求参数**:
```json
{
  "message": "你好，请介绍一下自己",
  "session_id": "session_123",
  "stream": false
}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "message": "你好！我是一个AI助手，可以帮助您解答问题...",
    "session_id": "session_123",
    "message_id": "msg_456",
    "usage": {
      "prompt_tokens": 20,
      "completion_tokens": 50,
      "total_tokens": 70
    }
  }
}
```

#### GET /api/v1/agents/{agent_id}/sessions
获取对话会话列表

#### GET /api/v1/agents/{agent_id}/sessions/{session_id}/messages
获取会话消息历史

---

## 🧠 大模型服务API

### 模型管理

#### GET /api/v1/llms/models
获取可用模型列表

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "models": [
      {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "provider": "openai",
        "type": "chat",
        "max_tokens": 4096,
        "pricing": {
          "input": 0.0015,
          "output": 0.002
        }
      }
    ]
  }
}
```

#### POST /api/v1/llms/chat/completions
聊天补全接口（兼容OpenAI格式）

**请求参数**:
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 150
}
```

### 配额管理

#### GET /api/v1/llms/quota
获取用户配额信息

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "total_quota": 10000,
    "used_quota": 2500,
    "remaining_quota": 7500,
    "reset_date": "2024-02-01T00:00:00Z"
  }
}
```

---

## 🛠️ 工具服务API

### 工具管理

#### GET /api/v1/tools/
获取工具列表

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "tools": [
      {
        "id": "tool_123",
        "name": "代码生成器",
        "description": "根据需求生成代码",
        "category": "development",
        "type": "code_generator",
        "status": "active"
      }
    ]
  }
}
```

#### POST /api/v1/tools/{tool_id}/execute
执行工具

**请求参数**:
```json
{
  "input": {
    "language": "python",
    "description": "创建一个计算器函数"
  },
  "config": {
    "style": "clean",
    "comments": true
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "task_id": "task_789",
    "status": "completed",
    "result": {
      "code": "def calculator(a, b, operation):\n    # 简单计算器函数\n    ...",
      "explanation": "这是一个简单的计算器函数..."
    }
  }
}
```

### 文档处理

#### POST /api/v1/tools/document/parse
解析文档

**请求参数**:
```json
{
  "file_url": "https://example.com/document.pdf",
  "format": "pdf",
  "extract_images": true
}
```

---

## ⚙️ 管理服务API

### 租户管理

#### GET /api/v1/admin/tenants
获取租户列表（超级管理员）

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "tenants": [
      {
        "id": "tenant_001",
        "name": "示例企业",
        "domain": "demo.cees.cc",
        "status": "active",
        "plan": "enterprise",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### POST /api/v1/admin/tenants
创建租户

**请求参数**:
```json
{
  "name": "新企业",
  "domain": "new-company.cees.cc",
  "plan": "professional",
  "admin_email": "<EMAIL>"
}
```

### 系统配置

#### GET /api/v1/admin/configs
获取系统配置

#### PUT /api/v1/admin/configs
更新系统配置

**请求参数**:
```json
{
  "configs": [
    {
      "key": "max_agents_per_user",
      "value": "10",
      "category": "limits"
    }
  ]
}
```

---

## 📊 系统API

### 健康检查

#### GET /health
系统健康检查

**响应示例**:
```json
{
  "status": "healthy",
  "service": "ai-users",
  "version": "v1.0.0-dev",
  "timestamp": **********,
  "port": "8002"
}
```

#### GET /nginx-health
Nginx健康检查

### 监控指标

#### GET /api/v1/metrics
获取系统指标（需要管理员权限）

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "requests_total": 10000,
    "requests_per_second": 50,
    "response_time_avg": 120,
    "error_rate": 0.01,
    "active_users": 500,
    "database_connections": 20
  }
}
```

---

## 🔧 SDK示例

### JavaScript/TypeScript
```typescript
import axios from 'axios';

class AIEcosystemAPI {
  private baseURL = 'http://localhost:8080';
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  async getProfile() {
    const response = await axios.get(`${this.baseURL}/api/v1/users/profile`, {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    return response.data;
  }

  async createAgent(agentData: any) {
    const response = await axios.post(`${this.baseURL}/api/v1/agents/`, agentData, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }
}
```

### Python
```python
import requests

class AIEcosystemAPI:
    def __init__(self, base_url="http://localhost:8080", token=None):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def get_profile(self):
        response = requests.get(
            f"{self.base_url}/api/v1/users/profile",
            headers=self.headers
        )
        return response.json()

    def create_agent(self, agent_data):
        response = requests.post(
            f"{self.base_url}/api/v1/agents/",
            json=agent_data,
            headers=self.headers
        )
        return response.json()
```

---

## 📝 注意事项

### 请求限制
- **频率限制**: 每分钟最多1000次请求
- **文件上传**: 最大50MB
- **请求超时**: 30秒

### 多租户支持
- 所有API自动根据域名识别租户
- 数据完全隔离，无法跨租户访问
- 租户配置影响API行为

### 版本兼容性
- API版本向后兼容
- 废弃功能会提前通知
- 建议使用最新版本

**🎯 快速开始**：
1. 获取访问令牌：`POST /api/v1/auth/login`
2. 设置请求头：`Authorization: Bearer <token>`
3. 调用业务API：根据需要选择相应接口

**⚠️ 重要提醒**：
- 生产环境请使用HTTPS
- 妥善保管访问令牌
- 遵循API调用频率限制
- 及时处理错误响应
