# AI生态平台部署指南

## 📋 目录

1. [🎯 项目概述](#项目概述)
2. [🔧 环境要求](#环境要求)
3. [🚀 快速部署](#快速部署)
4. [🏗️ 架构说明](#架构说明)
5. [🔍 服务验证](#服务验证)
6. [🛠️ 故障排查](#故障排查)
7. [📊 监控运维](#监控运维)

---

## 🎯 项目概述

AI生态平台是一个**多租户SaaS平台**，采用9容器微服务架构，支持代理商独立运营的商业模式。

### 核心特征
- **项目性质**：多租户SaaS平台，"卖坑位"商业模式
- **架构模式**：9容器微服务架构（5后端+1前端+3基础设施）
- **技术栈**：Go 1.21 + Vue3 + PostgreSQL + Redis + Nginx
- **部署方式**：Docker Compose容器编排
- **开发阶段**：核心架构已定型，生产就绪

### 容器架构
```
外部用户 → HTTPS代理 → Nginx(8080) → 微服务层
                                    ├── frontend(8001) - Vue3前端
                                    ├── ai-users(8002) - 用户管理
                                    ├── ai-agents(8003) - 智能体服务
                                    ├── ai-llms(8004) - 大模型服务
                                    ├── ai-tools(8005) - 工具服务
                                    ├── ai-admin(8006) - 管理服务
                                    ├── postgres(5432) - 数据库
                                    ├── redis(6379) - 缓存
                                    └── nginx-lb(8080) - 负载均衡
```

---

## 🔧 环境要求

### 系统要求
- **操作系统**：Windows 10/11, Linux, macOS
- **内存**：最低4GB，推荐8GB+
- **存储**：最低10GB可用空间
- **网络**：稳定的互联网连接

### 软件依赖
- **Docker Desktop** 4.0+ 或 Docker Engine 20.0+
- **Docker Compose** 2.0+
- **Git** 2.30+

### 端口要求
确保以下端口未被占用：
- `8080` - Nginx负载均衡器（主要访问端口）
- `8443` - HTTPS端口（预留）
- `5432` - PostgreSQL数据库（内部）
- `6379` - Redis缓存（内部）

---

## 🚀 快速部署

### 第一步：克隆项目
```bash
git clone <repository-url>
cd ai-ecosystem
```

### 第二步：环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 根据需要修改配置
# 重要：修改数据库密码、JWT密钥等敏感信息
```

### 第三步：启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 第四步：验证部署
```bash
# 检查系统健康状态
curl http://localhost:8080/health

# 访问前端界面
curl http://localhost:8080/
```

---

## 🏗️ 架构说明

### 容器职责分离
```yaml
基础设施层:
  nginx-lb (8080):     负载均衡、路由分发、SSL终端
  postgres (5432):     PostgreSQL数据库
  redis (6379):        Redis缓存

应用服务层:
  frontend (8001):     Vue3用户界面
  ai-users (8002):     用户管理、会员、营销、积分、支付
  ai-agents (8003):    智能体管理、对话、知识库、工作流
  ai-llms (8004):      大模型API中转、配额管理
  ai-tools (8005):     AI插件工具、代码生成、文档处理
  ai-admin (8006):     超级管理员、租户管理、系统配置
```

### 网络架构
- **外部网络**：通过8080端口访问
- **内部网络**：app-network，容器间通讯
- **数据持久化**：Docker volumes

### 数据存储
```yaml
PostgreSQL数据库:
  - 用户数据
  - 智能体配置
  - 系统配置
  - 租户信息

Redis缓存:
  - DB0: 用户会话
  - DB1: 智能体缓存
  - DB2: 工具缓存
  - DB3: 大模型缓存
  - DB4: 管理缓存
```

---

## 🔍 服务验证

### 健康检查
```bash
# 系统总体健康检查
curl http://localhost:8080/health

# 各服务健康检查
curl http://localhost:8080/api/v1/users/
curl http://localhost:8080/api/v1/agents/
curl http://localhost:8080/api/v1/llms/
curl http://localhost:8080/api/v1/tools/
curl http://localhost:8080/api/v1/admin/
```

### 容器状态检查
```bash
# 查看所有容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs ai-users
docker-compose logs nginx-lb

# 查看资源使用情况
docker stats
```

### 数据库连接测试
```bash
# PostgreSQL连接测试
docker exec postgres psql -U ai_user -d ai_ecosystem_db -c "SELECT version();"

# Redis连接测试
docker exec redis redis-cli -a testpass456 ping
```

---

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs <service-name>

# 重新构建容器
docker-compose build --no-cache <service-name>
docker-compose up -d <service-name>
```

#### 2. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 修改docker-compose.yml中的端口映射
ports:
  - "8081:80"  # 改为其他端口
```

#### 3. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose logs postgres

# 重置数据库
docker-compose down
docker volume rm ai-ecosystem_postgres_data
docker-compose up -d
```

#### 4. 内存不足
```bash
# 清理Docker缓存
docker system prune -a

# 增加Docker内存限制
# Docker Desktop -> Settings -> Resources -> Memory
```

### 日志分析
```bash
# 查看所有服务日志
docker-compose logs --tail=100

# 实时监控日志
docker-compose logs -f

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00"
```

---

## 📊 监控运维

### 性能监控
```bash
# 容器资源使用情况
docker stats

# 磁盘使用情况
docker system df

# 网络连接情况
docker network ls
docker network inspect ai-ecosystem_app-network
```

### 备份策略
```bash
# 数据库备份
docker exec postgres pg_dump -U ai_user ai_ecosystem_db > backup.sql

# 配置文件备份
tar -czf config-backup.tar.gz .env docker-compose.yml nginx/
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker-compose build
docker-compose up -d

# 验证更新
curl http://localhost:8080/health
```

### 扩容指南
```bash
# 水平扩容微服务
docker-compose up -d --scale ai-users=2

# 垂直扩容（修改docker-compose.yml）
services:
  ai-users:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

---

## 🔐 安全配置

### 生产环境安全检查清单
- [ ] 修改默认密码（数据库、Redis、JWT密钥）
- [ ] 启用HTTPS（配置SSL证书）
- [ ] 配置防火墙规则
- [ ] 定期更新Docker镜像
- [ ] 配置日志轮转
- [ ] 设置资源限制

### 环境变量安全
```bash
# 生产环境必须修改的配置
POSTGRES_PASSWORD=<strong-password>
REDIS_PASSWORD=<strong-password>
JWT_SECRET_KEY=<64-char-random-string>
```

---

## 📞 技术支持

### 获取帮助
- **文档**：查看docs目录下的详细文档
- **日志**：使用docker-compose logs查看详细错误信息
- **社区**：提交Issue到项目仓库

### 版本信息
- **平台版本**：v1.0.0
- **Docker版本要求**：20.0+
- **Go版本**：1.21
- **Node.js版本**：18+

---

**🎯 部署成功标志**：
- 所有9个容器状态为"healthy"
- 通过http://localhost:8080访问前端界面
- 所有API接口返回正常响应

**⚠️ 注意事项**：
- 首次启动可能需要5-10分钟下载镜像
- 确保Docker有足够的内存和存储空间
- 生产环境部署前请修改所有默认密码
