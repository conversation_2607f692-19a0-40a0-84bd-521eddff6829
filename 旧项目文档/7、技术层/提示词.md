# AI生态平台开发技术规范

## 📋 目录

1. [🎯 项目概述与架构](#项目概述与架构)
2. [🏗️ 微服务架构规范](#微服务架构规范)
3. [🔧 多租户配置管理](#多租户配置管理)
4. [🌐 API设计规范](#API设计规范)
5. [🚫 开发约束与禁忌](#开发约束与禁忌)
6. [📋 开发流程规范](#开发流程规范)
7. [🛠️ 运维与故障排查](#运维与故障排查)
8. [📚 开发指南与最佳实践](#开发指南与最佳实践)

---

## 🎯 项目概述与架构

### 项目定位
AI生态平台是一个**已开发50%的战略级多租户SaaS平台**，采用微服务架构，支持代理商独立运营的商业模式。

### 核心特征
- **项目性质**：多租户SaaS平台，"卖坑位"商业模式
- **架构模式**：5容器微服务架构（4后端+1前端）
- **技术栈**：Go 1.21 + Vue3 + PostgreSQL + Redis + MongoDB
- **部署方式**：Docker Compose容器编排，Nginx负载均衡
- **开发阶段**：核心架构已定型，**严禁大幅重构**

### 技术架构图
```
外部用户 → HTTPS代理 → Nginx(8080) → frontend(8001) → 微服务层
                                                    ├── ai-users(8002)
                                                    ├── ai-agents(8003)
                                                    ├── ai-llms(8004)
                                                    ├── ai-tools(8005)
                                                    └── ai-admin(8006)
```

### 多租户架构特点
- **租户隔离**：基于16位租户ID的完整数据隔离
- **域名路由**：支持代理商独立域名和智能识别
- **推广体系**：完整的用户归属追踪和佣金分成
- **配置管理**：第三方配置存储在数据库，支持动态配置
- **多渠道集成**：Coze、Dify、n8n、阿里云OSS等第三方服务

## 🏗️ 微服务架构规范

### 容器职责分离（不可违反）
```
ai-users (8002)    → 用户管理、会员、营销、积分、支付、课程、商城、租户级分销
ai-agents (8003)   → 智能体管理、对话、知识库、工作流
ai-llms (8004)     → 大模型API中转、New-API融合、配额管理
ai-tools (8005)    → AI插件工具、代码生成、文档处理、云提示词
ai-admin (8006)    → 超级管理员、租户管理、系统配置、平台级代理商管理
frontend (8001)    → Vue3用户界面(普通用户、SAAS租户)、Material Design 3.0
nginx (8080/8443)  → 负载均衡、路由分发、SSL终端、域名识别
```

### 双层代理商架构
**平台层代理商** (ai-admin管理)：
- 区域独家代理商、渠道合作伙伴
- 跨租户业绩统计和佣金结算
- 数据模型：`models.Distributor`, `models.Agent`

**租户层分销商** (ai-users管理)：
- 用户推广链接和邀请码管理
- 租户内部营销活动和奖励
- 数据模型：`models.UserDistribution`, `models.DistributionLevel`

### 容器重建规范
```bash
# 标准重建命令
make rebuild SERVICE=<服务名>

# 常用示例
make rebuild SERVICE=ai-users    # 用户服务
make rebuild SERVICE=ai-agents   # 智能体服务
make rebuild SERVICE=frontend    # 前端服务
```

**重建流程**：clean → update-env → build → deploy → verify

## 🔧 多租户配置管理

### 租户识别机制
```go
// 基于域名的租户识别
func extractTenantFromDomain(host string) string {
    // demo.cees.cc -> demo
    // admin.cees.cc -> admin (超级管理员)
    // custom-domain.com -> 查询数据库映射

    parts := strings.Split(host, ".")
    if len(parts) >= 3 && parts[1] == "cees" && parts[2] == "cc" {
        return parts[0]
    }

    // 自定义域名查询
    return queryTenantByCustomDomain(host)
}

// 租户中间件
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := extractTenantFromDomain(c.Request.Host)
        if tenantID == "" {
            c.JSON(400, gin.H{"error": "Invalid tenant"})
            c.Abort()
            return
        }

        c.Set("tenant_id", tenantID)
        c.Next()
    }
}
```

### 数据隔离实现
**数据库级别隔离**：
```sql
-- 所有业务表必须包含租户字段
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,  -- 16位标准租户ID
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,

    CONSTRAINT uk_users_tenant_username UNIQUE(tenant_id, username),
    INDEX idx_users_tenant_id (tenant_id)
);

-- 租户配置表
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,

    UNIQUE(tenant_id, config_category, config_key)
);
```

**ORM级别隔离**：
```go
// GORM作用域自动添加租户过滤
func TenantScope(tenantID string) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("tenant_id = ?", tenantID)
    }
}

// 用户服务示例
func (s *UserService) GetUsers() ([]User, error) {
    var users []User
    err := s.db.Scopes(TenantScope(s.tenantID)).Find(&users).Error
    return users, err
}
```

### 配置管理原则
1. **禁止硬编码敏感信息** - API密钥、密码等必须存储在数据库
2. **多渠道支持** - 一个租户可配置多个相同类型的第三方服务
3. **加密存储** - 敏感配置必须加密存储（is_encrypted=true）
4. **租户隔离** - 所有配置必须按tenant_id隔离
5. **动态配置** - 支持运行时修改，无需重启服务

### 配置优先级
```
1. 环境变量 (.env文件) - 系统级配置
2. 数据库配置表 (system_configs) - 全局配置
3. 租户配置表 (tenant_configs) - 租户个性化配置
4. 代码默认值 - 兜底配置
```

## 🌐 API设计规范

### API路由规范（不可更改）
```
/api/v1/auth/        → ai-users (认证相关)
/api/v1/users/       → ai-users (用户管理、租户级分销推广)
/api/v1/agents/      → ai-agents (智能体服务)
/api/v1/tools/       → ai-tools (工具服务)
/api/v1/llms/        → ai-llms (大模型服务)
/api/v1/admin/       → ai-admin (超级管理员、平台级代理商管理)
```

### 双层代理商API路由
```yaml
# 平台级代理商管理 (ai-admin)
/api/v1/admin/distributors/          # 平台代理商管理
/api/v1/admin/agents/                # 企业代理商管理

# 租户级分销推广 (ai-users)
/api/v1/users/distributions/         # 用户分销管理
/api/v1/users/referrals/             # 推广链接管理
```

## 🚫 开发约束与禁忌

### 绝对禁止事项（违反将导致系统崩溃）
1. **❌ 修改核心微服务架构** - 禁止改变5容器微服务边界和职责
2. **❌ 破坏端口分配规范** - 禁止修改8001-8006端口分配
3. **❌ 跨服务直接数据库访问** - 必须通过API调用，严禁直连数据库
4. **❌ 硬编码敏感配置** - API密钥、密码等必须存储在tenant_configs表
5. **❌ 忽略多租户数据隔离** - 所有数据操作必须包含tenant_id过滤
6. **❌ 破坏Nginx域名路由** - 禁止修改nginx-simplified.conf核心路由规则
7. **❌ 绕过JWT认证机制** - 所有API必须通过JWT验证
8. **❌ 混用Redis数据库** - 严格按DB0-DB4分配使用
9. **❌ 修改docker-compose.yml** - 影响所有容器，禁止随意修改
10. **❌ 破坏容器重建机制** - 必须使用make rebuild命令

### 高风险操作警告
- **修改.env文件** - 影响所有容器，需要全面测试
- **数据库结构变更** - 必须提供迁移脚本和回滚方案
- **Nginx配置修改** - 可能导致域名路由失效
- **JWT密钥变更** - 会导致所有用户需要重新登录

### SAAS特有约束（多租户架构）
1. **租户数据混合** - 严禁不同租户数据交叉访问
2. **配置硬编码** - 严禁将第三方配置写入代码或配置文件
3. **域名硬编码** - 严禁硬编码特定域名，必须支持动态识别
4. **代理商层级混乱** - 严禁混淆平台级代理商和租户级分销商的职责边界
5. **推广链接破坏** - 严禁破坏用户归属追踪机制

## 📋 开发流程规范

### Git工作流程
```bash
# 标准开发流程
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 开发和提交
git add .
git commit -m "feat(ai-agents): add coze sync functionality"

# 推送和创建PR
git push origin feature/new-feature
```

### 新功能开发流程
**1. 需求分析阶段**：
- 确定功能归属的微服务边界
- 评估是否需要多租户隔离支持
- 检查是否涉及第三方平台集成

**2. 设计阶段**：
- API接口设计（遵循RESTful规范）
- 数据库表设计（包含租户隔离字段）
- 前端组件设计（复用现有组件库）

**3. 开发实现阶段**：
- 后端API实现（包含参数验证和错误处理）
- 前端界面实现（遵循Material Design 3.0）
- 数据库迁移脚本（支持版本回滚）

**4. 测试验证阶段**：
- 单元测试通过（go test ./...）
- 集成测试通过（API接口测试）
- 多租户隔离测试（数据隔离验证）

**5. 部署发布阶段**：
- 容器构建测试（make rebuild SERVICE=服务名）
- 健康检查验证（/health端点正常）
- 配置文件更新（.env和文档同步）

### Bug修复流程（6步法）
**第1步：分析所有可能原因**
- 收集完整的错误日志和堆栈信息
- 分析问题的触发条件和重现步骤
- 检查相关的代码变更历史

**第2步：验证所有可能性**
```bash
# 验证数据库连接
docker exec -it postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "SELECT 1;"

# 验证Redis连接
docker exec -it redis redis-cli ping

# 验证服务间通信
curl -H "Host: admin.cees.cc" http://127.0.0.1:8080/health
```

**第3步：修复最高概率原因**
- 最小化修改范围，避免引入新问题
- 优先修复最可能的根本原因
- 保持代码的向后兼容性

**第4步：验证修复效果**
```bash
# 重建容器验证
make rebuild SERVICE=ai-users

# 功能测试验证
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

**第5步：清理不必要的修改**
```bash
# 回滚不必要的代码修改
git checkout -- unnecessary-file.go
```

**第6步：记录修复过程**
在 `/www/wwwroot/agents/docs/BUG修复记录.md` 中记录详细的修复过程

## 🛠️ 运维与故障排查

### 常见问题诊断流程
```bash
# 1. 检查容器状态
docker-compose ps
docker-compose logs -f --tail=100 ai-users

# 2. 检查服务健康状态
curl -H "Host: admin.cees.cc" http://127.0.0.1:8080/health
curl -H "Host: demo.cees.cc" http://127.0.0.1:8080/api/v1/users/health

# 3. 检查数据库连接
docker exec -it postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "SELECT version();"
docker exec -it redis redis-cli ping

# 4. 检查网络连接
docker network ls
docker network inspect agents_app-network

# 5. 检查资源使用
docker stats
df -h
free -m
```

### Docker构建同步问题解决方案
**问题现象**：代码修改后容器重建，但更改不生效

**解决方案**：
```bash
# 方案1：强制重建（推荐）
make rebuild SERVICE=ai-users

# 方案2：清理缓存重建
docker system prune -f
docker volume prune -f
make rebuild SERVICE=ai-users

# 方案3：完全重置（谨慎使用）
docker-compose down -v
docker system prune -a -f
docker-compose up -d
```

**根本原因分析**：
1. **Docker缓存问题** - 构建缓存导致代码未更新
2. **时区不一致** - 容器时区与宿主机不一致
3. **文件权限问题** - 挂载目录权限不正确
4. **配置不同步** - 环境变量或配置文件不一致

### 性能监控和优化
**关键性能指标监控**：
```go
// 性能监控中间件
func PerformanceMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        c.Next()

        duration := time.Since(start)
        if duration > 1*time.Second {
            logger.WithFields(logrus.Fields{
                "method":   c.Request.Method,
                "path":     c.FullPath(),
                "duration": duration.String(),
                "status":   c.Writer.Status(),
            }).Warn("Slow API request detected")
        }
    }
}
```

**数据库性能优化**：
```sql
-- 查询慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;

-- 检查索引使用情况
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename = 'users'
ORDER BY n_distinct DESC;
```

## 📚 开发指南与最佳实践

### 常用开发命令
```bash
# 容器重建（最常用）
make rebuild SERVICE=ai-users
make rebuild SERVICE=ai-agents
make rebuild SERVICE=frontend

# 健康检查
curl -H "Host: admin.cees.cc" http://127.0.0.1:8080/health
curl -H "Host: demo.cees.cc" http://127.0.0.1:8080/api/v1/users/health

# 日志查看
docker-compose logs -f ai-users
docker-compose logs -f --tail=100 ai-agents

# 数据库操作
docker exec -it postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB}
docker exec -it redis redis-cli
docker exec -it mongodb mongosh

# 容器状态检查
docker-compose ps
docker stats
```

### 关键配置文件位置
```
配置文件路径:
├── .env                           # 环境变量配置
├── docker-compose.yml             # 容器编排配置
├── nginx/nginx-simplified.conf    # Nginx路由配置
├── ai-users/production.yaml       # 用户服务配置
├── ai-agents/production.yaml      # 智能体服务配置
├── ai-tools/production.yaml       # 工具服务配置
├── ai-llms/app.yaml              # 大模型服务配置
└── ai-admin/production.yaml       # 管理服务配置
```

### 开发检查清单
```yaml
新功能开发检查清单:
  需求分析:
    - [ ] 确定归属微服务
    - [ ] 评估多租户隔离需求
    - [ ] 检查第三方集成需求

  设计阶段:
    - [ ] API接口设计
    - [ ] 数据库表设计
    - [ ] 前端组件设计
    - [ ] 权限安全设计

  开发实现:
    - [ ] 后端API实现
    - [ ] 前端界面实现
    - [ ] 数据库迁移脚本
    - [ ] 单元测试编写

  测试验证:
    - [ ] 单元测试通过
    - [ ] 集成测试通过
    - [ ] 多租户隔离测试
    - [ ] 性能基准测试

  部署发布:
    - [ ] 容器构建测试
    - [ ] 健康检查验证
    - [ ] 配置文件更新
    - [ ] 文档同步更新
```

### 开发场景指导

#### 添加新API接口
```go
// 1. 确定归属服务（ai-users/ai-agents/ai-tools/ai-llms/ai-admin）
// 2. 遵循路由规范
router.POST("/api/v1/users/profile", middleware.Auth(), handler.UpdateProfile)

// 3. 必须包含租户隔离
func (h *Handler) UpdateProfile(c *gin.Context) {
    tenantID := middleware.GetTenantID(c)
    userID := middleware.GetUserID(c)
    // 业务逻辑...
}
```

#### 添加新数据表
```sql
-- 租户级表（必须包含租户隔离字段）
CREATE TABLE user_distributions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    tenant_id VARCHAR(50) NOT NULL,         -- 租户隔离（必须）
    level_id INTEGER NOT NULL,
    distribution_code VARCHAR(50) UNIQUE NOT NULL,
    total_sales DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 必须创建租户索引
CREATE INDEX idx_user_distributions_tenant_id ON user_distributions(tenant_id);
```

#### 添加新配置项
```go
// 1. 先在.env.example中定义
NEW_FEATURE_ENABLED=true

// 2. 在数据库中创建配置记录
INSERT INTO system_configs (key, value, description, category)
VALUES ('new_feature_enabled', 'true', '新功能开关', 'features');

// 3. 在代码中读取配置
config := configService.GetConfig("new_feature_enabled", "false")
```

### 前端开发规范

#### Vue3组件开发
```vue
<template>
  <!-- 必须支持多租户主题 -->
  <div :class="tenantTheme">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { useTenantStore } from '@/stores/tenant'

const tenantStore = useTenantStore()
const tenantTheme = computed(() => tenantStore.theme)
</script>
```

#### API调用规范
```typescript
// 必须使用统一的API客户端
import { apiClient } from '@/utils/api'

// 自动处理租户隔离和认证
const response = await apiClient.post('/api/v1/users/profile', data)
```

### 服务间通信规范
```go
// ai-users调用ai-agents服务
func (s *UserService) GetUserAgents(tenantID, userID string) ([]Agent, error) {
    url := fmt.Sprintf("http://ai-agents:8003/api/v1/agents?user_id=%s", userID)
    req, _ := http.NewRequest("GET", url, nil)
    req.Header.Set("X-Tenant-ID", tenantID)
    req.Header.Set("Authorization", "Bearer "+s.getServiceToken())

    // 发送请求...
}
```

### 监控与日志规范
```go
// 使用结构化日志
logger.WithFields(logrus.Fields{
    "tenant_id": tenantID,
    "user_id":   userID,
    "action":    "create_agent",
    "agent_id":  agentID,
}).Info("Agent created successfully")

// 错误日志必须包含上下文
logger.WithFields(logrus.Fields{
    "tenant_id": tenantID,
    "error":     err.Error(),
    "function":  "CreateAgent",
}).Error("Failed to create agent")
```

### 部署与发布规范
**发布前检查清单**：
- [ ] 所有测试通过
- [ ] 健康检查正常
- [ ] 数据库迁移脚本准备
- [ ] 配置文件更新
- [ ] 文档同步更新
- [ ] 回滚方案准备

**灰度发布策略**：
1. 先在测试环境验证
2. 小流量灰度发布
3. 监控关键指标
4. 逐步扩大流量
5. 全量发布

## 📚 核心开发原则与总结

### 开发核心原则
1. **稳定性优先** - 保护现有架构，避免破坏性修改
2. **多租户第一** - 所有功能必须支持租户隔离
3. **配置数据库化** - 严禁硬编码，支持动态配置
4. **容器化标准** - 遵循Docker重建和同步检测机制
5. **文档同步** - 代码修改必须同步更新文档

### 绝对禁止事项
- ❌ 修改核心5容器微服务架构
- ❌ 破坏8001-8006端口分配
- ❌ 跨服务直接数据库访问
- ❌ 硬编码敏感配置信息
- ❌ 忽略多租户数据隔离
- ❌ 破坏Nginx域名路由配置
- ❌ 绕过JWT认证机制

### 项目战略定位
这是一个**已开发50%的战略级SAAS平台项目**，具有完整的商业模式和技术架构：
- **商业价值**：多租户"卖坑位"模式，支持代理商独立运营
- **技术价值**：5容器微服务架构，支持大规模用户访问
- **架构成熟度**：核心架构已定型，禁止大幅重构

### 开发成功要素
- **深度理解SAAS架构** - 掌握多租户隔离原理
- **严格遵循微服务边界** - 不跨服务直接访问数据
- **熟练使用容器重建** - 掌握make rebuild命令
- **重视配置管理** - 理解tenant_configs表设计
- **关注用户体验** - 遵循Material Design 3.0规范

---

**⚠️ 战略级提醒：这是一个已开发50%的复杂SAAS项目，拥有完整的商业模式和技术架构。任何修改都必须慎重考虑对多租户架构的影响。当遇到架构冲突时，优先保持现有架构的稳定性。**

**🎯 开发座右铭：稳定第一，多租户隔离，配置数据库化，容器标准化，文档同步化！**

**🏆 项目愿景：打造业界领先的AI生态平台SAAS系统，支持千万级用户、万级代理商和双层商业生态的完整运营！**