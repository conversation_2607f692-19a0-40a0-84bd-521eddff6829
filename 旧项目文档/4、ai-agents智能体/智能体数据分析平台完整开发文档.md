# 智能体数据分析平台完整开发文档

## 📋 项目概述

**项目名称：** AI生态平台 - 智能体数据分析系统  
**开发时间：** 2025年7月  
**技术栈：** Vue3 + FastAPI + PostgreSQL + Redis + ECharts + WebSocket  
**部署环境：** Docker + Nginx  
**访问地址：** http://**************:8080  

## 🎯 需求分析

### 业务背景
AI生态平台需要一个完整的智能体数据分析系统，用于：
1. **运营决策支持**：提供真实可靠的运营数据
2. **投资人展示**：专业级数据可视化界面
3. **用户体验优化**：展示真实的智能体使用数据
4. **商业价值提升**：支持精准营销和产品策略优化

### 核心需求
1. **数据概览**：核心KPI指标总览
2. **智能体分析**：排行榜、分类分析、性能分析
3. **用户行为分析**：用户画像、路径分析、留存分析
4. **商业分析**：收入分析、转化分析、成本分析
5. **实时监控**：实时浏览用户追踪（头像池+用户列表）
6. **运营报告**：日报/周报/月报生成和导出
7. **数据配置**：分析参数和业务指标配置

### 特殊要求
- **投资人级UI**：精致美观、国际化设计
- **实时性**：关键指标实时更新
- **扩展性**：预留未来功能扩展接口
- **性能优化**：Redis缓存 + 数据库优化

## 🏗️ 技术方案设计

### 方案选择：扩展现有表结构 + 新增统计表

**优点：**
- 保持现有数据完整性
- 支持实时统计和历史数据分析
- 性能优化，避免复杂查询
- 便于数据一致性维护

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端用户界面  │    │   管理后台界面  │    │   数据配置界面  │
│   (Vue3 + UI)   │    │   (Vue3 + UI)   │    │   (Vue3 + UI)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx 反向代理│
                    │   (路由分发)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   FastAPI 后端  │
                    │   (业务逻辑)    │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL DB  │    │   Redis 缓存    │    │   WebSocket     │
│   (数据存储)    │    │   (实时数据)    │    │   (实时通信)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🗄️ 数据库设计

### 1. 扩展现有表结构

#### ai_agents表扩展
```sql
-- 扩展智能体表，添加统计字段
ALTER TABLE ai_agents ADD COLUMN views_count INTEGER DEFAULT 0 COMMENT '浏览量';
ALTER TABLE ai_agents ADD COLUMN usage_count INTEGER DEFAULT 0 COMMENT '调用次数';
ALTER TABLE ai_agents ADD COLUMN user_count INTEGER DEFAULT 0 COMMENT '使用人数';
ALTER TABLE ai_agents ADD COLUMN last_used_at TIMESTAMP COMMENT '最后使用时间';
```

**字段说明：**
- `views_count`：智能体页面浏览次数
- `usage_count`：智能体API调用次数
- `user_count`：使用过该智能体的用户数量
- `last_used_at`：最后一次使用时间

### 2. 新增统计表

#### agent_statistics - 智能体统计表
```sql
CREATE TABLE agent_statistics (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    agent_id INTEGER REFERENCES ai_agents(id),
    user_id INTEGER,
    stat_type VARCHAR(20) NOT NULL, -- 'view', 'usage', 'chat'
    stat_date DATE NOT NULL,
    count INTEGER DEFAULT 1,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_agent_stat_date (agent_id, stat_date),
    INDEX idx_tenant_stat_type (tenant_id, stat_type)
);
```

**用途：** 记录智能体的各种统计数据，支持按日期聚合分析

#### agent_users - 智能体用户关系表
```sql
CREATE TABLE agent_users (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    agent_id INTEGER REFERENCES ai_agents(id),
    user_id INTEGER,
    first_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 1,
    UNIQUE KEY uk_agent_user (agent_id, user_id)
);
```

**用途：** 记录用户与智能体的使用关系，支持用户行为分析

#### agent_realtime_viewers - 实时浏览记录表
```sql
CREATE TABLE agent_realtime_viewers (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    agent_id INTEGER REFERENCES ai_agents(id),
    user_id INTEGER,
    user_name VARCHAR(100),
    user_avatar VARCHAR(500),
    session_id VARCHAR(100) UNIQUE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    INDEX idx_agent_active (agent_id, is_active),
    INDEX idx_session_heartbeat (session_id, last_heartbeat)
);
```

**用途：** 实时追踪智能体浏览用户，支持实时用户列表显示

### 3. 报告相关表

#### reports - 报告主表
```sql
CREATE TABLE reports (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'daily', 'weekly', 'monthly', 'custom'
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'generating', 'completed', 'failed'
    config JSONB,
    file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

#### report_configs - 报告配置表
```sql
CREATE TABLE report_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    config_type VARCHAR(50) NOT NULL, -- 'business', 'user', 'agent', 'system'
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_type_key (tenant_id, config_type, config_key)
);
```

## 🔧 后端API设计

### 1. 核心分析API

#### 数据概览API
```python
@router.get("/admin/overview")
async def get_overview(tenant_id: str, db: Session):
    """
    获取数据概览
    返回：核心指标、热门智能体、用户活跃度
    """
    # 查询智能体总数
    total_agents = db.query(Agent).filter(
        Agent.tenant_id == tenant_id,
        Agent.status.in_(['approved', 'online'])
    ).count()
    
    # 查询总浏览量
    total_views = db.query(Agent).filter(
        Agent.tenant_id == tenant_id
    ).with_entities(func.sum(Agent.views_count)).scalar() or 0
    
    # 返回结构化数据
    return {
        "success": True,
        "data": {
            "metrics": {
                "total_agents": {"value": total_agents, "change": 18.7},
                "total_users": {"value": 15680, "change": 22.3},
                "total_revenue": {"value": 2856000, "change": 28.5},
                "total_views": {"value": total_views, "change": 15.2}
            },
            "hotAgents": [...],
            "userActivity": {...}
        }
    }
```

#### 智能体分析API
```python
@router.get("/admin/agents")
async def get_agent_analysis(tenant_id: str, db: Session):
    """
    获取智能体分析数据
    返回：排行榜、分类统计、性能指标
    """
    # 智能体排行榜查询
    agents = db.query(Agent).filter(
        Agent.tenant_id == tenant_id,
        Agent.status.in_(['approved', 'online'])
    ).order_by(
        Agent.views_count.desc(),
        Agent.usage_count.desc()
    ).limit(20).all()
    
    # 构建排行榜数据
    ranking = []
    for i, agent in enumerate(agents, 1):
        ranking.append({
            "rank": i,
            "id": agent.id,
            "name": agent.name,
            "avatar": agent.avatar_url,
            "views": agent.views_count or 0,
            "usage": agent.usage_count or 0,
            "users": agent.user_count or 0
        })
    
    return {"success": True, "data": {"ranking": ranking}}
```

### 2. 实时监控API

#### 实时浏览用户API
```python
@router.get("/realtime/viewers/{agent_id}")
async def get_realtime_viewers(agent_id: int, tenant_id: str, db: Session):
    """
    获取智能体实时浏览用户
    返回：头像池数据、用户列表
    """
    # 查询活跃浏览用户（5分钟内有心跳）
    cutoff_time = datetime.now() - timedelta(minutes=5)
    
    viewers = db.query(AgentRealtimeViewer).filter(
        AgentRealtimeViewer.agent_id == agent_id,
        AgentRealtimeViewer.tenant_id == tenant_id,
        AgentRealtimeViewer.is_active == True,
        AgentRealtimeViewer.last_heartbeat >= cutoff_time
    ).all()
    
    # 构建头像池数据
    avatar_pool = [viewer.user_avatar for viewer in viewers[:10]]
    
    # 构建用户列表
    user_list = [{
        "id": viewer.user_id,
        "name": viewer.user_name,
        "avatar": viewer.user_avatar,
        "duration": calculate_duration(viewer.started_at)
    } for viewer in viewers]
    
    return {
        "success": True,
        "data": {
            "total": len(viewers),
            "avatarPool": avatar_pool,
            "userList": user_list
        }
    }
```

#### 心跳API
```python
@router.post("/realtime/heartbeat")
async def heartbeat(request: Request, db: Session):
    """
    用户心跳接口
    维护用户在线状态
    """
    session_id = request.headers.get("X-Session-ID")
    
    if session_id:
        # 更新心跳时间
        db.query(AgentRealtimeViewer).filter(
            AgentRealtimeViewer.session_id == session_id
        ).update({
            "last_heartbeat": datetime.now(),
            "is_active": True
        })
        db.commit()
    
    return {"success": True, "message": "心跳成功"}
```

### 3. 数据埋点API

#### 数据埋点接口
```python
@router.post("/track")
async def track_event(event_data: dict, tenant_id: str, db: Session):
    """
    数据埋点接口
    记录用户行为事件
    """
    event_type = event_data.get("type")  # 'view', 'usage', 'chat'
    agent_id = event_data.get("agent_id")
    user_id = event_data.get("user_id")
    
    # 记录统计数据
    stat = AgentStatistic(
        tenant_id=tenant_id,
        agent_id=agent_id,
        user_id=user_id,
        stat_type=event_type,
        stat_date=date.today(),
        metadata=event_data.get("metadata", {})
    )
    db.add(stat)
    
    # 更新智能体统计字段
    if event_type == "view":
        db.query(Agent).filter(Agent.id == agent_id).update({
            "views_count": Agent.views_count + 1
        })
    elif event_type == "usage":
        db.query(Agent).filter(Agent.id == agent_id).update({
            "usage_count": Agent.usage_count + 1,
            "last_used_at": datetime.now()
        })
    
    db.commit()
    return {"success": True, "message": "数据埋点成功"}
```

## 🎨 前端架构设计

### 1. 路由结构

```javascript
// 智能体数据分析路由配置
{
  path: 'analytics',
  redirect: '/admin/agent/analytics/overview',
  meta: { title: '智能体数据', icon: 'TrendCharts' },
  children: [
    {
      path: 'overview',
      component: () => import('@/views/admin/agent/analytics/Overview.vue'),
      meta: { title: '数据概览', icon: 'DataAnalysis' }
    },
    {
      path: 'agents',
      component: () => import('@/views/admin/agent/analytics/AgentAnalysis.vue'),
      meta: { title: '智能体分析', icon: 'Tools' }
    },
    {
      path: 'users',
      component: () => import('@/views/admin/agent/analytics/UserAnalysis.vue'),
      meta: { title: '用户行为分析', icon: 'User' }
    },
    {
      path: 'business',
      component: () => import('@/views/admin/agent/analytics/BusinessAnalysis.vue'),
      meta: { title: '商业分析', icon: 'Money' }
    },
    {
      path: 'realtime',
      component: () => import('@/views/admin/agent/analytics/RealtimeUsers.vue'),
      meta: { title: '实时浏览用户', icon: 'View' }
    },
    {
      path: 'reports',
      component: () => import('@/views/admin/agent/analytics/OperationalReports.vue'),
      meta: { title: '运营报告', icon: 'Document' }
    }
  ]
}
```

### 2. API客户端封装

```javascript
// analytics.js - 分析API客户端
import { request } from '@/utils/request'

export const analyticsApi = {
  // 数据概览
  getOverview: () => request('/analytics/admin/overview'),
  
  // 智能体分析
  getAgentAnalysis: () => request('/analytics/admin/agents'),
  
  // 用户行为分析
  getUserAnalysis: () => request('/analytics/admin/users'),
  
  // 商业分析
  getBusinessAnalysis: () => request('/analytics/admin/business'),
  
  // 实时浏览用户
  getRealtimeViewers: (agentId) => request(`/analytics/realtime/viewers/${agentId}`),
  
  // 实时统计
  getRealtimeStats: () => request('/analytics/admin/realtime'),
  
  // 数据埋点
  track: (data) => request('/analytics/track', 'post', data),
  
  // 运营报告
  getReports: (params) => request('/reports/list', 'get', { params }),
  generateReport: (data) => request('/reports/generate', 'post', data)
}
```

### 3. 核心组件设计

#### 数据概览组件 (Overview.vue)
```vue
<template>
  <div class="analytics-overview">
    <!-- 核心指标卡片 -->
    <div class="metrics-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="metric in coreMetrics" :key="metric.key">
          <el-card class="metric-card" :class="metric.type">
            <div class="metric-content">
              <div class="metric-icon" :class="metric.type">
                <el-icon><component :is="metric.icon" /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-value">{{ formatMetricValue(metric.value, metric.format) }}</div>
                <div class="metric-change" :class="{ 'positive': metric.change >= 0 }">
                  <el-icon><ArrowUp /></el-icon>
                  <span>{{ Math.abs(metric.change) }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 趋势图表 -->
    <div class="trend-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header><h3>平台总览趋势</h3></template>
            <div class="chart-container" ref="overviewChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header><h3>智能体活跃度分布</h3></template>
            <div class="chart-container" ref="activityChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 快速导航 -->
    <div class="quick-navigation">
      <el-card>
        <template #header><h3>快速导航</h3></template>
        <el-row :gutter="20">
          <el-col :span="4" v-for="nav in quickNavs" :key="nav.key">
            <div class="nav-item" @click="$router.push(nav.path)">
              <div class="nav-icon" :class="nav.type">
                <el-icon><component :is="nav.icon" /></el-icon>
              </div>
              <div class="nav-info">
                <div class="nav-title">{{ nav.title }}</div>
                <div class="nav-description">{{ nav.description }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { analyticsApi } from '@/api/analytics'
import * as echarts from 'echarts'

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'total_agents',
    label: '智能体总数',
    value: 0,
    change: 0,
    format: 'number',
    icon: 'Tools',
    type: 'primary'
  },
  // ... 其他指标
])

// 加载概览数据
const loadOverviewData = async () => {
  try {
    const response = await analyticsApi.getOverview()
    if (response.success) {
      // 更新指标数据
      coreMetrics.value.forEach(metric => {
        if (response.data.metrics[metric.key]) {
          metric.value = response.data.metrics[metric.key].value
          metric.change = response.data.metrics[metric.key].change
        }
      })
      
      // 初始化图表
      nextTick(() => {
        initCharts()
      })
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

onMounted(() => {
  loadOverviewData()
})
</script>
```

#### 实时浏览用户组件 (RealtimeUsers.vue)
```vue
<template>
  <div class="realtime-users">
    <!-- 智能体选择器 -->
    <div class="agent-selector">
      <el-select v-model="selectedAgentId" placeholder="选择智能体" @change="loadRealtimeData">
        <el-option
          v-for="agent in agentList"
          :key="agent.id"
          :label="agent.name"
          :value="agent.id"
        />
      </el-select>
    </div>

    <!-- 实时数据展示 -->
    <div class="realtime-display" v-if="selectedAgentId">
      <el-row :gutter="20">
        <!-- 头像池 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>当前浏览用户 ({{ realtimeData.total }}人)</h3>
            </template>
            <div class="avatar-pool">
              <el-avatar
                v-for="(avatar, index) in realtimeData.avatarPool"
                :key="index"
                :src="avatar"
                :size="40"
                class="avatar-item"
              >
                {{ index + 1 }}
              </el-avatar>
              <div v-if="realtimeData.total > 10" class="more-users">
                +{{ realtimeData.total - 10 }}
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 用户列表 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>用户详细列表</h3>
            </template>
            <div class="user-list">
              <div
                v-for="user in realtimeData.userList"
                :key="user.id"
                class="user-item"
              >
                <el-avatar :src="user.avatar" :size="32">{{ user.name[0] }}</el-avatar>
                <div class="user-info">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-duration">浏览时长: {{ user.duration }}</div>
                </div>
                <div class="user-status">
                  <el-tag type="success" size="small">在线</el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { analyticsApi } from '@/api/analytics'

const selectedAgentId = ref('')
const agentList = ref([])
const realtimeData = ref({
  total: 0,
  avatarPool: [],
  userList: []
})

let refreshTimer = null
let heartbeatTimer = null
const sessionId = ref('')

// 生成会话ID
const generateSessionId = () => {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 发送心跳
const sendHeartbeat = async () => {
  if (!sessionId.value) return

  try {
    await fetch('/api/v1/analytics/realtime/heartbeat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId.value
      }
    })
  } catch (error) {
    console.error('心跳发送失败:', error)
  }
}

// 启动心跳
const startHeartbeat = () => {
  sessionId.value = generateSessionId()
  sendHeartbeat()
  heartbeatTimer = setInterval(sendHeartbeat, 30000) // 30秒心跳
}

// 加载实时数据
const loadRealtimeData = async () => {
  if (!selectedAgentId.value) return

  try {
    const response = await analyticsApi.getRealtimeViewers(selectedAgentId.value)
    if (response.success) {
      realtimeData.value = response.data
    }
  } catch (error) {
    console.error('加载实时数据失败:', error)
  }
}

// 启动自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(loadRealtimeData, 5000) // 5秒刷新
}

onMounted(() => {
  startHeartbeat()
  startAutoRefresh()
})

onUnmounted(() => {
  if (refreshTimer) clearInterval(refreshTimer)
  if (heartbeatTimer) clearInterval(heartbeatTimer)
})
</script>
```

## 🎨 UI设计规范

### 投资人级UI设计原则

#### 1. 现代化设计
- **设计系统**：Material Design 3.0规范
- **色彩体系**：专业商务配色方案
- **字体系统**：系统字体 + 数字字体优化
- **间距系统**：8px基础网格系统

#### 2. 数据可视化
- **图表库**：ECharts Professional
- **图表主题**：自定义投资人级主题
- **动画效果**：流畅的数据变化动画
- **交互设计**：hover、click、zoom等交互

#### 3. 响应式布局
- **断点设计**：支持4K显示器展示
- **网格系统**：24栅格系统
- **组件适配**：移动端友好设计

### 核心样式定义

```scss
// variables.scss - 设计变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 投资人级配色方案
$gradient-primary: linear-gradient(135deg, #409EFF 0%, #1890ff 100%);
$gradient-success: linear-gradient(135deg, #67C23A 0%, #52c41a 100%);
$gradient-warning: linear-gradient(135deg, #E6A23C 0%, #fa8c16 100%);
$gradient-danger: linear-gradient(135deg, #F56C6C 0%, #ff4d4f 100%);

// 阴影系统
$shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
$shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);

// 动画系统
$transition-fast: all 0.2s ease;
$transition-normal: all 0.3s ease;
$transition-slow: all 0.5s ease;
```

## 📊 数据流转逻辑

### 1. 数据收集流程

```
用户行为 → 前端埋点 → API接口 → 数据库存储 → 统计聚合 → 缓存更新
    ↓           ↓         ↓         ↓           ↓         ↓
  页面访问    track()   POST /track  agent_statistics  定时任务   Redis缓存
  智能体使用   事件数据   数据埋点     agent_users      数据聚合   实时更新
  用户注册    实时上报   异步处理     ai_agents        统计计算   性能优化
```

### 2. 实时数据流程

```
用户进入页面 → 生成会话ID → 记录浏览状态 → 定时心跳 → 更新在线状态
     ↓             ↓           ↓           ↓         ↓
  页面加载      generateSessionId()  数据库插入    30秒间隔   实时用户列表
  WebSocket连接   唯一标识生成    realtime_viewers  心跳API   头像池更新
  状态初始化     session管理      用户状态追踪    保活机制   动态显示
```

### 3. 报告生成流程

```
配置报告参数 → 数据查询聚合 → 报告模板渲染 → 文件生成 → 下载/预览
     ↓             ↓           ↓           ↓         ↓
  用户配置      SQL查询      模板引擎     PDF/Excel   文件服务
  时间范围      数据聚合      数据填充     格式转换    下载链接
  指标选择      统计计算      图表生成     文件存储    预览功能
```

## 🔧 关键技术实现

### 1. 数据一致性保障

#### 数据库事务处理
```python
@transaction.atomic
def update_agent_statistics(agent_id: int, stat_type: str, user_id: int):
    """
    原子性更新智能体统计数据
    确保数据一致性
    """
    # 更新智能体主表统计字段
    agent = Agent.objects.select_for_update().get(id=agent_id)

    if stat_type == "view":
        agent.views_count = F('views_count') + 1
    elif stat_type == "usage":
        agent.usage_count = F('usage_count') + 1
        agent.last_used_at = timezone.now()

    agent.save()

    # 记录详细统计数据
    AgentStatistic.objects.create(
        agent_id=agent_id,
        user_id=user_id,
        stat_type=stat_type,
        stat_date=date.today()
    )

    # 更新用户关系表
    agent_user, created = AgentUser.objects.get_or_create(
        agent_id=agent_id,
        user_id=user_id,
        defaults={'usage_count': 1}
    )

    if not created:
        agent_user.usage_count = F('usage_count') + 1
        agent_user.last_used_at = timezone.now()
        agent_user.save()
```

### 2. 性能优化策略

#### Redis缓存机制
```python
import redis
from django.core.cache import cache

class AnalyticsCache:
    """分析数据缓存管理"""

    @staticmethod
    def get_agent_ranking(tenant_id: str):
        """获取智能体排行榜缓存"""
        cache_key = f"agent_ranking:{tenant_id}"
        ranking = cache.get(cache_key)

        if ranking is None:
            # 查询数据库
            ranking = Agent.objects.filter(
                tenant_id=tenant_id,
                status__in=['approved', 'online']
            ).order_by('-views_count', '-usage_count')[:20]

            # 缓存30分钟
            cache.set(cache_key, ranking, 1800)

        return ranking

    @staticmethod
    def update_realtime_viewers(agent_id: int, user_data: dict):
        """更新实时浏览用户缓存"""
        cache_key = f"realtime_viewers:{agent_id}"

        # 使用Redis Set存储在线用户
        redis_client = redis.Redis()
        redis_client.hset(
            cache_key,
            user_data['session_id'],
            json.dumps(user_data)
        )

        # 设置过期时间5分钟
        redis_client.expire(cache_key, 300)
```

### 3. 实时通信机制

#### WebSocket实现
```python
from fastapi import WebSocket
import json

class RealtimeManager:
    """实时数据管理器"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket

    def disconnect(self, session_id: str):
        """断开连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]

    async def broadcast_viewer_update(self, agent_id: int, viewer_data: dict):
        """广播浏览用户更新"""
        message = {
            "type": "viewer_update",
            "agent_id": agent_id,
            "data": viewer_data
        }

        # 向所有连接的客户端发送更新
        for session_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except:
                # 连接已断开，清理
                self.disconnect(session_id)

# 全局实时管理器实例
realtime_manager = RealtimeManager()
```

## 📈 商业分析数据路径

### 1. ROI投资回报分析

#### 数据配置需求
```json
{
  "investment_costs": {
    "tech_development": 500000,    // 技术研发投入
    "human_resources": 800000,     // 人力成本
    "marketing": 300000,           // 营销费用
    "operations": 200000           // 运营费用
  },
  "revenue_sources": {
    "subscription": 1200000,       // 订阅收入
    "one_time_payment": 400000,    // 一次性付费
    "value_added_services": 300000 // 增值服务收入
  },
  "calculation_period": "monthly", // 计算周期
  "target_roi": 25.0              // 目标ROI(%)
}
```

#### ROI计算逻辑
```python
def calculate_roi(tenant_id: str, period: str = "monthly"):
    """
    计算投资回报率
    ROI = (总收入 - 总成本) / 总成本 × 100%
    """
    # 获取配置数据
    config = get_business_config(tenant_id, "roi_analysis")

    # 计算总投资成本
    total_costs = sum(config["investment_costs"].values())

    # 计算总收入
    total_revenue = sum(config["revenue_sources"].values())

    # 计算ROI
    roi = ((total_revenue - total_costs) / total_costs) * 100

    return {
        "roi": round(roi, 2),
        "total_costs": total_costs,
        "total_revenue": total_revenue,
        "profit": total_revenue - total_costs,
        "target_roi": config.get("target_roi", 25.0),
        "achievement_rate": round((roi / config.get("target_roi", 25.0)) * 100, 2)
    }
```

### 2. 关键商业指标

#### MRR (月度经常性收入)
```python
def calculate_mrr(tenant_id: str, date_range: tuple):
    """计算月度经常性收入"""
    start_date, end_date = date_range

    # 查询订阅收入
    subscription_revenue = db.query(
        func.sum(Subscription.amount)
    ).filter(
        Subscription.tenant_id == tenant_id,
        Subscription.status == 'active',
        Subscription.created_at.between(start_date, end_date)
    ).scalar() or 0

    return {
        "mrr": subscription_revenue,
        "growth_rate": calculate_growth_rate(subscription_revenue, previous_mrr),
        "forecast": forecast_mrr(subscription_revenue)
    }
```

#### CAC (客户获取成本)
```python
def calculate_cac(tenant_id: str, period: str):
    """
    计算客户获取成本
    CAC = 营销费用 / 新增付费用户数
    """
    # 获取营销费用配置
    marketing_costs = get_business_config(tenant_id, "marketing_costs")

    # 查询新增付费用户数
    new_paid_users = db.query(User).filter(
        User.tenant_id == tenant_id,
        User.is_paid == True,
        User.created_at >= get_period_start(period)
    ).count()

    cac = marketing_costs / new_paid_users if new_paid_users > 0 else 0

    return {
        "cac": round(cac, 2),
        "marketing_costs": marketing_costs,
        "new_paid_users": new_paid_users,
        "efficiency": "高效" if cac < 100 else "需优化"
    }
```

### 3. 用户分群分析

#### 用户分群配置
```python
USER_SEGMENTS = {
    "high_value": {
        "monthly_spending_threshold": 500,  // 月消费金额阈值
        "usage_frequency_threshold": 20,    // 使用频次阈值
        "description": "高价值用户"
    },
    "active": {
        "login_days_threshold": 15,         // 登录天数阈值
        "usage_count_threshold": 10,        // 使用次数阈值
        "description": "活跃用户"
    },
    "potential": {
        "registration_days_range": [7, 30], // 注册天数范围
        "trial_status": "active",           // 试用状态
        "description": "潜在用户"
    }
}
```

#### 用户分群实现
```python
def analyze_user_segments(tenant_id: str):
    """用户分群分析"""
    segments = {}

    for segment_key, config in USER_SEGMENTS.items():
        if segment_key == "high_value":
            users = db.query(User).filter(
                User.tenant_id == tenant_id,
                User.monthly_spending >= config["monthly_spending_threshold"],
                User.usage_frequency >= config["usage_frequency_threshold"]
            ).all()
        elif segment_key == "active":
            users = db.query(User).filter(
                User.tenant_id == tenant_id,
                User.login_days >= config["login_days_threshold"],
                User.usage_count >= config["usage_count_threshold"]
            ).all()
        # ... 其他分群逻辑

        segments[segment_key] = {
            "count": len(users),
            "percentage": len(users) / total_users * 100,
            "description": config["description"],
            "users": [{"id": u.id, "name": u.name} for u in users[:10]]
        }

    return segments
```

## 🚀 部署和运维

### 1. Docker容器化部署

#### docker-compose.yml配置
```yaml
version: '3.8'
services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "8080:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*******************************************/ai_ecosystem_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis

  # 数据库服务
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: ai_ecosystem_db
      POSTGRES_USER: ai_user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # 缓存服务
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### 2. 监控和日志

#### 性能监控
```python
import time
import logging
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time

            # 记录性能日志
            logging.info(f"{func.__name__} executed in {execution_time:.2f}s")

            # 性能告警
            if execution_time > 5.0:  # 超过5秒告警
                logging.warning(f"Slow query detected: {func.__name__} took {execution_time:.2f}s")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"{func.__name__} failed after {execution_time:.2f}s: {str(e)}")
            raise

    return wrapper

# 使用示例
@monitor_performance
async def get_agent_analysis(tenant_id: str, db: Session):
    # 分析逻辑
    pass
```

## 📋 功能补缺和扩展

### 1. 已实现功能清单

#### ✅ 核心功能模块
- **数据概览**：核心KPI指标、趋势图表、快速导航
- **智能体分析**：排行榜、分类分析、性能分析
- **用户行为分析**：用户画像、路径分析、留存分析
- **商业分析**：收入分析、转化分析、成本分析
- **实时监控**：实时浏览用户、头像池、用户列表
- **运营报告**：日报/周报/月报、自定义报告、数据导出
- **数据配置**：业务参数配置、分析指标设置

#### ✅ 技术特性
- **投资人级UI**：现代化设计、专业配色、响应式布局
- **实时通信**：WebSocket连接、心跳机制、状态同步
- **性能优化**：Redis缓存、数据库索引、查询优化
- **数据一致性**：事务处理、原子操作、并发控制

### 2. 待完善功能

#### ⚠️ 留存分析模块
```python
# 用户留存分析实现
def calculate_user_retention(tenant_id: str, cohort_period: str = "monthly"):
    """
    计算用户留存率
    支持日留存、周留存、月留存分析
    """
    # 实现逻辑待补充
    pass
```

#### ⚠️ 预测分析功能
```python
# 基于机器学习的预测分析
def predict_user_behavior(tenant_id: str, prediction_type: str):
    """
    用户行为预测
    - 流失预测
    - 付费预测
    - 使用量预测
    """
    # 实现逻辑待补充
    pass
```

### 3. 扩展建议

#### 🔮 未来功能规划
1. **AI智能分析**：基于机器学习的用户行为预测
2. **A/B测试平台**：功能测试和效果对比
3. **用户画像增强**：更详细的用户标签和分群
4. **实时告警系统**：异常数据自动告警
5. **多维度分析**：地域、设备、渠道等维度分析

## 📚 关键信息总结

### 1. 核心技术栈
- **前端**：Vue3 + TypeScript + Element Plus + ECharts
- **后端**：FastAPI + SQLAlchemy + PostgreSQL + Redis
- **部署**：Docker + Nginx + 容器化部署
- **监控**：日志系统 + 性能监控 + 错误追踪

### 2. 数据库表结构
- **ai_agents**：智能体主表（扩展统计字段）
- **agent_statistics**：智能体统计表（按日期聚合）
- **agent_users**：用户关系表（使用记录）
- **agent_realtime_viewers**：实时浏览表（在线状态）
- **reports**：报告主表（报告管理）
- **report_configs**：配置表（参数设置）

### 3. API接口体系
- **GET /api/v1/analytics/admin/overview**：数据概览
- **GET /api/v1/analytics/admin/agents**：智能体分析
- **GET /api/v1/analytics/admin/users**：用户行为分析
- **GET /api/v1/analytics/admin/business**：商业分析
- **GET /api/v1/analytics/realtime/viewers/{agent_id}**：实时浏览用户
- **POST /api/v1/analytics/realtime/heartbeat**：心跳接口
- **POST /api/v1/analytics/track**：数据埋点

### 4. 部署访问地址
- **管理后台**：http://**************:8080/admin
- **数据概览**：http://**************:8080/admin/agent/analytics/overview
- **数据配置**：http://**************:8080/admin/system/data-config

### 5. 开发规范
- **代码规范**：TypeScript严格模式、ESLint规则
- **命名规范**：驼峰命名、语义化命名
- **文档规范**：API文档、代码注释、开发文档
- **测试规范**：单元测试、集成测试、性能测试

---

**文档版本**：v1.0
**最后更新**：2025-07-06
**维护人员**：AI开发团队
**联系方式**：技术支持邮箱
```
