# 🚀 AI生态平台SaaS产品开发方案

## 📋 项目概述

### 🎯 产品定位
AI生态平台采用多租户SaaS模式，为代理商提供独立的AI智能体管理后台，支持自定义域名、品牌配置、支付集成等功能，实现"卖坑位"的商业模式。

### 多租户架构模式
采用多租户SaaS模式，支持单租户和多租户混合部署，满足不同业务场景需求：

#### 租户ID标准化规范

**统一标准格式**：16位字符串 = 年份(4位) + 随机字母(8位) + 月日(4位)

```go
// 标准租户ID生成示例
2025CeesAiDr0716  // 赛斯AI直营平台（2025年7月16日创建）
2025AgentAbc1225  // 代理商ABC（2025年12月25日创建）
2025SmartAi0301   // 智能AI代理商（2025年3月1日创建）
```

**验证规则**：
```go
func isValidTenantID(tenantID string) bool {
    // 必须是16位字符串
    if len(tenantID) != 16 {
        return false
    }

    // 只允许字母和数字
    for _, char := range tenantID {
        if !((char >= 'a' && char <= 'z') ||
            (char >= 'A' && char <= 'Z') ||
            (char >= '0' && char <= '9')) {
            return false
        }
    }

    return true
}
```

**重要升级**：
- ✅ **无特殊租户ID**：移除`default`、`system`等特殊ID
- ✅ **完全标准化**：所有租户统一使用16位标准格式
- ✅ **数据库化管理**：默认租户ID存储在`system_configs`表
- ✅ **严禁硬编码**：任何租户ID都不允许硬编码

#### 租户隔离策略
```
租户隔离策略：
├── 数据隔离：独立数据库/共享数据库+租户标识
├── 应用隔离：独立域名/子域名访问
├── 资源隔离：独立计算资源/共享资源池
├── 权限隔离：基于租户的RBAC权限体系
└── 推广隔离：独立推广链接和佣金分成体系
```

#### SAAS推广溯源机制

**核心商业价值**：
- 🎯 **精准用户归属**：每个注册用户准确追溯到推广来源
- 💰 **自动佣金分成**：基于用户归属自动计算代理商佣金
- 📊 **完整数据分析**：推广效果统计、转化率分析、ROI计算
- 🚀 **病毒式增长**：支持多级推广体系，实现指数级用户增长

**推广溯源流程**：
```
用户访问 → 租户识别 → 用户注册 → 推广溯源记录 → 佣金分成
    ↓           ↓           ↓           ↓           ↓
推广链接    查询租户ID    绑定租户    记录来源    计算佣金
```

**技术实现架构**：
```sql
-- 推广溯源数据库架构
CREATE TABLE distributors (          -- 代理商管理
    tenant_id VARCHAR(16),
    distributor_code VARCHAR(20),
    commission_rate DECIMAL(5,4)
);

CREATE TABLE referral_links (        -- 推广链接管理
    tenant_id VARCHAR(16),
    distributor_id INTEGER,
    referral_code VARCHAR(20),
    click_count INTEGER,
    conversion_count INTEGER
);

CREATE TABLE user_referral_sources ( -- 用户推广溯源
    user_id VARCHAR(50),
    tenant_id VARCHAR(16),
    source_type VARCHAR(20),         -- 'domain', 'referral_code', 'direct'
    distributor_id INTEGER,
    referral_code VARCHAR(20)
);
```

#### 配置存储原则
**核心原则**：所有租户配置存储在数据库中，**严禁硬编码**

```sql
-- 系统配置表（存储默认租户ID等全局配置）
CREATE TABLE system_configs (
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT
);

-- 租户配置表（存储各租户个性化配置）
CREATE TABLE tenant_configs (
    tenant_id VARCHAR(16) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,

    UNIQUE(tenant_id, config_category, config_key)
);
```

**配置分类体系**：
| 分类 | 说明 | 商业价值 |
|------|------|----------|
| `app_basic` | 应用基础配置 | 品牌定制、域名绑定 |
| `domain_ssl` | 域名和SSL配置 | 独立品牌形象 |
| `third_party_coze` | 扣子平台配置 | AI能力接入 |
| `cloud_storage` | 阿里云OSS配置 | 独立存储空间 |
| `payment_wechat` | 微信支付配置 | 独立收款账户 |
| `payment_alipay` | 支付宝支付配置 | 多渠道收款 |

### 🏗️ 产品架构
```
AI生态平台SaaS架构
├── 开发公司直营平台（www.cees.cc）
├── 代理商A平台（agent001.cees.cc）
├── 代理商B平台（smart.agent-b.com）
├── 代理商C平台（ai.agent-c.cn）
└── ... 支持100+代理商
```

## 🎯 开发优先级战略

### 📊 当前状况分析
```
现有模块状态：
├── ✅ 已完成
│   ├── 用户管理系统（ai-users）
│   ├── 智能体管理系统（ai-agents）
│   ├── 前端界面系统（frontend）
│   └── 基础架构（数据库、容器等）
├── 🚧 待开发核心模块
│   ├── 大模型API中转（ai-llms）
│   └── AI插件工具（ai-tools）
└── 🔧 待完善功能
    ├── SaaS多租户完善
    ├── 超级管理后台
    └── 其他产品版本
```

### 🥇 推荐开发顺序

#### 第一阶段：SaaS版BUG修复与推广溯源系统升级（1周）✅ 已完成

**🎯 重大架构升级：租户ID标准化与推广溯源机制**

**租户ID标准化升级：**
- ✅ **移除特殊租户ID**: 彻底移除`default`、`system`等特殊ID概念
- ✅ **统一16位标准格式**: 所有租户使用年份+随机字母+月日格式
- ✅ **数据库化管理**: 默认租户ID存储在`system_configs`表
- ✅ **严禁硬编码**: 任何租户ID都不允许硬编码在代码中
- ✅ **智能验证机制**: 完整的租户ID格式验证和安全检查

**推广溯源机制实现：**
- ✅ **代理商管理系统**: 完整的分销商等级、佣金率管理
- ✅ **推广链接生成**: 支持个性化推广码和链接管理
- ✅ **用户归属追踪**: 精准记录每个用户的推广来源
- ✅ **智能租户解析**: 支持域名、推广码、默认租户三级解析
- ✅ **推广统计分析**: 点击量、转化率、佣金计算等完整数据

**安全防护升级：**
- ✅ **IP访问阻断**: 完全阻断IP直接访问，防止DOS攻击
- ✅ **域名白名单**: 只允许合法域名访问系统
- ✅ **推广码验证**: 防SQL注入的安全推广码验证机制
- ✅ **数据库安全**: 所有查询使用参数化防注入

**数据库架构完善：**
- ✅ **5张核心表**: system_configs、tenant_configs、distributors、referral_links、user_referral_sources
- ✅ **完整索引优化**: 查询性能优化和数据完整性约束
- ✅ **配置分类体系**: 10大配置分类，支持加密存储
- ✅ **审计日志**: 完整的配置变更和用户行为审计

**商业价值突破：**
- 🚀 **真正SAAS化**: 支持无限代理商独立运营
- 💰 **自动佣金分成**: 基于用户归属的智能佣金计算
- 📊 **数据驱动增长**: 完整的推广效果分析和优化
- 🛡️ **企业级安全**: 多层安全防护和合规审计
- 🎯 **精准营销**: 推广来源追踪和转化率优化

#### 第二阶段：大模型API开发（3-4周）
**开发内容：**
- 多模型接入（OpenAI、Claude、文心一言等）
- API密钥管理和轮询
- 请求限流和配额管理
- 成本统计和计费
- 模型性能监控
- 错误处理和重试
- 租户隔离和权限控制

**优先原因：**
- AI应用的核心是大模型调用
- 客户最关心的功能
- 直接影响产品竞争力
- 是智能体运行的基础

#### 第三阶段：AI插件工具开发（2-3周）
**开发内容：**
- 文案生成工具
- 图片处理工具
- 视频编辑工具
- 数据分析工具
- 代码生成工具
- 工具市场和管理
- 工具使用统计

#### 第四阶段：超级管理后台（2周）
**开发内容：**
- 代理商管理和开通
- 跳转到代理商后台
- 使用统计和监控
- 客户支持工单系统
- 实时监控面板

#### 第五阶段：其他产品版本（4-6周）
- 独立部署版本
- 源码授权版本
- 源码买断版本

## 📅 详细开发时间表

### 🗓️ 12周完整开发计划

```
第1周：SaaS版BUG修复
├── 修复租户ID验证问题
├── 完善智能体同步功能
├── 解决前端API调用问题
├── 数据库结构优化
└── 功能测试和上线

第2-5周：大模型API开发（ai-llms）
├── 第2周：基础架构和多模型接入
├── 第3周：API管理和配额控制
├── 第4周：计费统计和监控
├── 第5周：测试优化和文档

第6-8周：AI插件工具开发（ai-tools）
├── 第6周：工具框架和基础工具
├── 第7周：高级工具和工具市场
├── 第8周：测试优化和集成

第9-10周：超级管理后台
├── 第9周：基础功能和SaaS管理
├── 第10周：其他版本管理和监控

第11-12周：其他产品版本
├── 第11周：独立部署版本
├── 第12周：源码授权版本
```

## 🏢 SaaS多租户架构设计

### 🎯 推广溯源技术实现

#### 1. 租户解析服务架构
```go
// TenantResolverService - 核心租户解析服务
type TenantResolverService struct {
    db *gorm.DB
}

// 智能租户解析 - 支持推广溯源
func (s *TenantResolverService) ResolveTenantFromRequest(
    ctx context.Context, host, referralCode, userAgent, clientIP string) (*TenantResolution, error) {

    // 优先级1：推广码识别（最高优先级）
    if referralCode != "" {
        if resolution, err := s.resolveTenantByReferralCode(ctx, referralCode); err == nil {
            // 自动更新推广统计
            s.updateReferralStats(ctx, referralCode)
            return resolution, nil
        }
    }

    // 优先级2：域名识别
    if host != "" {
        if resolution, err := s.resolveTenantByDomain(ctx, host); err == nil {
            return resolution, nil
        }
    }

    // 优先级3：默认租户（从数据库读取）
    return s.getDefaultTenant(ctx)
}
```

#### 2. 用户注册推广溯源
```go
// 用户注册时自动记录推广来源
func (s *UserService) CreateUser(tenantID, username, email, password string,
    tenantResolution *TenantResolution) (*models.User, error) {

    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 创建用户
        user := &models.User{
            TenantID: tenantID,
            Username: username,
            Email:    email,
            // ... 其他字段
        }
        if err := tx.Create(user).Error; err != nil {
            return err
        }

        // 2. 记录推广溯源
        if tenantResolution != nil {
            source := &models.UserReferralSource{
                UserID:        user.ID,
                TenantID:      tenantID,
                SourceType:    tenantResolution.SourceType,
                SourceValue:   tenantResolution.SourceValue,
                DistributorID: tenantResolution.DistributorID,
                ReferralCode:  tenantResolution.ReferralCode,
            }
            if err := tx.Create(source).Error; err != nil {
                return err
            }

            // 3. 更新转化统计
            if tenantResolution.ReferralCode != "" {
                tx.Model(&models.ReferralLink{}).
                    Where("referral_code = ?", tenantResolution.ReferralCode).
                    UpdateColumn("conversion_count", gorm.Expr("conversion_count + 1"))
            }
        }

        return nil
    })
}
```

#### 3. 佣金分成计算系统
```go
// 自动佣金计算服务
type CommissionService struct {
    db *gorm.DB
}

func (s *CommissionService) CalculateCommission(orderID, userID string, amount decimal.Decimal) error {
    // 1. 查询用户推广来源
    var source models.UserReferralSource
    err := s.db.Where("user_id = ?", userID).First(&source).Error
    if err != nil {
        return nil // 无推广来源，无需计算佣金
    }

    // 2. 查询代理商佣金率
    var distributor models.Distributor
    err = s.db.Where("id = ?", source.DistributorID).First(&distributor).Error
    if err != nil {
        return err
    }

    // 3. 计算佣金金额
    commissionAmount := amount.Mul(decimal.NewFromFloat(distributor.CommissionRate))

    // 4. 创建佣金记录
    commission := &models.Commission{
        OrderID:          orderID,
        UserID:           userID,
        DistributorID:    distributor.ID,
        TenantID:         distributor.TenantID,
        BaseAmount:       amount,
        CommissionRate:   distributor.CommissionRate,
        CommissionAmount: commissionAmount,
        Status:           "pending",
        ReferralCode:     source.ReferralCode,
    }

    return s.db.Create(commission).Error
}
```

### 📊 租户隔离策略

#### 1. 数据隔离
```sql
-- 所有业务表都包含tenant_id字段
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(16) NOT NULL,  -- 标准16位租户ID
    username VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    -- 其他字段...
    INDEX idx_tenant_id (tenant_id)
);

-- 查询时必须包含租户过滤
SELECT * FROM users WHERE tenant_id = 'agent_001';
```

#### 2. 域名隔离
```javascript
// 租户识别中间件
function identifyTenant(req, res, next) {
  const host = req.get('host');
  let tenantId;
  
  if (host === 'www.cees.cc') {
    tenantId = 'direct';  // 直营
  } else if (host.includes('.cees.cc')) {
    tenantId = host.split('.')[0]; // 子域名
  } else {
    // 查询自定义域名映射
    tenantId = await getTenantByCustomDomain(host);
  }
  
  req.tenantId = tenantId;
  next();
}
```

#### 3. 配置隔离（数据库化多租户配置系统）

**🎯 设计理念**: 所有租户配置参数存储在数据库中，支持动态配置和实时生效，彻底解决硬编码问题。

**📊 配置分类体系**:
```
多租户配置分类：
├── 应用基础配置（app_basic）
│   ├── 平台名称、版本、域名
│   ├── 前端协议、端口、SSL配置
│   └── 自定义CSS、Logo、图标
├── 域名与SSL配置（domain_ssl）
│   ├── 主域名、备用域名配置
│   ├── SSL证书类型和自动续期
│   └── CDN加速和安全头配置
├── 邮件服务配置（email_service）
│   ├── SMTP服务器和认证信息
│   └── 发件人名称和加密方式
├── 对象存储配置（storage_service）
│   ├── 阿里云OSS完整配置
│   └── 存储类型和自定义域名
├── 支付服务配置（wechat_pay, alipay_service）
│   ├── 微信支付商户配置
│   └── 支付宝应用配置
├── 短信服务配置（sms_service）
│   └── 阿里云短信服务配置
├── 大模型API配置（llm_service）
│   └── OpenAI等大模型API配置
└── 系统高级配置（system_advanced）
    ├── 日志级别、会话超时
    ├── 上传限制、缓存配置
    └── 备份设置、维护模式
```

**🗄️ 数据库表结构**:
```sql
-- 租户配置主表
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,        -- 租户ID
    config_category VARCHAR(50) NOT NULL,  -- 配置分类
    config_key VARCHAR(100) NOT NULL,      -- 配置键
    config_value TEXT,                     -- 配置值
    config_type VARCHAR(20) DEFAULT 'string',
    is_encrypted BOOLEAN DEFAULT FALSE,    -- 敏感配置加密
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, config_category, config_key)
);

-- 配置模板表（支持新租户快速初始化）
CREATE TABLE config_templates (
    id SERIAL PRIMARY KEY,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    default_value TEXT,                    -- 默认值
    is_required BOOLEAN DEFAULT FALSE,     -- 是否必需
    validation_rule TEXT,                  -- 验证规则
    display_name VARCHAR(200),             -- 前端显示名称
    input_type VARCHAR(20) DEFAULT 'text', -- 输入类型
    help_text TEXT                         -- 帮助文本
);

-- 配置变更历史表（审计追踪）
CREATE TABLE config_change_logs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    old_value TEXT,                        -- 旧值
    new_value TEXT,                        -- 新值
    changed_by VARCHAR(50) NOT NULL,       -- 变更者
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**🎛️ 配置管理界面**:
- **SAAS管理后台**: `/admin/system/params-config` - 管理所有租户配置
- **租户客户后台**: `/tenant/config` - 租户自主配置（待开发）
- **可视化编辑**: 支持文本、密码、数字、选择、开关等输入类型
- **实时生效**: 配置变更后立即生效，无需重启服务

**🔒 安全特性**:
- **敏感配置加密**: 支付密钥、API密钥等自动加密存储
- **权限控制**: 基于角色的配置访问权限
- **变更审计**: 完整的配置变更历史记录
- **配置验证**: 前端和后端双重验证机制

### 🔧 技术实现要点

#### 租户中间件优化
```javascript
// 优化后的租户中间件
function TenantID() {
  return function(c *gin.Context) {
    // 跳过健康检查路径
    if (c.Request.URL.Path == "/health" ||
       c.Request.URL.Path == "/ready" ||
       c.Request.URL.Path == "/metrics") {
      c.Set("tenant_id", "default")
      c.Next()
      return
    }

    tenantID := c.GetHeader("X-Tenant-ID")
    if (tenantID == "") {
      tenantID = c.Query("tenant_id")
    }
    // 如果还是没有，使用默认租户ID
    if (tenantID == "") {
      tenantID = "default"
    }

    c.Set("tenant_id", tenantID)
    c.Next()
  }
}
```

## 🔄 版本管理和功能同步

### 📦 统一版本控制策略

#### 版本号规范
```
版本号格式：主版本.次版本.修订版本
例如：1.2.3
├── 1 = 主版本（重大功能更新）
├── 2 = 次版本（新功能添加）
└── 3 = 修订版本（BUG修复）
```

#### 不同产品版本的更新策略
```javascript
const VERSION_STRATEGY = {
  SAAS: {
    auto_update: true,        // 自动更新
    update_schedule: 'weekly', // 每周更新
    rollback_support: true    // 支持回滚
  },
  
  PRIVATE_DEPLOYMENT: {
    auto_update: false,       // 手动更新
    update_notification: true, // 更新通知
    update_package: true      // 提供更新包
  },
  
  SOURCE_LICENSE: {
    source_sync: true,        // 源码同步
    update_frequency: 'monthly', // 月度更新
    version_control: 'git'    // Git版本控制
  },
  
  SOURCE_BUYOUT: {
    full_source: true,        // 完整源码
    update_optional: true,    // 可选更新
    support_period: '6months' // 6个月支持期
  }
};
```

### 🔧 功能同步实现

#### 1. SaaS版本同步（自动）
```javascript
class SaaSAutoUpdate {
  async deployNewVersion(version) {
    // 1. 灰度发布
    await this.grayScaleDeployment(version, 0.1); // 10%流量
    
    // 2. 监控指标
    const metrics = await this.monitorMetrics(30); // 监控30分钟
    
    // 3. 全量发布或回滚
    if (metrics.success_rate > 0.99) {
      await this.fullDeployment(version);
    } else {
      await this.rollback();
    }
    
    // 4. 通知所有租户
    await this.notifyTenants(version);
  }
}
```

#### 2. 独立部署版本同步（推送）
```javascript
class PrivateDeploymentUpdate {
  async pushUpdateToCustomers(version) {
    const customers = await this.getPrivateDeploymentCustomers();
    
    for (const customer of customers) {
      // 1. 生成客户专属更新包
      const updatePackage = await this.generateUpdatePackage(
        customer.id, 
        version
      );
      
      // 2. 发送更新通知
      await this.sendUpdateNotification(customer, {
        version: version,
        download_url: updatePackage.url,
        update_notes: version.release_notes,
        expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      });
    }
  }
}
```

## 💰 商业模式和定价策略

### 📊 产品定价矩阵

| 产品版本 | 价格 | 适用客户 | 主要特点 |
|---------|------|----------|----------|
| **SaaS版** | ¥1000-5000/月 | 代理商、小企业 | 无需部署，快速上线 |
| **单平台版** | ¥50万 | 大企业、政府 | 独立部署，数据安全 |
| **多平台版** | ¥100万 | 软件公司、集成商 | 可分租，商业模式灵活 |
| **年费授权** | ¥30-100万/年 | 科技公司、开发商 | 源码可见，持续支持 |
| **永久授权** | ¥100-300万 | 大型企业 | 一次付费，永久使用 |
| **源码买断** | ¥500-1000万 | 科技巨头 | 完全所有权，无限制 |

### 🎯 SaaS版收费模式
```
代理商费用：
├── 基础版: ¥1000/月（10个智能体，1000用户）
├── 标准版: ¥2000/月（50个智能体，5000用户）
├── 专业版: ¥5000/月（无限智能体，无限用户）
└── 企业版: ¥10000/月（定制功能，专属支持）

按使用量计费：
├── 每个用户: ¥5-10/月
├── API调用: ¥0.01/次
├── 存储空间: ¥1/GB/月
└── 带宽使用: ¥0.5/GB
```

## 📈 投入产出分析

### 💰 开发成本估算
```
人力成本（12周）：
├── 后端开发工程师：2人 × 12周 = 24人周
├── 前端开发工程师：1人 × 10周 = 10人周
├── UI/UX设计师：1人 × 4周 = 4人周
├── 测试工程师：1人 × 6周 = 6人周
└── 项目经理：1人 × 12周 = 12人周
总计：56人周

按平均薪资¥8000/周计算：
总开发成本：56 × ¥8000 = ¥448,000

基础设施成本：
├── 服务器和云服务：¥50,000
├── 开发工具和软件：¥20,000
├── 第三方服务集成：¥30,000
└── 测试和部署环境：¥20,000
总计：¥120,000

总投入：¥568,000
```

### 📊 预期收益分析
```
SaaS版收益预测：
├── 第1个月：10个代理商 × ¥2000 = ¥20,000
├── 第3个月：30个代理商 × ¥2000 = ¥60,000
├── 第6个月：60个代理商 × ¥2000 = ¥120,000
├── 第12个月：100个代理商 × ¥2000 = ¥200,000
└── 年收入：¥1,440,000

其他版本收益：
├── 独立部署版：5个客户 × ¥75万 = ¥375万
├── 源码授权版：3个客户 × ¥50万 = ¥150万
└── 源码买断版：1个客户 × ¥500万 = ¥500万

总年收益：¥1,440万
投资回报率：2,435%
回本周期：1.4个月
```

## 🎯 关键成功因素

### ✅ 技术层面
```
系统稳定性：
├── 7×24小时稳定运行
├── 99.9%以上可用性
├── 完善的监控和告警
└── 快速故障恢复能力

数据安全：
├── 租户数据完全隔离
├── 敏感信息加密存储
├── 完善的权限控制
└── 定期安全审计

性能优化：
├── 数据库查询优化
├── 缓存策略设计
├── CDN加速部署
└── 负载均衡配置
```

### 💡 商业层面
```
客户满意度：
├── 快速响应客户需求
├── 提供优质技术支持
├── 持续产品功能迭代
└── 建立客户成功团队

市场推广：
├── 制定有效的获客策略
├── 建立合作伙伴生态
├── 参与行业展会和活动
└── 建立品牌知名度

运营效率：
├── 自动化运营流程
├── 完善的客户服务体系
├── 数据驱动的决策
└── 持续的产品优化
```

## 🚀 立即行动计划

### ✅ 本周行动项
1. **立即修复SaaS版BUG**
   - 修复租户ID验证问题
   - 解决智能体同步问题
   - 完善前端API调用

2. **制定详细开发计划**
   - 确定开发团队分工
   - 制定每周开发目标
   - 建立项目管理流程

3. **准备技术架构**
   - 设计大模型API架构
   - 规划AI工具插件框架
   - 完善数据库设计

### 🎯 下周启动项
1. **开始大模型API开发**
2. **建立版本管理机制**
3. **设计超级管理后台原型**

---

**文档版本**: v1.0  
**创建时间**: 2025-01-14  
**最后更新**: 2025-01-14  
**负责人**: AI生态平台开发团队  
**审核状态**: 待审核
