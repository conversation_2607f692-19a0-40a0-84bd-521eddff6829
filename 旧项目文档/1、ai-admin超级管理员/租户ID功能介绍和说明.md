# AI生态平台租户ID功能介绍和说明

**文档版本**: v1.0  
**更新时间**: 2025年7月27日  
**功能模块**: 租户ID管理体系  
**开发状态**: 需求设计阶段  
**负责团队**: ai-admin开发团队  

---

## 📋 目录

1. [租户ID概述](#租户ID概述)
2. [租户ID生成规则](#租户ID生成规则)
3. [租户ID功能特性](#租户ID功能特性)
4. [租户ID管理功能](#租户ID管理功能)
5. [租户ID使用场景](#租户ID使用场景)
6. [技术实现方案](#技术实现方案)
7. [安全与权限控制](#安全与权限控制)
8. [运维监控](#运维监控)

---

## 🎯 租户ID概述

### 1.1 定义与作用
租户ID是AI生态平台多租户架构的核心标识符，用于区分和隔离不同的SAAS代理商租户。每个租户ID代表一个独立的业务实体，拥有完全隔离的数据空间和独立的运营权限。

### 1.2 设计原则
- **唯一性**: 全平台唯一，永不重复
- **简洁性**: 6位字符，易于记忆和使用
- **可读性**: 大写英文字母和数字组合，避免歧义字符
- **扩展性**: 支持大规模租户扩展（约2.1亿个组合）
- **稳定性**: 一旦分配，终身不变

### 1.3 业务价值
- **数据隔离**: 确保租户间数据完全隔离
- **独立运营**: 支持代理商独立运营业务
- **品牌定制**: 支持自定义域名和品牌化
- **权限控制**: 基于租户的精细化权限管理
- **商业模式**: 支持"卖坑位"的SAAS商业模式

---

## 🔧 租户ID生成规则

### 2.1 格式规范
**标准格式**: 6位大写英文字母和数字组合

**字符集**: A-Z, 0-9 (排除易混淆字符)
- 排除字符: 0(零)与O(欧), 1(一)与I(艾)与l(小写L)
- 实际字符集: A-Z(除I,O) + 2-9 = 24字母 + 8数字 = 32个字符

**生成示例**:
```
D6SS21  # 代理商租户ID
A8BC45  # 代理商租户ID  
F2GH89  # 代理商租户ID
K3MN67  # 代理商租户ID
```

### 2.2 生成算法
```javascript
// 租户ID生成算法
function generateTenantId() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // 排除易混淆字符
    let result = '';
    
    // 生成6位随机字符
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // 检查唯一性
    while (await checkTenantIdExists(result)) {
        result = generateTenantId(); // 重新生成
    }
    
    return result;
}
```

### 2.3 唯一性保证
- **数据库约束**: 租户ID字段设置唯一索引
- **生成检查**: 生成时实时检查数据库唯一性
- **重试机制**: 冲突时自动重新生成
- **预留机制**: 预留特殊租户ID（如：ADMIN, SYSTEM等）

---

## ⚡租户ID功能特性

### 3.1 核心功能
#### 3.1.1 租户识别
- **域名解析**: 通过域名自动识别租户ID
- **参数解析**: 从URL参数中提取租户ID
- **API识别**: API请求中的租户身份验证
- **数据过滤**: 基于租户ID的数据自动过滤

#### 3.1.2 数据隔离
- **数据库隔离**: 所有业务表包含tenant_id字段
- **文件隔离**: 文件存储按租户ID分目录
- **缓存隔离**: Redis缓存按租户ID分区
- **日志隔离**: 操作日志按租户ID分类

#### 3.1.3 权限控制
- **访问控制**: 基于租户ID的访问权限验证
- **功能权限**: 不同租户的功能开关控制
- **资源配额**: 基于租户的资源使用限制
- **API限流**: 按租户ID的API调用频率限制

### 3.2 扩展功能
#### 3.2.1 域名管理
- **默认域名**: 系统分配的默认子域名
- **自定义域名**: 支持绑定多个自定义域名
- **域名验证**: DNS验证确保域名所有权
- **SSL证书**: 自动配置和更新SSL证书

#### 3.2.2 配置管理
- **个性化配置**: 租户级别的系统配置
- **主题定制**: 自定义UI主题和Logo
- **功能开关**: 租户级别的功能开关
- **第三方集成**: 租户独立的第三方服务配置

---

## 🛠️ 租户ID管理功能

### 4.1 租户创建
#### 4.1.1 创建流程
```
申请提交 → 资质审核 → 租户ID生成 → 初始化配置 → 域名分配 → 账户开通 → 培训交付
```

#### 4.1.2 创建参数
- **基础信息**: 公司名称、联系人、联系方式
- **业务信息**: 业务类型、预期用户规模、功能需求
- **技术信息**: 域名需求、集成需求、定制需求
- **商务信息**: 合作模式、费用标准、服务等级

#### 4.1.3 自动初始化
- **数据库初始化**: 创建租户相关数据表记录
- **配置初始化**: 设置默认配置参数
- **权限初始化**: 创建租户管理员账户
- **资源初始化**: 分配初始资源配额

### 4.2 租户管理
#### 4.2.1 基础管理
- **租户信息**: 查看和编辑租户基础信息
- **状态管理**: 正常、暂停、终止、删除状态控制
- **配额管理**: 用户数量、存储空间、API调用限制
- **功能管理**: 功能模块的开通和关闭

#### 4.2.2 高级管理
- **数据迁移**: 租户数据的导入导出
- **备份恢复**: 租户数据的备份和恢复
- **监控告警**: 租户运营状况监控
- **审计日志**: 租户操作的审计追踪

### 4.3 批量操作
#### 4.3.1 批量管理
- **批量创建**: 基于模板批量创建租户
- **批量配置**: 批量修改租户配置
- **批量通知**: 向多个租户发送通知
- **批量统计**: 多租户数据统计分析

#### 4.3.2 模板管理
- **创建模板**: 预定义租户创建模板
- **配置模板**: 常用配置的模板化
- **权限模板**: 标准权限配置模板
- **功能模板**: 功能组合的模板化

---

## 🎯 租户ID使用场景

### 5.1 用户注册场景
```javascript
// 用户注册时的租户识别
function registerUser(domain, userData) {
    // 1. 从域名识别租户ID
    const tenantId = extractTenantFromDomain(domain);
    
    // 2. 验证租户状态
    if (!await validateTenantStatus(tenantId)) {
        throw new Error('租户不可用');
    }
    
    // 3. 生成用户ID（租户ID + 递增数字）
    const userId = await generateUserId(tenantId);
    
    // 4. 创建用户记录
    return await createUser({
        ...userData,
        tenant_id: tenantId,
        user_id: userId
    });
}
```

### 5.2 数据查询场景
```javascript
// 基于租户ID的数据查询
function getUserList(tenantId, filters) {
    return db.users.findAll({
        where: {
            tenant_id: tenantId,  // 自动添加租户过滤
            ...filters
        }
    });
}
```

### 5.3 API访问场景
```javascript
// API中间件：租户验证
function tenantMiddleware(req, res, next) {
    const tenantId = extractTenantId(req);
    
    if (!tenantId) {
        return res.status(400).json({ error: '缺少租户标识' });
    }
    
    req.tenantId = tenantId;
    next();
}
```

---

## 💻 技术实现方案

### 6.1 数据库设计
```sql
-- 租户主表
CREATE TABLE tenants (
    tenant_id VARCHAR(6) PRIMARY KEY,
    company_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(50) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    contact_email VARCHAR(100) NOT NULL,
    status ENUM('active', 'suspended', 'terminated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 租户域名表
CREATE TABLE tenant_domains (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(6) NOT NULL,
    domain VARCHAR(100) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    ssl_status ENUM('none', 'pending', 'active', 'expired') DEFAULT 'none',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_domain (domain),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    INDEX idx_tenant_id (tenant_id)
);

-- 租户配置表
CREATE TABLE tenant_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(6) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_tenant_config (tenant_id, config_key),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    INDEX idx_tenant_id (tenant_id)
);
```

### 6.2 缓存策略
```javascript
// Redis缓存策略
const CACHE_KEYS = {
    TENANT_INFO: 'tenant:info:',
    TENANT_CONFIG: 'tenant:config:',
    TENANT_DOMAINS: 'tenant:domains:',
    DOMAIN_TENANT: 'domain:tenant:'
};

// 租户信息缓存
async function getTenantInfo(tenantId) {
    const cacheKey = CACHE_KEYS.TENANT_INFO + tenantId;
    let tenantInfo = await redis.get(cacheKey);
    
    if (!tenantInfo) {
        tenantInfo = await db.tenants.findByPk(tenantId);
        await redis.setex(cacheKey, 3600, JSON.stringify(tenantInfo));
    }
    
    return JSON.parse(tenantInfo);
}
```

### 6.3 域名解析机制
```javascript
// 域名到租户ID的解析
function extractTenantFromDomain(host) {
    // 1. 检查是否为默认域名格式 (tenant.cees.cc)
    const defaultDomainMatch = host.match(/^([A-Z0-9]{6})\.cees\.cc$/i);
    if (defaultDomainMatch) {
        return defaultDomainMatch[1].toUpperCase();
    }

    // 2. 查询自定义域名映射
    return await queryTenantByCustomDomain(host);
}

// 自定义域名查询
async function queryTenantByCustomDomain(domain) {
    const cacheKey = CACHE_KEYS.DOMAIN_TENANT + domain;
    let tenantId = await redis.get(cacheKey);

    if (!tenantId) {
        const domainRecord = await db.tenant_domains.findOne({
            where: { domain: domain, is_verified: true }
        });

        if (domainRecord) {
            tenantId = domainRecord.tenant_id;
            await redis.setex(cacheKey, 1800, tenantId);
        }
    }

    return tenantId;
}
```

### 6.4 中间件实现
```javascript
// Express中间件：租户识别和验证
function tenantMiddleware() {
    return async (req, res, next) => {
        try {
            // 1. 提取租户ID
            const tenantId = extractTenantFromDomain(req.get('host'));

            if (!tenantId) {
                return res.status(400).json({
                    error: 'INVALID_TENANT',
                    message: '无效的租户标识'
                });
            }

            // 2. 验证租户状态
            const tenantInfo = await getTenantInfo(tenantId);
            if (!tenantInfo || tenantInfo.status !== 'active') {
                return res.status(403).json({
                    error: 'TENANT_UNAVAILABLE',
                    message: '租户不可用'
                });
            }

            // 3. 设置请求上下文
            req.tenantId = tenantId;
            req.tenantInfo = tenantInfo;

            next();
        } catch (error) {
            res.status(500).json({
                error: 'TENANT_ERROR',
                message: '租户验证失败'
            });
        }
    };
}
```

---

## 🔒 安全与权限控制

### 7.1 访问安全
#### 7.1.1 租户隔离
- **数据隔离**: 严格的数据库级别隔离
- **文件隔离**: 文件系统按租户分区
- **网络隔离**: 基于租户的网络访问控制
- **进程隔离**: 容器级别的进程隔离

#### 7.1.2 权限验证
```javascript
// 租户权限验证中间件
function tenantPermissionMiddleware(requiredPermission) {
    return async (req, res, next) => {
        const { tenantId, user } = req;

        // 验证用户是否属于该租户
        if (user.tenant_id !== tenantId) {
            return res.status(403).json({
                error: 'TENANT_MISMATCH',
                message: '用户租户不匹配'
            });
        }

        // 验证租户级别权限
        const hasPermission = await checkTenantPermission(
            tenantId,
            user.role,
            requiredPermission
        );

        if (!hasPermission) {
            return res.status(403).json({
                error: 'INSUFFICIENT_PERMISSION',
                message: '权限不足'
            });
        }

        next();
    };
}
```

### 7.2 数据安全
#### 7.2.1 敏感数据保护
- **配置加密**: 敏感配置信息加密存储
- **传输加密**: HTTPS强制加密传输
- **存储加密**: 数据库字段级加密
- **访问日志**: 详细的数据访问审计

#### 7.2.2 备份安全
- **定期备份**: 自动化的数据备份机制
- **异地备份**: 多地域备份存储
- **加密备份**: 备份文件加密存储
- **恢复测试**: 定期备份恢复测试

### 7.3 操作审计
```javascript
// 租户操作审计
function auditTenantOperation(operation, tenantId, userId, details) {
    return db.tenant_audit_logs.create({
        tenant_id: tenantId,
        user_id: userId,
        operation: operation,
        details: JSON.stringify(details),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: new Date()
    });
}

// 使用示例
await auditTenantOperation('CREATE_USER', tenantId, adminId, {
    new_user_id: newUserId,
    user_role: 'user'
});
```

---

## 📊 运维监控

### 8.1 性能监控
#### 8.1.1 关键指标
- **租户数量**: 总租户数、活跃租户数、新增租户数
- **用户分布**: 各租户用户数量分布
- **资源使用**: CPU、内存、存储、网络使用情况
- **API调用**: 各租户API调用频率和响应时间

#### 8.1.2 监控实现
```javascript
// 租户性能监控
class TenantMonitor {
    // 记录API调用
    static recordApiCall(tenantId, endpoint, responseTime, statusCode) {
        const metrics = {
            tenant_id: tenantId,
            endpoint: endpoint,
            response_time: responseTime,
            status_code: statusCode,
            timestamp: Date.now()
        };

        // 发送到监控系统
        this.sendToMonitoring(metrics);

        // 更新Redis统计
        this.updateRedisStats(tenantId, metrics);
    }

    // 检查租户健康状态
    static async checkTenantHealth(tenantId) {
        const health = {
            tenant_id: tenantId,
            database_connection: await this.checkDatabaseConnection(tenantId),
            cache_connection: await this.checkCacheConnection(tenantId),
            api_response_time: await this.getAverageResponseTime(tenantId),
            error_rate: await this.getErrorRate(tenantId)
        };

        return health;
    }
}
```

### 8.2 告警机制
#### 8.2.1 告警规则
- **租户离线**: 租户长时间无活动
- **性能异常**: API响应时间超过阈值
- **错误率高**: 错误率超过设定阈值
- **资源超限**: 资源使用超过配额

#### 8.2.2 告警处理
```javascript
// 租户告警处理
class TenantAlertHandler {
    static async handleAlert(alertType, tenantId, details) {
        const alert = {
            type: alertType,
            tenant_id: tenantId,
            details: details,
            timestamp: new Date(),
            status: 'pending'
        };

        // 保存告警记录
        await db.tenant_alerts.create(alert);

        // 发送通知
        await this.sendAlertNotification(alert);

        // 自动处理（如果有规则）
        await this.autoHandleAlert(alert);
    }

    static async sendAlertNotification(alert) {
        // 发送邮件通知
        await emailService.sendAlert(alert);

        // 发送短信通知（紧急情况）
        if (alert.type === 'CRITICAL') {
            await smsService.sendAlert(alert);
        }

        // 发送到监控平台
        await monitoringService.sendAlert(alert);
    }
}
```

### 8.3 统计报表
#### 8.3.1 租户统计
- **租户概览**: 租户总数、状态分布、增长趋势
- **用户统计**: 各租户用户数量、活跃度统计
- **使用统计**: 功能使用情况、API调用统计
- **收入统计**: 各租户收入贡献、成本分析

#### 8.3.2 报表生成
```javascript
// 租户统计报表生成
class TenantReportGenerator {
    // 生成租户概览报表
    static async generateOverviewReport(startDate, endDate) {
        const report = {
            period: { start: startDate, end: endDate },
            total_tenants: await this.getTotalTenants(),
            active_tenants: await this.getActiveTenants(),
            new_tenants: await this.getNewTenants(startDate, endDate),
            tenant_distribution: await this.getTenantDistribution(),
            top_tenants: await this.getTopTenants(10)
        };

        return report;
    }

    // 生成租户详细报表
    static async generateDetailReport(tenantId, startDate, endDate) {
        const report = {
            tenant_id: tenantId,
            period: { start: startDate, end: endDate },
            user_stats: await this.getTenantUserStats(tenantId, startDate, endDate),
            api_stats: await this.getTenantApiStats(tenantId, startDate, endDate),
            performance_stats: await this.getTenantPerformanceStats(tenantId, startDate, endDate),
            revenue_stats: await this.getTenantRevenueStats(tenantId, startDate, endDate)
        };

        return report;
    }
}
```

---

## 🎯 总结

### 核心价值
租户ID作为AI生态平台多租户架构的核心，提供了：
- **完整的数据隔离**：确保租户间数据安全
- **灵活的配置管理**：支持租户个性化定制
- **强大的权限控制**：精细化的访问权限管理
- **全面的监控运维**：实时监控和告警机制

### 技术特点
- **简洁高效**：6位字符设计，易于使用和记忆
- **安全可靠**：多层次的安全保护机制
- **扩展性强**：支持大规模租户扩展
- **运维友好**：完善的监控和管理工具

### 应用场景
- **SAAS平台**：多租户SAAS应用的标准解决方案
- **企业服务**：企业级应用的租户管理
- **云服务**：云服务平台的租户隔离
- **电商平台**：多商户电商平台的商户管理

租户ID功能为AI生态平台提供了坚实的多租户基础，支撑平台的规模化运营和商业化发展。
