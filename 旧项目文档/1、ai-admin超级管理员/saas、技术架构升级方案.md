# 🏗️ AI生态平台技术架构升级方案

**文档版本**: v1.0  
**创建时间**: 2025-01-17  
**目标**: 将AI生态平台从当前架构升级为完整的SAAS多租户商业化架构  
**技术负责人**: AI生态平台开发团队  

---

## 📊 当前架构分析

### 现有技术栈
```
当前架构（第一阶段完成）：
├── 前端层: Vue.js 3.4 + TypeScript + Element Plus
├── 负载均衡: Nginx (8080端口)
├── 后端服务:
│   ├── ai-users (8002): 用户管理、认证授权
│   ├── ai-agents (8003): 智能体管理
│   ├── ai-tools (8005): 工具管理（基础框架）
│   └── ai-llms (8004): 大模型服务（基础框架）
├── 数据存储:
│   ├── PostgreSQL (5432): 主数据库
│   ├── Redis (6379): 缓存和会话
│   └── MongoDB (27017): 日志存储
└── 消息队列: EMQ X 5.10.0 (18083)
```

### 架构优势
- ✅ **微服务架构**: 服务解耦，独立部署
- ✅ **容器化部署**: Docker + Docker Compose
- ✅ **高性能**: Go语言，QPS可达25K+
- ✅ **基础功能完整**: 用户管理、智能体管理基本完成

### 架构不足
- ❌ **多租户隔离不完整**: 数据隔离机制不完善
- ❌ **权限控制粗糙**: 缺乏细粒度权限控制
- ❌ **大模型服务缺失**: ai-llms服务功能不完整
- ❌ **管理后台缺失**: 超级管理员功能缺失
- ❌ **监控体系不完善**: 缺乏完整的监控和告警

---

## 🎯 目标架构设计

### SAAS多租户架构
```
目标架构（SAAS多租户）：
├── 🌐 接入层
│   ├── CDN加速 (静态资源)
│   ├── 负载均衡 (Nginx + SSL)
│   └── 域名路由 (多域名支持)
├── 🔐 安全层
│   ├── 租户识别中间件
│   ├── 权限验证中间件
│   ├── 限流防护中间件
│   └── 安全审计中间件
├── 💼 业务层
│   ├── 用户服务 (ai-users)
│   ├── 智能体服务 (ai-agents)
│   ├── 大模型服务 (ai-llms) ⭐ 重点升级
│   ├── 工具服务 (ai-tools) ⭐ 重点升级
│   └── 管理服务 (ai-admin) ⭐ 新增服务
├── 📊 数据层
│   ├── 主数据库 (PostgreSQL + 租户隔离)
│   ├── 缓存层 (Redis Cluster)
│   ├── 日志存储 (MongoDB)
│   └── 文件存储 (阿里云OSS)
├── 📈 监控层
│   ├── 应用监控 (Prometheus + Grafana)
│   ├── 日志分析 (ELK Stack)
│   ├── 链路追踪 (Jaeger)
│   └── 告警通知 (AlertManager)
└── 🔧 运维层
    ├── 容器编排 (Docker Compose)
    ├── 配置管理 (Consul/Etcd)
    ├── 服务发现 (Consul)
    └── 健康检查 (自定义探针)
```

---

## 🔧 核心技术升级方案

### 1. 多租户数据隔离架构

#### 数据库隔离策略
```sql
-- 租户隔离实现方案
-- 方案1: 共享数据库 + 租户标识（推荐）
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,  -- 租户隔离字段
    username VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    -- 其他字段...
    INDEX idx_tenant_id (tenant_id)
);

-- 方案2: 租户配置表
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    config_category VARCHAR(50) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    UNIQUE(tenant_id, config_category, config_key)
);
```

#### 租户识别中间件
```go
// pkg/middleware/tenant.go
type TenantResolver struct {
    db    *gorm.DB
    cache *redis.Client
}

func (tr *TenantResolver) ResolveTenant(c *gin.Context) (string, error) {
    // 优先级1: 推广码识别
    if code := c.Query("ref"); code != "" {
        return tr.resolveTenantByReferralCode(code)
    }
    
    // 优先级2: 域名识别
    if host := c.Request.Host; host != "" {
        return tr.resolveTenantByDomain(host)
    }
    
    // 优先级3: Header识别
    if tenantID := c.GetHeader("X-Tenant-ID"); tenantID != "" {
        return tenantID, nil
    }
    
    // 优先级4: JWT Token识别
    if tenantID := tr.extractFromToken(c); tenantID != "" {
        return tenantID, nil
    }
    
    // 默认租户
    return tr.getDefaultTenant()
}
```

### 2. 大模型API中转架构升级

#### new-api集成架构
```go
// ai-llms/internal/newapi/service.go
type NewAPIService struct {
    config     *Config
    db         *gorm.DB
    redis      *redis.Client
    relay      *relay.Relay
    quota      *QuotaManager
    statistics *StatisticsManager
}

func (s *NewAPIService) Initialize() error {
    // 1. 初始化数据库连接
    if err := s.initDatabase(); err != nil {
        return err
    }
    
    // 2. 初始化Redis连接
    if err := s.initRedis(); err != nil {
        return err
    }
    
    // 3. 加载模型配置
    if err := s.loadModelConfigs(); err != nil {
        return err
    }
    
    // 4. 启动relay服务
    s.relay = relay.NewRelay(s.config)
    return s.relay.Start()
}

func (s *NewAPIService) ProcessRequest(ctx context.Context, req *APIRequest) (*APIResponse, error) {
    // 1. 租户验证
    tenantID := ctx.Value("tenant_id").(string)
    
    // 2. 配额检查
    if err := s.quota.CheckQuota(ctx, req.UserID, req.EstimatedTokens); err != nil {
        return nil, err
    }
    
    // 3. 模型选择
    model := s.selectOptimalModel(req.Model, tenantID)
    
    // 4. 请求转发
    resp, err := s.relay.Process(ctx, model, req)
    if err != nil {
        return nil, err
    }
    
    // 5. 配额消费
    s.quota.ConsumeQuota(ctx, req.UserID, resp.ActualTokens)
    
    // 6. 统计记录
    s.statistics.RecordUsage(ctx, tenantID, req.UserID, resp)
    
    return resp, nil
}
```

#### 配额管理系统
```go
// ai-llms/internal/quota/manager.go
type QuotaManager struct {
    db    *gorm.DB
    redis *redis.Client
}

type UserQuota struct {
    ID          string          `json:"id" gorm:"primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"index"`
    UserID      string          `json:"user_id" gorm:"index"`
    QuotaType   string          `json:"quota_type"` // tokens, requests, cost
    TotalQuota  decimal.Decimal `json:"total_quota"`
    UsedQuota   decimal.Decimal `json:"used_quota"`
    ResetPeriod string          `json:"reset_period"` // daily, monthly
    ExpiresAt   *time.Time      `json:"expires_at"`
}

func (qm *QuotaManager) CheckQuota(ctx context.Context, userID string, amount decimal.Decimal) error {
    tenantID := ctx.Value("tenant_id").(string)
    
    // 使用Redis分布式锁
    lockKey := fmt.Sprintf("quota_lock:%s:%s", tenantID, userID)
    lock := qm.redis.SetNX(ctx, lockKey, "1", time.Second*10)
    
    if !lock.Val() {
        return ErrQuotaLocked
    }
    defer qm.redis.Del(ctx, lockKey)
    
    // 检查配额
    quota, err := qm.getUserQuota(tenantID, userID)
    if err != nil {
        return err
    }
    
    if quota.UsedQuota.Add(amount).GreaterThan(quota.TotalQuota) {
        return ErrQuotaExceeded
    }
    
    return nil
}
```

### 3. 超级管理员后台架构

#### 管理服务架构
```go
// ai-admin/internal/service/tenant.go
type TenantService struct {
    db     *gorm.DB
    redis  *redis.Client
    config *config.Config
}

type TenantCreateRequest struct {
    Name        string                 `json:"name" binding:"required"`
    Domain      string                 `json:"domain"`
    AdminEmail  string                 `json:"admin_email" binding:"required,email"`
    Config      map[string]interface{} `json:"config"`
    PlanType    string                 `json:"plan_type"` // basic, pro, enterprise
}

func (s *TenantService) CreateTenant(ctx context.Context, req *TenantCreateRequest) (*TenantResponse, error) {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 验证域名唯一性
        if req.Domain != "" {
            var count int64
            tx.Model(&model.Tenant{}).Where("domain = ?", req.Domain).Count(&count)
            if count > 0 {
                return ErrDomainAlreadyExists
            }
        }
        
        // 2. 创建租户
        tenant := &model.Tenant{
            ID:     generateTenantID(),
            Name:   req.Name,
            Domain: req.Domain,
            Status: "active",
            Config: req.Config,
        }
        
        if err := tx.Create(tenant).Error; err != nil {
            return err
        }
        
        // 3. 创建租户管理员
        admin := &model.User{
            ID:       uuid.New().String(),
            TenantID: tenant.ID,
            Email:    req.AdminEmail,
            Role:     "tenant_admin",
            Status:   "active",
        }
        
        if err := tx.Create(admin).Error; err != nil {
            return err
        }
        
        // 4. 初始化租户配置
        return s.initTenantDefaults(tx, tenant.ID, req.PlanType)
    })
}
```

### 4. 前端多层级架构

#### 路由权限控制
```typescript
// frontend/src/router/permission.ts
export interface RoutePermission {
  roles: UserRole[]
  tenantRequired?: boolean
  superAdminOnly?: boolean
}

export function createPermissionGuard() {
  return async (to: RouteLocationNormalized) => {
    const userStore = useUserStore()
    const permission = to.meta.permission as RoutePermission
    
    if (!permission) return true
    
    // 检查登录状态
    if (!userStore.isLoggedIn) {
      return { name: 'Login', query: { redirect: to.fullPath } }
    }
    
    // 检查超级管理员权限
    if (permission.superAdminOnly && !userStore.isSuperAdmin) {
      return { name: 'Forbidden' }
    }
    
    // 检查租户权限
    if (permission.tenantRequired && !userStore.currentTenant) {
      return { name: 'TenantSelect' }
    }
    
    // 检查角色权限
    if (permission.roles.length > 0) {
      const hasPermission = permission.roles.some(role => 
        userStore.hasRole(role)
      )
      if (!hasPermission) {
        return { name: 'Forbidden' }
      }
    }
    
    return true
  }
}
```

#### 多租户状态管理
```typescript
// frontend/src/stores/tenant.ts
export const useTenantStore = defineStore('tenant', () => {
  const currentTenant = ref<Tenant | null>(null)
  const tenantList = ref<Tenant[]>([])
  
  // 切换租户
  async function switchTenant(tenantId: string) {
    const tenant = await tenantApi.getTenant(tenantId)
    currentTenant.value = tenant
    
    // 应用租户品牌样式
    applyTenantBranding(tenant.branding)
    
    // 更新本地存储
    localStorage.setItem('current_tenant', tenantId)
    
    return tenant
  }
  
  // 应用租户品牌样式
  function applyTenantBranding(branding: TenantBranding) {
    const root = document.documentElement
    root.style.setProperty('--primary-color', branding.primaryColor)
    root.style.setProperty('--secondary-color', branding.secondaryColor)
    
    // 应用自定义CSS
    if (branding.customCSS) {
      let styleElement = document.getElementById('tenant-custom-styles')
      if (!styleElement) {
        styleElement = document.createElement('style')
        styleElement.id = 'tenant-custom-styles'
        document.head.appendChild(styleElement)
      }
      styleElement.textContent = branding.customCSS
    }
  }
  
  return {
    currentTenant,
    tenantList,
    switchTenant,
    applyTenantBranding
  }
})
```

---

## 📊 性能优化方案

### 数据库优化
```sql
-- 1. 索引优化
CREATE INDEX CONCURRENTLY idx_users_tenant_status ON users(tenant_id, status);
CREATE INDEX CONCURRENTLY idx_agents_tenant_category ON agents(tenant_id, category_id);

-- 2. 分区表设计（大数据量场景）
CREATE TABLE api_usage_logs (
    id BIGSERIAL,
    tenant_id VARCHAR(50),
    user_id VARCHAR(50),
    created_at TIMESTAMP,
    -- 其他字段...
) PARTITION BY RANGE (created_at);

-- 3. 读写分离配置
-- 主库：写操作
-- 从库：读操作、报表查询
```

### 缓存策略
```go
// pkg/cache/strategy.go
type CacheStrategy struct {
    redis *redis.Client
}

// 多级缓存策略
func (cs *CacheStrategy) GetWithFallback(key string, fallback func() (interface{}, error)) (interface{}, error) {
    // L1: 本地缓存（内存）
    if value, ok := cs.localCache.Get(key); ok {
        return value, nil
    }
    
    // L2: Redis缓存
    if value, err := cs.redis.Get(context.Background(), key).Result(); err == nil {
        cs.localCache.Set(key, value, time.Minute*5)
        return value, nil
    }
    
    // L3: 数据库查询
    value, err := fallback()
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    cs.redis.Set(context.Background(), key, value, time.Hour)
    cs.localCache.Set(key, value, time.Minute*5)
    
    return value, nil
}
```

---

## 🔒 安全加固方案

### 安全中间件
```go
// pkg/middleware/security.go
func SecurityMiddleware() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        // 1. CORS配置
        c.Header("Access-Control-Allow-Origin", getAllowedOrigins())
        
        // 2. 安全头设置
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        
        // 3. 限流控制
        if !rateLimiter.Allow(c.ClientIP()) {
            c.JSON(429, gin.H{"error": "Too many requests"})
            c.Abort()
            return
        }
        
        // 4. IP白名单检查（超级管理员）
        if isAdminRoute(c.Request.URL.Path) {
            if !isAllowedIP(c.ClientIP()) {
                c.JSON(403, gin.H{"error": "Access denied"})
                c.Abort()
                return
            }
        }
        
        c.Next()
    })
}
```

### 数据加密
```go
// pkg/crypto/encryption.go
type EncryptionService struct {
    key []byte
}

func (es *EncryptionService) EncryptSensitiveData(data string) (string, error) {
    // AES-256-GCM加密
    block, err := aes.NewCipher(es.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, []byte(data), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}
```

---

**文档完成时间**: 2025-01-17  
**技术架构版本**: v2.0 (SAAS多租户)  
**实施周期**: 16周  
**技术负责人**: AI生态平台开发团队
