# 超级管理员后台集成接口设计

**文档版本**: v1.0  
**创建时间**: 2025-07-18  
**负责人**: AI开发团队  
**目标**: 为大模型API中转系统设计超级管理员后台集成接口  

---

## 📋 文档概述

本文档详细设计AI生态平台SAAS超级管理员后台与大模型API中转系统的集成接口，包括全平台模型管理、租户模型权限管理、全平台统计概览等功能，为超级管理员提供完整的平台管理能力。

## 🎯 设计目标

1. **全平台管理**: 提供超级管理员对全平台资源的管理能力
2. **多租户管理**: 支持对多个租户的统一管理和配置
3. **数据可视化**: 提供全平台数据的可视化展示
4. **安全可靠**: 确保超级管理员接口的安全性和可靠性
5. **易于集成**: 设计易于与超级管理员后台集成的接口

## 🔍 核心概念

### 管理权限级别

| 权限级别 | 描述 | 适用范围 |
|---------|------|----------|
| `SUPER_ADMIN` | 超级管理员权限 | 全平台管理 |
| `PLATFORM_ADMIN` | 平台管理员权限 | 平台级配置管理 |
| `TENANT_ADMIN` | 租户管理员权限 | 单个租户管理 |
| `USER_ADMIN` | 用户管理员权限 | 用户管理 |

### 管理功能模块

| 功能模块 | 描述 | 权限要求 |
|---------|------|----------|
| `MODEL_MANAGEMENT` | 模型管理 | SUPER_ADMIN, PLATFORM_ADMIN |
| `TENANT_MANAGEMENT` | 租户管理 | SUPER_ADMIN, PLATFORM_ADMIN |
| `QUOTA_MANAGEMENT` | 配额管理 | SUPER_ADMIN, PLATFORM_ADMIN, TENANT_ADMIN |
| `STATS_DASHBOARD` | 统计仪表板 | SUPER_ADMIN, PLATFORM_ADMIN, TENANT_ADMIN |
| `USER_MANAGEMENT` | 用户管理 | SUPER_ADMIN, PLATFORM_ADMIN, TENANT_ADMIN |
| `BILLING_MANAGEMENT` | 计费管理 | SUPER_ADMIN, PLATFORM_ADMIN |

## 🛣️ API接口设计

### 1. 全平台模型管理接口

#### 1.1 获取全平台模型列表

```http
GET /api/v1/admin/models/global
```

**功能描述**: 获取全平台可用的模型列表

**请求参数**:
- `provider`: 模型提供商 (查询参数，可选)
- `status`: 状态筛选 (查询参数，可选)
- `type`: 模型类型 (查询参数，可选)
- `page`: 页码 (查询参数，可选，默认1)
- `size`: 每页记录数 (查询参数，可选，默认20)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "model_gpt4",
        "name": "GPT-4",
        "provider": "openai",
        "type": "chat",
        "description": "OpenAI最新的大型语言模型",
        "capabilities": [
          "文本生成",
          "对话聊天",
          "代码生成",
          "文档分析"
        ],
        "pricing": {
          "input_price": 0.03,
          "output_price": 0.06,
          "unit": "1K tokens"
        },
        "limits": {
          "max_tokens": 8192,
          "context_length": 32768,
          "rate_limit": 3500
        },
        "status": "active",
        "created_at": "2025-07-14T10:00:00Z",
        "tenant_access_count": 15
      }
    ],
    "total": 25,
    "page": 1,
    "size": 20
  }
}
```

#### 1.2 创建/更新全平台模型

```http
POST /api/v1/admin/models/global
```

**功能描述**: 创建新的全平台模型

**请求体**:
```json
{
  "name": "Claude 3 Opus",
  "provider": "anthropic",
  "type": "chat",
  "description": "Anthropic最新的大型语言模型",
  "capabilities": [
    "文本生成",
    "对话聊天",
    "代码生成",
    "文档分析"
  ],
  "pricing": {
    "input_price": 0.015,
    "output_price": 0.075,
    "unit": "1K tokens"
  },
  "limits": {
    "max_tokens": 32768,
    "context_length": 200000,
    "rate_limit": 5000
  },
  "status": "active"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "模型创建成功",
  "data": {
    "id": "model_claude3opus",
    "name": "Claude 3 Opus",
    "provider": "anthropic",
    "created_at": "2025-07-18T15:30:00Z"
  }
}
```

#### 1.3 获取模型详情

```http
GET /api/v1/admin/models/global/{model_id}
```

**功能描述**: 获取指定模型的详细信息

**请求参数**:
- `model_id`: 模型ID (路径参数)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "model_claude3opus",
    "name": "Claude 3 Opus",
    "provider": "anthropic",
    "type": "chat",
    "description": "Anthropic最新的大型语言模型",
    "capabilities": [
      "文本生成",
      "对话聊天",
      "代码生成",
      "文档分析"
    ],
    "pricing": {
      "input_price": 0.015,
      "output_price": 0.075,
      "unit": "1K tokens"
    },
    "limits": {
      "max_tokens": 32768,
      "context_length": 200000,
      "rate_limit": 5000
    },
    "status": "active",
    "created_at": "2025-07-18T15:30:00Z",
    "updated_at": "2025-07-18T15:30:00Z",
    "tenant_access": [
      {
        "tenant_id": "tenant-001",
        "tenant_name": "企业客户A",
        "access_level": "full",
        "usage_count": 1250
      },
      {
        "tenant_id": "tenant-002",
        "tenant_name": "企业客户B",
        "access_level": "limited",
        "usage_count": 850
      }
    ]
  }
}
```

#### 1.4 更新模型状态

```http
PUT /api/v1/admin/models/global/{model_id}/status
```

**功能描述**: 更新指定模型的状态

**请求参数**:
- `model_id`: 模型ID (路径参数)

**请求体**:
```json
{
  "status": "maintenance",
  "reason": "系统升级维护",
  "estimated_recovery_time": "2025-07-19T10:00:00Z"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "模型状态已更新",
  "data": {
    "id": "model_claude3opus",
    "status": "maintenance",
    "updated_at": "2025-07-18T16:00:00Z"
  }
}
```

### 2. 租户模型权限管理接口

#### 2.1 获取租户模型权限列表

```http
GET /api/v1/admin/tenants/{tenant_id}/models/permissions
```

**功能描述**: 获取指定租户的模型访问权限

**请求参数**:
- `tenant_id`: 租户ID (路径参数)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant-001",
    "tenant_name": "企业客户A",
    "model_permissions": [
      {
        "model_id": "model_gpt4",
        "model_name": "GPT-4",
        "access_level": "full",
        "max_tokens_per_request": 8192,
        "rate_limit_per_minute": 100,
        "is_active": true,
        "created_at": "2025-07-01T00:00:00Z"
      },
      {
        "model_id": "model_claude3opus",
        "model_name": "Claude 3 Opus",
        "access_level": "limited",
        "max_tokens_per_request": 4096,
        "rate_limit_per_minute": 50,
        "is_active": true,
        "created_at": "2025-07-18T15:30:00Z"
      }
    ]
  }
}
```

#### 2.2 设置租户模型权限

```http
PUT /api/v1/admin/tenants/{tenant_id}/models/permissions
```

**功能描述**: 设置租户的模型访问权限

**请求参数**:
- `tenant_id`: 租户ID (路径参数)

**请求体**:
```json
{
  "model_permissions": [
    {
      "model_id": "model_gpt4",
      "access_level": "full",
      "max_tokens_per_request": 8192,
      "rate_limit_per_minute": 100,
      "is_active": true
    },
    {
      "model_id": "model_claude3opus",
      "access_level": "limited",
      "max_tokens_per_request": 4096,
      "rate_limit_per_minute": 50,
      "is_active": true
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "租户模型权限设置成功",
  "data": {
    "updated_count": 2
  }
}
```

### 3. 全平台统计概览接口

#### 3.1 获取平台概览数据

```http
GET /api/v1/admin/stats/platform-overview
```

**功能描述**: 获取全平台统计概览数据

**请求参数**:
- `time_range`: 时间范围 (查询参数，可选，默认"this_month")

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tenant_stats": {
      "total_tenants": 25,
      "active_tenants": 18,
      "new_tenants_this_month": 3
    },
    "user_stats": {
      "total_users": 1250,
      "active_users": 850,
      "new_users_this_month": 120
    },
    "api_stats": {
      "total_api_calls": 1250000,
      "successful_calls": 1225000,
      "failed_calls": 25000,
      "average_response_time": 350
    },
    "token_stats": {
      "total_tokens_used": 75000000,
      "input_tokens": 25000000,
      "output_tokens": 50000000
    },
    "cost_stats": {
      "total_cost": 2500.50,
      "input_cost": 750.25,
      "output_cost": 1750.25,
      "currency": "USD"
    },
    "top_models": [
      {
        "model_id": "model_gpt4",
        "model_name": "GPT-4",
        "call_count": 500000,
        "token_count": 30000000,
        "cost": 1200.50
      },
      {
        "model_id": "model_claude3opus",
        "model_name": "Claude 3 Opus",
        "call_count": 300000,
        "token_count": 25000000,
        "cost": 800.75
      }
    ],
    "top_tenants": [
      {
        "tenant_id": "tenant-001",
        "tenant_name": "企业客户A",
        "call_count": 300000,
        "token_count": 20000000,
        "cost": 600.25
      },
      {
        "tenant_id": "tenant-002",
        "tenant_name": "企业客户B",
        "call_count": 250000,
        "token_count": 15000000,
        "cost": 450.50
      }
    ]
  }
}
```

#### 3.2 获取模型使用统计

```http
GET /api/v1/admin/stats/models-usage
```

**功能描述**: 获取模型使用统计数据

**请求参数**:
- `time_range`: 时间范围 (查询参数，可选，默认"this_month")
- `group_by`: 分组方式 (查询参数，可选，支持"day"、"tenant"、"provider")

**响应示例**:
```json
{
  "success": true,
  "data": {
    "time_range": "this_month",
    "group_by": "provider",
    "total_calls": 1250000,
    "total_tokens": 75000000,
    "total_cost": 2500.50,
    "provider_stats": [
      {
        "provider": "openai",
        "call_count": 700000,
        "token_count": 40000000,
        "cost": 1500.25,
        "models": [
          {
            "model_id": "model_gpt4",
            "model_name": "GPT-4",
            "call_count": 500000,
            "token_count": 30000000,
            "cost": 1200.50
          },
          {
            "model_id": "model_gpt35turbo",
            "model_name": "GPT-3.5 Turbo",
            "call_count": 200000,
            "token_count": 10000000,
            "cost": 300.75
          }
        ]
      },
      {
        "provider": "anthropic",
        "call_count": 550000,
        "token_count": 35000000,
        "cost": 1000.25,
        "models": [
          {
            "model_id": "model_claude3opus",
            "model_name": "Claude 3 Opus",
            "call_count": 300000,
            "token_count": 25000000,
            "cost": 800.75
          },
          {
            "model_id": "model_claude3sonnet",
            "model_name": "Claude 3 Sonnet",
            "call_count": 250000,
            "token_count": 10000000,
            "cost": 200.50
          }
        ]
      }
    ]
  }
}
```

#### 3.3 获取租户使用排名

```http
GET /api/v1/admin/stats/tenant-rankings
```

**功能描述**: 获取租户使用排名数据

**请求参数**:
- `time_range`: 时间范围 (查询参数，可选，默认"this_month")
- `sort_by`: 排序字段 (查询参数，可选，支持"calls"、"tokens"、"cost")
- `limit`: 返回数量 (查询参数，可选，默认10)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "time_range": "this_month",
    "sort_by": "cost",
    "rankings": [
      {
        "rank": 1,
        "tenant_id": "tenant-001",
        "tenant_name": "企业客户A",
        "call_count": 300000,
        "token_count": 20000000,
        "cost": 600.25,
        "user_count": 150,
        "active_user_count": 120
      },
      {
        "rank": 2,
        "tenant_id": "tenant-002",
        "tenant_name": "企业客户B",
        "call_count": 250000,
        "token_count": 15000000,
        "cost": 450.50,
        "user_count": 100,
        "active_user_count": 85
      }
    ]
  }
}
```

### 4. 系统健康监控接口

#### 4.1 获取系统健康状态

```http
GET /api/v1/admin/system/health
```

**功能描述**: 获取系统健康状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "components": {
      "database": {
        "status": "healthy",
        "latency": 5,
        "connections": 25
      },
      "redis": {
        "status": "healthy",
        "latency": 2,
        "memory_usage": "45%"
      },
      "api_services": {
        "status": "healthy",
        "response_time": 120
      },
      "model_providers": [
        {
          "provider": "openai",
          "status": "healthy",
          "latency": 350
        },
        {
          "provider": "anthropic",
          "status": "healthy",
          "latency": 380
        }
      ]
    },
    "metrics": {
      "cpu_usage": "35%",
      "memory_usage": "42%",
      "disk_usage": "38%",
      "requests_per_second": 85,
      "average_response_time": 320
    },
    "alerts": [
      {
        "level": "info",
        "message": "系统运行正常",
        "timestamp": "2025-07-18T16:30:00Z"
      }
    ]
  }
}
```

## 🔒 安全设计

### 1. 认证与授权

```go
// 超级管理员认证中间件
func SuperAdminAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取认证令牌
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(401, gin.H{"error": "未提供认证令牌"})
            c.Abort()
            return
        }
        
        // 验证令牌
        claims, err := verifyAdminToken(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "认证令牌无效"})
            c.Abort()
            return
        }
        
        // 检查超级管理员权限
        if !hasRole(claims, "SUPER_ADMIN") {
            c.JSON(403, gin.H{"error": "权限不足"})
            c.Abort()
            return
        }
        
        // 设置用户信息
        c.Set("admin_id", claims.UserID)
        c.Set("admin_roles", claims.Roles)
        
        c.Next()
    }
}
```

### 2. 操作审计

```go
// 操作审计中间件
func AdminAuditMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 记录请求开始时间
        startTime := time.Now()
        
        // 获取管理员信息
        adminID, _ := c.Get("admin_id")
        
        // 记录请求信息
        requestInfo := &models.AdminAuditLog{
            AdminID:    adminID.(string),
            Method:     c.Request.Method,
            Path:       c.Request.URL.Path,
            IP:         c.ClientIP(),
            UserAgent:  c.Request.UserAgent(),
            RequestAt:  startTime,
        }
        
        // 使用自定义ResponseWriter记录响应
        blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
        c.Writer = blw
        
        // 处理请求
        c.Next()
        
        // 记录响应信息
        requestInfo.StatusCode = c.Writer.Status()
        requestInfo.ResponseTime = time.Since(startTime).Milliseconds()
        
        // 保存审计日志
        saveAdminAuditLog(requestInfo)
    }
}
```

## 🔧 实现要点

### 1. 数据聚合与缓存

```go
// 数据聚合与缓存
func (s *StatsService) GetPlatformOverview(timeRange string) (*PlatformOverview, error) {
    // 尝试从缓存获取
    cacheKey := fmt.Sprintf("platform:overview:%s", timeRange)
    cachedData, err := s.cache.Get(cacheKey)
    if err == nil {
        var overview PlatformOverview
        if err := json.Unmarshal([]byte(cachedData), &overview); err == nil {
            return &overview, nil
        }
    }
    
    // 缓存未命中，从数据库聚合
    overview, err := s.aggregatePlatformOverview(timeRange)
    if err != nil {
        return nil, err
    }
    
    // 缓存结果
    data, _ := json.Marshal(overview)
    s.cache.Set(cacheKey, string(data), 5*time.Minute)
    
    return overview, nil
}
```

### 2. 分布式锁保护

```go
// 分布式锁保护敏感操作
func (s *ModelService) UpdateModelStatus(modelID string, status string) error {
    // 创建分布式锁
    lockKey := fmt.Sprintf("lock:model:%s", modelID)
    lock, err := s.redisLock.Obtain(lockKey, 10*time.Second, nil)
    if err != nil {
        return fmt.Errorf("无法获取锁: %v", err)
    }
    defer lock.Release()
    
    // 执行状态更新
    model, err := s.repo.GetModelByID(modelID)
    if err != nil {
        return err
    }
    
    model.Status = status
    model.UpdatedAt = time.Now()
    
    return s.repo.UpdateModel(model)
}
```

---

**文档状态**: 待与另一团队确认  
**下一步**: 根据反馈调整接口设计，开始基础预留实现
