# 授权验证系统技术实现文档

## 🏗️ 系统架构设计

### 1. 整体架构
```
客户端应用 ←→ 授权验证模块 ←→ 许可证服务器
     ↓              ↓              ↓
  功能限制      本地验证        远程验证
     ↓              ↓              ↓
  用户界面      数字签名        数据库
```

### 2. 核心组件
- **许可证管理器** (License Manager)
- **签名验证器** (Signature Validator)
- **功能控制器** (Feature Controller)
- **网络验证器** (Network Validator)
- **统计上报器** (Usage Reporter)

## 💻 Go语言实现

### 1. 许可证结构定义

```go
package license

import (
    "crypto/rsa"
    "crypto/sha256"
    "encoding/json"
    "time"
)

// License 许可证结构
type License struct {
    LicenseID    string `json:"license_id"`
    LicenseType  string `json:"license_type"`
    CustomerInfo struct {
        CompanyName       string   `json:"company_name"`
        ContactEmail      string   `json:"contact_email"`
        AuthorizedDomains []string `json:"authorized_domains"`
    } `json:"customer_info"`
    Authorization struct {
        MaxUsers       int      `json:"max_users"`
        MaxAgents      int      `json:"max_agents"`
        Features       []string `json:"features"`
        DeploymentType string   `json:"deployment_type"`
    } `json:"authorization"`
    Validity struct {
        IssuedDate  time.Time `json:"issued_date"`
        ExpiryDate  time.Time `json:"expiry_date"`
        GracePeriod int       `json:"grace_period"`
    } `json:"validity"`
    Signature string `json:"signature"`
    Checksum  string `json:"checksum"`
}

// LicenseManager 许可证管理器
type LicenseManager struct {
    publicKey *rsa.PublicKey
    serverURL string
    cache     map[string]*License
}

// NewLicenseManager 创建许可证管理器
func NewLicenseManager(publicKey *rsa.PublicKey, serverURL string) *LicenseManager {
    return &LicenseManager{
        publicKey: publicKey,
        serverURL: serverURL,
        cache:     make(map[string]*License),
    }
}
```

### 2. 许可证验证逻辑

```go
// ValidateLicense 验证许可证
func (lm *LicenseManager) ValidateLicense(licenseData []byte) (*License, error) {
    // 1. 解析许可证
    var license License
    if err := json.Unmarshal(licenseData, &license); err != nil {
        return nil, fmt.Errorf("failed to parse license: %w", err)
    }
    
    // 2. 验证数字签名
    if !lm.verifySignature(&license) {
        return nil, errors.New("invalid license signature")
    }
    
    // 3. 检查有效期
    now := time.Now()
    if now.Before(license.Validity.IssuedDate) {
        return nil, errors.New("license not yet valid")
    }
    if now.After(license.Validity.ExpiryDate) {
        return nil, errors.New("license expired")
    }
    
    // 4. 验证校验和
    if !lm.verifyChecksum(&license) {
        return nil, errors.New("license checksum mismatch")
    }
    
    // 5. 联网验证（可选）
    if err := lm.remoteValidation(&license); err != nil {
        logrus.Warnf("Remote validation failed: %v", err)
        // 可以选择是否允许离线使用
    }
    
    // 6. 缓存有效许可证
    lm.cache[license.LicenseID] = &license
    
    return &license, nil
}

// verifySignature 验证数字签名
func (lm *LicenseManager) verifySignature(license *License) bool {
    // 构建待签名数据
    data := fmt.Sprintf("%s:%s:%s:%s", 
        license.LicenseID,
        license.LicenseType,
        license.CustomerInfo.CompanyName,
        license.Validity.ExpiryDate.Format(time.RFC3339))
    
    // 计算哈希
    hash := sha256.Sum256([]byte(data))
    
    // 验证签名
    signature, err := base64.StdEncoding.DecodeString(license.Signature)
    if err != nil {
        return false
    }
    
    err = rsa.VerifyPKCS1v15(lm.publicKey, crypto.SHA256, hash[:], signature)
    return err == nil
}

// remoteValidation 远程验证
func (lm *LicenseManager) remoteValidation(license *License) error {
    client := &http.Client{Timeout: 10 * time.Second}
    
    payload := map[string]interface{}{
        "license_id": license.LicenseID,
        "timestamp":  time.Now().Unix(),
        "hostname":   getHostname(),
    }
    
    jsonData, _ := json.Marshal(payload)
    resp, err := client.Post(lm.serverURL+"/validate", 
        "application/json", bytes.NewBuffer(jsonData))
    
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("validation failed with status: %d", resp.StatusCode)
    }
    
    return nil
}
```

### 3. 功能控制实现

```go
// FeatureController 功能控制器
type FeatureController struct {
    license *License
    stats   *UsageStats
}

// IsFeatureEnabled 检查功能是否启用
func (fc *FeatureController) IsFeatureEnabled(feature string) bool {
    if fc.license == nil {
        return false
    }
    
    for _, f := range fc.license.Authorization.Features {
        if f == feature {
            return true
        }
    }
    return false
}

// CheckUserLimit 检查用户数量限制
func (fc *FeatureController) CheckUserLimit(currentUsers int) error {
    if fc.license == nil {
        return errors.New("no valid license")
    }
    
    if currentUsers > fc.license.Authorization.MaxUsers {
        return fmt.Errorf("user limit exceeded: %d/%d", 
            currentUsers, fc.license.Authorization.MaxUsers)
    }
    
    return nil
}

// CheckAgentLimit 检查智能体数量限制
func (fc *FeatureController) CheckAgentLimit(currentAgents int) error {
    if fc.license == nil {
        return errors.New("no valid license")
    }
    
    if currentAgents > fc.license.Authorization.MaxAgents {
        return fmt.Errorf("agent limit exceeded: %d/%d", 
            currentAgents, fc.license.Authorization.MaxAgents)
    }
    
    return nil
}
```

## 🌐 前端实现

### 1. TypeScript接口定义

```typescript
interface LicenseInfo {
    licenseId: string;
    licenseType: 'saas' | 'deployment' | 'distribution' | 'source' | 'perpetual' | 'buyout';
    customerInfo: {
        companyName: string;
        contactEmail: string;
        authorizedDomains: string[];
    };
    authorization: {
        maxUsers: number;
        maxAgents: number;
        features: string[];
        deploymentType: string;
    };
    validity: {
        issuedDate: string;
        expiryDate: string;
        gracePeriod: number;
    };
    isValid: boolean;
    daysRemaining: number;
}

interface UsageStats {
    currentUsers: number;
    currentAgents: number;
    apiCalls: number;
    storageUsed: number;
}
```

### 2. 许可证验证服务

```typescript
class LicenseService {
    private licenseInfo: LicenseInfo | null = null;
    private validationInterval: number | null = null;

    async initialize(): Promise<void> {
        try {
            this.licenseInfo = await this.validateLicense();
            this.startPeriodicValidation();
            this.applyLicenseRestrictions();
        } catch (error) {
            this.handleLicenseError(error);
        }
    }

    private async validateLicense(): Promise<LicenseInfo> {
        const response = await fetch('/api/license/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: window.location.hostname,
                timestamp: Date.now(),
                userAgent: navigator.userAgent
            })
        });

        if (!response.ok) {
            throw new Error(`License validation failed: ${response.status}`);
        }

        return await response.json();
    }

    private applyLicenseRestrictions(): void {
        if (!this.licenseInfo) return;

        // 根据许可证类型应用限制
        this.limitUserInterface();
        this.limitFeatureAccess();
        this.showLicenseStatus();
    }

    private limitUserInterface(): void {
        const license = this.licenseInfo!;
        
        // 隐藏未授权功能
        if (!license.authorization.features.includes('advanced')) {
            document.querySelectorAll('[data-feature="advanced"]')
                .forEach(el => (el as HTMLElement).style.display = 'none');
        }

        // 显示用户限制警告
        if (license.authorization.maxUsers > 0) {
            this.showUserLimitWarning();
        }
    }

    private startPeriodicValidation(): void {
        // 每小时验证一次
        this.validationInterval = window.setInterval(async () => {
            try {
                await this.validateLicense();
            } catch (error) {
                console.error('Periodic license validation failed:', error);
                this.handleLicenseError(error);
            }
        }, 3600000);
    }

    private handleLicenseError(error: any): void {
        console.error('License error:', error);
        
        // 显示许可证错误页面
        this.showLicenseErrorPage(error.message);
        
        // 禁用核心功能
        this.disableCoreFeatures();
    }

    getLicenseInfo(): LicenseInfo | null {
        return this.licenseInfo;
    }

    isFeatureEnabled(feature: string): boolean {
        return this.licenseInfo?.authorization.features.includes(feature) ?? false;
    }
}
```

## 🔐 安全措施

### 1. 代码混淆
```bash
# Go代码混淆
go build -ldflags="-s -w" -gcflags="all=-trimpath" .

# JavaScript代码混淆
npx terser --compress --mangle --output dist/app.min.js src/app.js
```

### 2. 反调试检测
```go
func detectDebugger() bool {
    // 检测调试器附加
    if runtime.GOOS == "windows" {
        return isDebuggerPresent()
    }
    
    // 检测ptrace
    return isPtraceDetected()
}

func antiTamper() {
    // 检查二进制文件完整性
    if !verifyBinaryIntegrity() {
        os.Exit(1)
    }
    
    // 检查运行环境
    if isVirtualMachine() {
        os.Exit(1)
    }
}
```

### 3. 网络通信加密
```go
func encryptLicenseData(data []byte, key []byte) ([]byte, error) {
    block, err := aes.NewCipher(key)
    if err != nil {
        return nil, err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return nil, err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return nil, err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, data, nil)
    return ciphertext, nil
}
```

## 📊 使用统计监控

### 1. 统计数据收集
```go
type UsageReporter struct {
    licenseID string
    endpoint  string
    stats     *UsageStats
}

func (ur *UsageReporter) ReportUsage() error {
    data := map[string]interface{}{
        "license_id":     ur.licenseID,
        "timestamp":      time.Now().Unix(),
        "current_users":  ur.stats.CurrentUsers,
        "current_agents": ur.stats.CurrentAgents,
        "api_calls":      ur.stats.APICalls,
        "storage_used":   ur.stats.StorageUsed,
    }
    
    return ur.sendReport(data)
}
```

### 2. 异常行为检测
```go
func (ur *UsageReporter) DetectAnomalies() []string {
    var anomalies []string
    
    // 检测用户数量异常增长
    if ur.stats.CurrentUsers > ur.stats.PreviousUsers*2 {
        anomalies = append(anomalies, "unusual_user_growth")
    }
    
    // 检测API调用异常
    if ur.stats.APICalls > ur.stats.ExpectedAPICalls*5 {
        anomalies = append(anomalies, "excessive_api_usage")
    }
    
    return anomalies
}
```

## 🚀 部署指南

### 1. 许可证服务器部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  license-server:
    image: license-server:latest
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - RSA_PRIVATE_KEY_PATH=/keys/private.pem
    volumes:
      - ./keys:/keys:ro
```

### 2. 客户端集成
```go
// main.go
func main() {
    // 初始化许可证管理器
    publicKey := loadPublicKey("public.pem")
    licenseManager := license.NewLicenseManager(publicKey, "https://license.yourcompany.com")
    
    // 验证许可证
    licenseData := loadLicenseFile("license.json")
    validLicense, err := licenseManager.ValidateLicense(licenseData)
    if err != nil {
        log.Fatalf("License validation failed: %v", err)
    }
    
    // 初始化功能控制器
    featureController := license.NewFeatureController(validLicense)
    
    // 启动应用
    startApplication(featureController)
}
```

---

**文档版本**: v1.0  
**创建时间**: 2025年7月20日  
**技术栈**: Go + TypeScript + RSA + AES
