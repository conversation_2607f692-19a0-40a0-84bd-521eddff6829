# 🛠️ AI生态平台技术实施方案详细设计

**文档版本**: v1.0  
**创建时间**: 2025-01-17  
**基于分析**: 功能缺失清单与实施建议  
**目标**: 提供详细的技术实施方案和代码架构设计  

---

## 🎯 核心技术架构升级方案

### 1. 多租户数据隔离架构设计

#### 数据库层面改造
```sql
-- 1. 为所有核心表添加租户字段
ALTER TABLE users ADD COLUMN tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE agents ADD COLUMN tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE orders ADD COLUMN tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE user_memberships ADD COLUMN tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';

-- 2. 创建租户管理表
CREATE TABLE tenants (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(100) UNIQUE,
    status VARCHAR(20) DEFAULT 'active',
    config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建租户配置表
CREATE TABLE tenant_configs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) REFERENCES tenants(id),
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, config_key)
);

-- 4. 添加索引优化查询性能
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_agents_tenant_id ON agents(tenant_id);
CREATE INDEX idx_orders_tenant_id ON orders(tenant_id);
```

#### 后端中间件实现
```go
// pkg/middleware/tenant.go
package middleware

import (
    "context"
    "strings"
    "github.com/gin-gonic/gin"
)

// TenantMiddleware 租户识别中间件
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := extractTenantID(c)
        
        // 验证租户是否存在和有效
        if !isValidTenant(tenantID) {
            c.JSON(403, gin.H{"error": "Invalid tenant"})
            c.Abort()
            return
        }
        
        // 将租户ID添加到上下文
        ctx := context.WithValue(c.Request.Context(), "tenant_id", tenantID)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}

// 从请求中提取租户ID
func extractTenantID(c *gin.Context) string {
    // 1. 从域名提取
    host := c.Request.Host
    if subdomain := extractSubdomain(host); subdomain != "" {
        return subdomain
    }
    
    // 2. 从Header提取
    if tenantID := c.GetHeader("X-Tenant-ID"); tenantID != "" {
        return tenantID
    }
    
    // 3. 从JWT Token提取
    if tenantID := extractFromToken(c); tenantID != "" {
        return tenantID
    }
    
    return "default"
}
```

#### 数据访问层改造
```go
// internal/repository/base.go
package repository

import (
    "context"
    "gorm.io/gorm"
)

type BaseRepository struct {
    db *gorm.DB
}

// WithTenant 自动添加租户过滤
func (r *BaseRepository) WithTenant(ctx context.Context) *gorm.DB {
    tenantID := ctx.Value("tenant_id").(string)
    return r.db.Where("tenant_id = ?", tenantID)
}

// 用户仓库示例
type UserRepository struct {
    BaseRepository
}

func (r *UserRepository) FindByID(ctx context.Context, id string) (*User, error) {
    var user User
    err := r.WithTenant(ctx).Where("id = ?", id).First(&user).Error
    return &user, err
}

func (r *UserRepository) Create(ctx context.Context, user *User) error {
    tenantID := ctx.Value("tenant_id").(string)
    user.TenantID = tenantID
    return r.db.Create(user).Error
}
```

### 2. 大模型API中转系统集成

#### new-api项目集成架构
```go
// ai-llms/internal/newapi/adapter.go
package newapi

import (
    "github.com/songquanpeng/one-api/relay"
    "github.com/songquanpeng/one-api/model"
)

// NewAPIAdapter new-api适配器
type NewAPIAdapter struct {
    config *Config
    relay  *relay.Relay
}

type Config struct {
    DatabaseURL string
    RedisURL    string
    APIKeys     map[string]string
    Models      []ModelConfig
}

type ModelConfig struct {
    Name     string `json:"name"`
    Provider string `json:"provider"`
    APIKey   string `json:"api_key"`
    BaseURL  string `json:"base_url"`
    Enabled  bool   `json:"enabled"`
}

// Initialize 初始化new-api引擎
func (a *NewAPIAdapter) Initialize() error {
    // 1. 初始化数据库连接
    if err := a.initDatabase(); err != nil {
        return err
    }
    
    // 2. 初始化Redis连接
    if err := a.initRedis(); err != nil {
        return err
    }
    
    // 3. 加载模型配置
    if err := a.loadModels(); err != nil {
        return err
    }
    
    // 4. 启动relay服务
    a.relay = relay.NewRelay(a.config)
    return a.relay.Start()
}

// ProcessRequest 处理API请求
func (a *NewAPIAdapter) ProcessRequest(ctx context.Context, req *APIRequest) (*APIResponse, error) {
    // 1. 验证用户权限和配额
    if err := a.validateQuota(ctx, req); err != nil {
        return nil, err
    }
    
    // 2. 选择最优模型
    model := a.selectModel(req.Model)
    
    // 3. 转发请求到对应提供商
    resp, err := a.relay.Process(ctx, model, req)
    if err != nil {
        return nil, err
    }
    
    // 4. 记录使用统计
    a.recordUsage(ctx, req, resp)
    
    return resp, nil
}
```

#### 配额管理系统
```go
// ai-llms/internal/quota/manager.go
package quota

import (
    "context"
    "time"
    "github.com/shopspring/decimal"
)

type QuotaManager struct {
    db    *gorm.DB
    redis *redis.Client
}

type UserQuota struct {
    ID          string          `json:"id" gorm:"primaryKey"`
    TenantID    string          `json:"tenant_id" gorm:"index"`
    UserID      string          `json:"user_id" gorm:"index"`
    QuotaType   string          `json:"quota_type"` // tokens, requests, cost
    TotalQuota  decimal.Decimal `json:"total_quota"`
    UsedQuota   decimal.Decimal `json:"used_quota"`
    ResetPeriod string          `json:"reset_period"` // daily, monthly, yearly
    ExpiresAt   *time.Time      `json:"expires_at"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
}

// CheckQuota 检查用户配额
func (qm *QuotaManager) CheckQuota(ctx context.Context, userID string, quotaType string, amount decimal.Decimal) error {
    tenantID := ctx.Value("tenant_id").(string)
    
    // 1. 获取用户配额
    quota, err := qm.getUserQuota(tenantID, userID, quotaType)
    if err != nil {
        return err
    }
    
    // 2. 检查是否超限
    if quota.UsedQuota.Add(amount).GreaterThan(quota.TotalQuota) {
        return ErrQuotaExceeded
    }
    
    return nil
}

// ConsumeQuota 消费配额
func (qm *QuotaManager) ConsumeQuota(ctx context.Context, userID string, quotaType string, amount decimal.Decimal) error {
    tenantID := ctx.Value("tenant_id").(string)
    
    // 使用Redis分布式锁防止并发问题
    lockKey := fmt.Sprintf("quota_lock:%s:%s:%s", tenantID, userID, quotaType)
    lock := qm.redis.SetNX(ctx, lockKey, "1", time.Second*10)
    
    if !lock.Val() {
        return ErrQuotaLocked
    }
    defer qm.redis.Del(ctx, lockKey)
    
    // 原子性更新配额
    return qm.db.Transaction(func(tx *gorm.DB) error {
        var quota UserQuota
        if err := tx.Where("tenant_id = ? AND user_id = ? AND quota_type = ?", 
            tenantID, userID, quotaType).First(&quota).Error; err != nil {
            return err
        }
        
        newUsed := quota.UsedQuota.Add(amount)
        if newUsed.GreaterThan(quota.TotalQuota) {
            return ErrQuotaExceeded
        }
        
        return tx.Model(&quota).Update("used_quota", newUsed).Error
    })
}
```

### 3. 平台超级管理员后台架构

#### 租户管理系统
```go
// ai-users/internal/service/tenant.go
package service

type TenantService struct {
    repo   *repository.TenantRepository
    config *config.Config
}

type TenantCreateRequest struct {
    Name        string                 `json:"name" binding:"required"`
    Domain      string                 `json:"domain"`
    AdminEmail  string                 `json:"admin_email" binding:"required,email"`
    Config      map[string]interface{} `json:"config"`
    PlanType    string                 `json:"plan_type"` // basic, pro, enterprise
}

type TenantResponse struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Domain      string                 `json:"domain"`
    Status      string                 `json:"status"`
    Config      map[string]interface{} `json:"config"`
    Statistics  *TenantStatistics      `json:"statistics"`
    CreatedAt   time.Time              `json:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at"`
}

type TenantStatistics struct {
    UserCount    int             `json:"user_count"`
    AgentCount   int             `json:"agent_count"`
    APIUsage     decimal.Decimal `json:"api_usage"`
    Revenue      decimal.Decimal `json:"revenue"`
    LastActivity time.Time       `json:"last_activity"`
}

// CreateTenant 创建租户
func (s *TenantService) CreateTenant(ctx context.Context, req *TenantCreateRequest) (*TenantResponse, error) {
    // 1. 验证域名唯一性
    if req.Domain != "" {
        if exists, err := s.repo.DomainExists(req.Domain); err != nil {
            return nil, err
        } else if exists {
            return nil, ErrDomainAlreadyExists
        }
    }
    
    // 2. 创建租户
    tenant := &model.Tenant{
        ID:     uuid.New().String(),
        Name:   req.Name,
        Domain: req.Domain,
        Status: "active",
        Config: req.Config,
    }
    
    if err := s.repo.Create(ctx, tenant); err != nil {
        return nil, err
    }
    
    // 3. 创建租户管理员账户
    if err := s.createTenantAdmin(ctx, tenant.ID, req.AdminEmail); err != nil {
        // 回滚租户创建
        s.repo.Delete(ctx, tenant.ID)
        return nil, err
    }
    
    // 4. 初始化租户默认配置
    if err := s.initTenantDefaults(ctx, tenant.ID, req.PlanType); err != nil {
        return nil, err
    }
    
    return s.buildTenantResponse(ctx, tenant), nil
}

// GetTenantStatistics 获取租户统计信息
func (s *TenantService) GetTenantStatistics(ctx context.Context, tenantID string) (*TenantStatistics, error) {
    stats := &TenantStatistics{}
    
    // 并发获取各项统计数据
    var wg sync.WaitGroup
    var mu sync.Mutex
    var errs []error
    
    wg.Add(4)
    
    // 用户数量
    go func() {
        defer wg.Done()
        count, err := s.repo.GetUserCount(tenantID)
        if err != nil {
            mu.Lock()
            errs = append(errs, err)
            mu.Unlock()
            return
        }
        stats.UserCount = count
    }()
    
    // 智能体数量
    go func() {
        defer wg.Done()
        count, err := s.repo.GetAgentCount(tenantID)
        if err != nil {
            mu.Lock()
            errs = append(errs, err)
            mu.Unlock()
            return
        }
        stats.AgentCount = count
    }()
    
    // API使用量
    go func() {
        defer wg.Done()
        usage, err := s.repo.GetAPIUsage(tenantID)
        if err != nil {
            mu.Lock()
            errs = append(errs, err)
            mu.Unlock()
            return
        }
        stats.APIUsage = usage
    }()
    
    // 收入统计
    go func() {
        defer wg.Done()
        revenue, err := s.repo.GetRevenue(tenantID)
        if err != nil {
            mu.Lock()
            errs = append(errs, err)
            mu.Unlock()
            return
        }
        stats.Revenue = revenue
    }()
    
    wg.Wait()
    
    if len(errs) > 0 {
        return nil, errs[0]
    }
    
    return stats, nil
}
```

### 4. 前端多层级架构实现

#### 路由权限控制
```typescript
// frontend/src/router/permission.ts
import { RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { UserRole } from '@/types/user'

export interface RoutePermission {
  roles: UserRole[]
  tenantRequired?: boolean
  superAdminOnly?: boolean
}

// 权限验证守卫
export function createPermissionGuard() {
  return async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const userStore = useUserStore()
    const permission = to.meta.permission as RoutePermission
    
    if (!permission) {
      return true // 无权限要求的路由
    }
    
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      return { name: 'Login', query: { redirect: to.fullPath } }
    }
    
    // 检查超级管理员权限
    if (permission.superAdminOnly && !userStore.isSuperAdmin) {
      return { name: 'Forbidden' }
    }
    
    // 检查租户权限
    if (permission.tenantRequired && !userStore.currentTenant) {
      return { name: 'TenantSelect' }
    }
    
    // 检查角色权限
    if (permission.roles.length > 0) {
      const hasPermission = permission.roles.some(role => 
        userStore.hasRole(role)
      )
      
      if (!hasPermission) {
        return { name: 'Forbidden' }
      }
    }
    
    return true
  }
}
```

#### 多租户状态管理
```typescript
// frontend/src/stores/tenant.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { tenantApi } from '@/api/tenant'

export interface Tenant {
  id: string
  name: string
  domain: string
  status: string
  config: Record<string, any>
  branding: TenantBranding
}

export interface TenantBranding {
  logo: string
  primaryColor: string
  secondaryColor: string
  fontFamily: string
  customCSS: string
}

export const useTenantStore = defineStore('tenant', () => {
  const currentTenant = ref<Tenant | null>(null)
  const tenantList = ref<Tenant[]>([])
  const loading = ref(false)
  
  // 计算属性
  const tenantBranding = computed(() => currentTenant.value?.branding)
  const isTenantAdmin = computed(() => {
    // 检查当前用户是否为租户管理员
    return currentTenant.value && userStore.hasRole('tenant_admin')
  })
  
  // 切换租户
  async function switchTenant(tenantId: string) {
    loading.value = true
    try {
      const tenant = await tenantApi.getTenant(tenantId)
      currentTenant.value = tenant
      
      // 应用租户品牌样式
      applyTenantBranding(tenant.branding)
      
      // 更新本地存储
      localStorage.setItem('current_tenant', tenantId)
      
      return tenant
    } finally {
      loading.value = false
    }
  }
  
  // 应用租户品牌样式
  function applyTenantBranding(branding: TenantBranding) {
    if (!branding) return
    
    const root = document.documentElement
    
    // 设置CSS变量
    root.style.setProperty('--primary-color', branding.primaryColor)
    root.style.setProperty('--secondary-color', branding.secondaryColor)
    root.style.setProperty('--font-family', branding.fontFamily)
    
    // 应用自定义CSS
    if (branding.customCSS) {
      let styleElement = document.getElementById('tenant-custom-styles')
      if (!styleElement) {
        styleElement = document.createElement('style')
        styleElement.id = 'tenant-custom-styles'
        document.head.appendChild(styleElement)
      }
      styleElement.textContent = branding.customCSS
    }
    
    // 更新Logo
    const logoElements = document.querySelectorAll('.tenant-logo')
    logoElements.forEach(el => {
      if (el instanceof HTMLImageElement) {
        el.src = branding.logo
      }
    })
  }
  
  // 获取租户列表
  async function fetchTenantList() {
    loading.value = true
    try {
      tenantList.value = await tenantApi.getTenantList()
    } finally {
      loading.value = false
    }
  }
  
  return {
    currentTenant,
    tenantList,
    loading,
    tenantBranding,
    isTenantAdmin,
    switchTenant,
    applyTenantBranding,
    fetchTenantList
  }
})
```

---

## 📋 实施步骤详细规划

### 第一周：多租户基础架构
1. **数据库改造** (2天)
   - 添加租户字段到所有表
   - 创建租户管理相关表
   - 数据迁移脚本编写

2. **中间件开发** (2天)
   - 租户识别中间件
   - 权限验证增强
   - 审计日志系统

3. **基础测试** (1天)
   - 单元测试编写
   - 集成测试验证

### 第二周：大模型API集成
1. **new-api集成** (3天)
   - 源码集成和配置
   - 数据库适配
   - 服务启动优化

2. **配额系统** (2天)
   - 配额管理逻辑
   - 使用统计功能
   - 预警通知系统

### 第三周：超级管理员后台
1. **租户管理** (3天)
   - CRUD接口开发
   - 统计分析功能
   - 前端管理界面

2. **权限系统** (2天)
   - 角色权限配置
   - 动态权限分配
   - 权限继承机制

---

**文档完成时间**: 2025-01-17  
**技术架构**: 微服务 + 多租户 + 权限控制  
**实施周期**: 预计12-16周完成核心功能
