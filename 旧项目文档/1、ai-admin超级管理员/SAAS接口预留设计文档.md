# AI生态平台SAAS接口预留设计文档

**文档版本**: v1.0  
**创建时间**: 2025-07-18  
**负责人**: AI开发团队  
**目标**: 为大模型API中转系统预留SAAS多租户相关接口  

---

## 📋 文档概述

本文档基于对ai-llms服务代码结构的深入分析，为另一团队开发的大模型API中转系统预留SAAS多租户相关接口，确保系统架构的一致性和可扩展性。

## 🔍 ai-llms服务代码结构分析

### 当前架构概览

```
ai-llms/
├── internal/
│   ├── models/          # 数据模型层
│   │   ├── user.go      # 用户相关模型（已支持TenantID）
│   │   └── token.go     # Token模型（已支持TenantID）
│   ├── handlers/        # 处理器层
│   │   ├── handler.go
│   │   ├── token_handler.go
│   │   ├── permission_handler.go
│   │   └── quota_handler.go
│   ├── routes/          # 路由层
│   │   └── routes.go    # 已有完整的API路由结构
│   ├── middleware/      # 中间件层
│   ├── services/        # 服务层
│   └── repository/      # 数据访问层
├── config/
│   └── production.yaml  # 配置文件
└── migrations/          # 数据库迁移
```

### 核心发现

1. **✅ 多租户支持基础**：
   - User模型已包含`TenantID`字段
   - Token模型已包含`TenantID`字段
   - 数据库表结构已支持租户隔离

2. **✅ 完整的API路由结构**：
   - OpenAI兼容API路由（/api/v1/openai/*）
   - Token管理路由（/api/v1/tokens/*）
   - 模型管理路由（/api/v1/models/*）
   - 权限管理路由（/api/v1/permissions/*）

3. **✅ 权限和认证系统**：
   - 基于角色的权限控制（RBAC）
   - Token认证中间件
   - 模型权限中间件

## 🎯 SAAS接口预留设计

### 1. 租户级别配额管理接口

#### 1.1 租户配额概览
```http
GET /api/v1/admin/tenants/{tenant_id}/quota/overview
```

**功能描述**: 获取指定租户的配额使用概览

**预留参数**:
```json
{
  "tenant_id": "string",
  "total_quota": "number",
  "used_quota": "number", 
  "remaining_quota": "number",
  "quota_percentage": "number",
  "daily_usage": "array",
  "model_usage": "object"
}
```

#### 1.2 租户配额设置
```http
PUT /api/v1/admin/tenants/{tenant_id}/quota/settings
```

**功能描述**: 设置租户级别的配额限制

**预留参数**:
```json
{
  "monthly_quota": "number",
  "daily_quota": "number", 
  "model_quotas": {
    "gpt-4": "number",
    "claude-3": "number"
  },
  "rate_limits": {
    "requests_per_minute": "number",
    "requests_per_hour": "number"
  }
}
```

#### 1.3 租户配额历史
```http
GET /api/v1/admin/tenants/{tenant_id}/quota/history
```

**功能描述**: 获取租户配额使用历史记录

### 2. 租户级别API统计接口

#### 2.1 租户API调用统计
```http
GET /api/v1/admin/tenants/{tenant_id}/stats/api-calls
```

**功能描述**: 获取租户API调用统计数据

**预留参数**:
```json
{
  "time_range": "string",
  "total_calls": "number",
  "successful_calls": "number",
  "failed_calls": "number",
  "average_response_time": "number",
  "model_breakdown": "object",
  "hourly_distribution": "array"
}
```

#### 2.2 租户成本分析
```http
GET /api/v1/admin/tenants/{tenant_id}/stats/cost-analysis
```

**功能描述**: 获取租户成本分析数据

#### 2.3 租户用户活跃度
```http
GET /api/v1/admin/tenants/{tenant_id}/stats/user-activity
```

**功能描述**: 获取租户用户活跃度统计

### 3. 超级管理员后台集成接口

#### 3.1 全平台模型管理
```http
GET /api/v1/admin/models/global
POST /api/v1/admin/models/global
PUT /api/v1/admin/models/global/{model_id}
DELETE /api/v1/admin/models/global/{model_id}
```

**功能描述**: 超级管理员管理全平台可用模型

#### 3.2 租户模型权限管理
```http
GET /api/v1/admin/tenants/{tenant_id}/models/permissions
PUT /api/v1/admin/tenants/{tenant_id}/models/permissions
```

**功能描述**: 管理租户对特定模型的访问权限

#### 3.3 全平台统计概览
```http
GET /api/v1/admin/stats/platform-overview
```

**功能描述**: 获取全平台统计概览数据

**预留响应**:
```json
{
  "total_tenants": "number",
  "active_tenants": "number", 
  "total_api_calls": "number",
  "total_cost": "number",
  "top_models": "array",
  "tenant_rankings": "array"
}
```

## 🔧 技术实现要点

### 1. 数据库表结构扩展

基于现有模型，需要扩展以下表结构：

```sql
-- 租户配额配置表
CREATE TABLE llmsapi_tenant_quotas (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    quota_type VARCHAR(50) NOT NULL,
    quota_limit BIGINT DEFAULT 0,
    quota_used BIGINT DEFAULT 0,
    reset_period VARCHAR(20) DEFAULT 'monthly',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 租户API调用日志表
CREATE TABLE llmsapi_tenant_api_logs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    request_tokens INTEGER DEFAULT 0,
    response_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    cost DECIMAL(10,6) DEFAULT 0,
    response_time INTEGER DEFAULT 0,
    status_code INTEGER DEFAULT 200,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 中间件扩展

需要扩展现有中间件以支持租户级别的限制：

```go
// 租户识别中间件
func TenantIdentificationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从域名、Header、JWT等多种方式识别租户
        tenantID := extractTenantID(c)
        c.Set("tenant_id", tenantID)
        c.Next()
    }
}

// 租户配额检查中间件  
func TenantQuotaMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := c.GetString("tenant_id")
        if !checkTenantQuota(tenantID) {
            c.JSON(429, gin.H{"error": "Tenant quota exceeded"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

### 3. 服务层接口设计

```go
// 租户配额服务接口
type TenantQuotaService interface {
    GetTenantQuota(tenantID string) (*TenantQuota, error)
    SetTenantQuota(tenantID string, quota *TenantQuota) error
    ConsumeTenantQuota(tenantID string, amount int) error
    GetTenantUsageStats(tenantID string, timeRange string) (*UsageStats, error)
}

// 租户统计服务接口
type TenantStatsService interface {
    GetAPICallStats(tenantID string, timeRange string) (*APICallStats, error)
    GetCostAnalysis(tenantID string, timeRange string) (*CostAnalysis, error)
    GetUserActivity(tenantID string, timeRange string) (*UserActivity, error)
}
```

## 📝 与另一团队的协调要点

### 1. 接口命名规范
- 所有SAAS相关接口统一使用 `/api/v1/admin/` 前缀
- 租户相关接口使用 `/tenants/{tenant_id}/` 路径参数
- 统计接口使用 `/stats/` 子路径

### 2. 数据格式标准
- 时间格式统一使用 ISO 8601 标准
- 配额数值统一使用整数（以token为单位）
- 成本计算统一使用6位小数精度

### 3. 错误处理规范
- 租户不存在：404 Tenant Not Found
- 配额超限：429 Quota Exceeded  
- 权限不足：403 Insufficient Permissions

### 4. 认证授权要求
- 超级管理员接口需要特殊权限验证
- 租户级接口需要租户权限验证
- 所有接口都需要记录操作日志

## 🚀 实施建议

### 阶段一：基础接口预留
1. 在路由层预留接口路径
2. 创建基础的Handler结构
3. 定义数据模型和响应格式

### 阶段二：中间件集成
1. 实现租户识别中间件
2. 实现租户配额检查中间件
3. 集成到现有路由中

### 阶段三：服务层实现
1. 实现租户配额服务
2. 实现租户统计服务
3. 完善数据库操作层

### 阶段四：测试和优化
1. 编写单元测试
2. 进行集成测试
3. 性能优化和监控

## 🔗 接口集成示例

### 1. 超级管理员后台集成示例

```javascript
// 前端调用示例
const getTenantQuotaOverview = async (tenantId) => {
  const response = await fetch(`/api/v1/admin/tenants/${tenantId}/quota/overview`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// 设置租户配额
const setTenantQuota = async (tenantId, quotaSettings) => {
  const response = await fetch(`/api/v1/admin/tenants/${tenantId}/quota/settings`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(quotaSettings)
  });
  return response.json();
};
```

### 2. 租户数据隔离示例

```go
// 租户数据过滤示例
func (r *TokenRepository) GetTokensByTenant(tenantID string) ([]*models.Token, error) {
    var tokens []*models.Token
    err := r.db.Where("tenant_id = ?", tenantID).Find(&tokens).Error
    return tokens, err
}

// 租户配额检查示例
func (s *QuotaService) CheckTenantQuota(tenantID string, requestTokens int) error {
    quota, err := s.GetTenantQuota(tenantID)
    if err != nil {
        return err
    }

    if quota.UsedQuota + requestTokens > quota.TotalQuota {
        return ErrQuotaExceeded
    }

    return nil
}
```

## 📊 监控和告警预留

### 1. 租户级别监控指标

```yaml
# 预留监控指标
tenant_api_calls_total:
  type: counter
  labels: [tenant_id, model_name, status]

tenant_quota_usage_ratio:
  type: gauge
  labels: [tenant_id, quota_type]

tenant_response_time_seconds:
  type: histogram
  labels: [tenant_id, model_name]

tenant_cost_total:
  type: counter
  labels: [tenant_id, model_name]
```

### 2. 告警规则预留

```yaml
# 预留告警规则
- alert: TenantQuotaExceeded
  expr: tenant_quota_usage_ratio > 0.9
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "租户配额即将耗尽"

- alert: TenantHighErrorRate
  expr: rate(tenant_api_calls_total{status="error"}[5m]) > 0.1
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "租户API错误率过高"
```

## 🔒 安全考虑

### 1. 数据访问控制

```go
// 租户数据访问权限检查
func (m *TenantMiddleware) CheckTenantAccess(c *gin.Context) {
    userTenantID := c.GetString("user_tenant_id")
    requestTenantID := c.Param("tenant_id")

    // 超级管理员可以访问所有租户数据
    if isSupperAdmin(c) {
        return
    }

    // 普通用户只能访问自己租户的数据
    if userTenantID != requestTenantID {
        c.JSON(403, gin.H{"error": "Access denied"})
        c.Abort()
        return
    }
}
```

### 2. API密钥管理

```go
// 租户级别的API密钥管理
type TenantAPIKey struct {
    ID        string    `json:"id"`
    TenantID  string    `json:"tenant_id"`
    KeyName   string    `json:"key_name"`
    KeyHash   string    `json:"key_hash"`
    Scopes    []string  `json:"scopes"`
    ExpiresAt time.Time `json:"expires_at"`
    CreatedAt time.Time `json:"created_at"`
}
```

## 📈 性能优化预留

### 1. 缓存策略

```go
// 租户配额缓存
type TenantQuotaCache struct {
    redis *redis.Client
}

func (c *TenantQuotaCache) GetTenantQuota(tenantID string) (*TenantQuota, error) {
    key := fmt.Sprintf("tenant:quota:%s", tenantID)
    data, err := c.redis.Get(key).Result()
    if err == redis.Nil {
        // 缓存未命中，从数据库加载
        return c.loadFromDB(tenantID)
    }

    var quota TenantQuota
    json.Unmarshal([]byte(data), &quota)
    return &quota, nil
}
```

### 2. 数据库优化

```sql
-- 预留索引优化
CREATE INDEX CONCURRENTLY idx_tenant_api_logs_tenant_time
ON llmsapi_tenant_api_logs(tenant_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_tenant_quotas_tenant_type
ON llmsapi_tenant_quotas(tenant_id, quota_type);

-- 分区表预留（按租户分区）
CREATE TABLE llmsapi_tenant_api_logs_partitioned (
    LIKE llmsapi_tenant_api_logs INCLUDING ALL
) PARTITION BY HASH (tenant_id);
```

## 🧪 测试用例预留

### 1. 单元测试示例

```go
func TestTenantQuotaService_CheckQuota(t *testing.T) {
    tests := []struct {
        name        string
        tenantID    string
        requestTokens int
        expectError bool
    }{
        {
            name: "正常配额检查",
            tenantID: "tenant-001",
            requestTokens: 100,
            expectError: false,
        },
        {
            name: "配额超限检查",
            tenantID: "tenant-002",
            requestTokens: 10000,
            expectError: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := quotaService.CheckTenantQuota(tt.tenantID, tt.requestTokens)
            if tt.expectError {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

### 2. 集成测试示例

```go
func TestTenantAPIIntegration(t *testing.T) {
    // 创建测试租户
    tenant := createTestTenant()

    // 测试租户配额设置
    quotaReq := &TenantQuotaRequest{
        MonthlyQuota: 10000,
        DailyQuota: 1000,
    }

    resp := httptest.NewRecorder()
    req := httptest.NewRequest("PUT",
        fmt.Sprintf("/api/v1/admin/tenants/%s/quota/settings", tenant.ID),
        bytes.NewBuffer(marshal(quotaReq)))

    router.ServeHTTP(resp, req)
    assert.Equal(t, 200, resp.Code)
}
```

---

**文档状态**: 待与另一团队确认
**下一步**: 根据反馈调整接口设计，开始基础预留实现
**协调联系**: 需要与大模型API中转系统开发团队进行接口对接确认
