# 租户级别配额管理接口设计

**文档版本**: v1.0  
**创建时间**: 2025-07-18  
**负责人**: AI开发团队  
**目标**: 为大模型API中转系统设计租户级别配额管理接口  

---

## 📋 文档概述

本文档详细设计AI生态平台SAAS多租户环境下的配额管理接口，包括租户配额设置、查询、消费和统计等功能，为超级管理员后台提供完整的租户配额管理能力。

## 🎯 设计目标

1. **多级配额管理**: 支持平台级、租户级、用户级、Token级四级配额管理
2. **灵活配额策略**: 支持总量配额、周期配额、模型特定配额
3. **实时配额监控**: 提供实时配额使用统计和告警
4. **配额自动重置**: 支持按日、按月、按年自动重置配额
5. **配额转移与共享**: 支持租户内配额分配和调整

## 🔍 核心概念

### 配额类型

| 配额类型 | 描述 | 应用场景 |
|---------|------|----------|
| `TOTAL_TOKENS` | 总Token消耗量 | 限制总体使用量 |
| `MONTHLY_TOKENS` | 每月Token消耗量 | 月度订阅计划 |
| `DAILY_TOKENS` | 每日Token消耗量 | 防止突发大量使用 |
| `HOURLY_TOKENS` | 每小时Token消耗量 | 平滑使用曲线 |
| `MODEL_SPECIFIC` | 特定模型Token消耗量 | 限制高成本模型使用 |
| `CONCURRENT_REQUESTS` | 并发请求数 | 控制系统负载 |
| `REQUESTS_PER_MINUTE` | 每分钟请求数 | 限流保护 |

### 配额级别

| 配额级别 | 描述 | 优先级 |
|---------|------|-------|
| `PLATFORM` | 整个平台的配额限制 | 最高 |
| `TENANT` | 租户级别的配额限制 | 高 |
| `USER` | 用户级别的配额限制 | 中 |
| `TOKEN` | Token级别的配额限制 | 低 |

## 📝 数据模型设计

### TenantQuota 租户配额模型

```go
type TenantQuota struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TenantID     string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    QuotaType    string    `json:"quota_type" gorm:"type:varchar(50);not null"`
    QuotaLimit   int64     `json:"quota_limit" gorm:"default:0"`
    QuotaUsed    int64     `json:"quota_used" gorm:"default:0"`
    ResetPeriod  string    `json:"reset_period" gorm:"type:varchar(20);default:'monthly'"`
    NextResetAt  time.Time `json:"next_reset_at"`
    ModelName    string    `json:"model_name" gorm:"type:varchar(100);default:''"`
    IsActive     bool      `json:"is_active" gorm:"default:true"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

### QuotaConsumption 配额消费记录

```go
type QuotaConsumption struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TenantID     string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID       string    `json:"user_id" gorm:"type:varchar(50);not null;index"`
    TokenID      uint      `json:"token_id" gorm:"not null;index"`
    RequestID    string    `json:"request_id" gorm:"type:varchar(50);not null;index"`
    QuotaType    string    `json:"quota_type" gorm:"type:varchar(50);not null"`
    QuotaBefore  int64     `json:"quota_before" gorm:"default:0"`
    QuotaAfter   int64     `json:"quota_after" gorm:"default:0"`
    QuotaUsed    int64     `json:"quota_used" gorm:"default:0"`
    ModelName    string    `json:"model_name" gorm:"type:varchar(100)"`
    CreatedAt    time.Time `json:"created_at" gorm:"index"`
}
```

### QuotaAlert 配额告警记录

```go
type QuotaAlert struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TenantID     string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    QuotaType    string    `json:"quota_type" gorm:"type:varchar(50);not null"`
    AlertType    string    `json:"alert_type" gorm:"type:varchar(50);not null"`
    Threshold    float64   `json:"threshold" gorm:"default:0.9"`
    CurrentUsage float64   `json:"current_usage" gorm:"default:0"`
    Message      string    `json:"message" gorm:"type:text"`
    IsResolved   bool      `json:"is_resolved" gorm:"default:false"`
    ResolvedAt   *time.Time `json:"resolved_at"`
    CreatedAt    time.Time `json:"created_at"`
}
```

## 🛣️ API接口设计

### 1. 租户配额管理接口

#### 1.1 获取租户配额列表

```http
GET /api/v1/admin/tenants/{tenant_id}/quotas
```

**功能描述**: 获取指定租户的所有配额设置

**请求参数**:
- `tenant_id`: 租户ID (路径参数)
- `quota_type`: 配额类型 (查询参数，可选)
- `model_name`: 模型名称 (查询参数，可选)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "quotas": [
      {
        "id": 1,
        "tenant_id": "tenant-001",
        "quota_type": "MONTHLY_TOKENS",
        "quota_limit": 1000000,
        "quota_used": 250000,
        "reset_period": "monthly",
        "next_reset_at": "2025-08-01T00:00:00Z",
        "model_name": "",
        "is_active": true,
        "created_at": "2025-07-01T00:00:00Z",
        "updated_at": "2025-07-18T10:30:00Z"
      },
      {
        "id": 2,
        "tenant_id": "tenant-001",
        "quota_type": "MODEL_SPECIFIC",
        "quota_limit": 100000,
        "quota_used": 35000,
        "reset_period": "monthly",
        "next_reset_at": "2025-08-01T00:00:00Z",
        "model_name": "gpt-4",
        "is_active": true,
        "created_at": "2025-07-01T00:00:00Z",
        "updated_at": "2025-07-18T10:30:00Z"
      }
    ],
    "total": 2
  }
}
```

#### 1.2 创建/更新租户配额

```http
PUT /api/v1/admin/tenants/{tenant_id}/quotas
```

**功能描述**: 创建或更新租户配额设置

**请求参数**:
- `tenant_id`: 租户ID (路径参数)

**请求体**:
```json
{
  "quotas": [
    {
      "quota_type": "MONTHLY_TOKENS",
      "quota_limit": 2000000,
      "reset_period": "monthly",
      "model_name": "",
      "is_active": true
    },
    {
      "quota_type": "MODEL_SPECIFIC",
      "quota_limit": 200000,
      "reset_period": "monthly",
      "model_name": "gpt-4",
      "is_active": true
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "租户配额设置成功",
  "data": {
    "updated_count": 2
  }
}
```

#### 1.3 获取租户配额使用情况

```http
GET /api/v1/admin/tenants/{tenant_id}/quotas/usage
```

**功能描述**: 获取租户配额使用统计

**请求参数**:
- `tenant_id`: 租户ID (路径参数)
- `time_range`: 时间范围 (查询参数，可选，默认"this_month")
- `group_by`: 分组方式 (查询参数，可选，支持"day"、"model"、"user")

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant-001",
    "total_quota": 1000000,
    "used_quota": 250000,
    "usage_percentage": 25.0,
    "remaining_quota": 750000,
    "reset_date": "2025-08-01T00:00:00Z",
    "daily_usage": [
      {"date": "2025-07-01", "usage": 10000},
      {"date": "2025-07-02", "usage": 12000},
      {"date": "2025-07-03", "usage": 9500}
    ],
    "model_usage": {
      "gpt-4": 35000,
      "gpt-3.5-turbo": 180000,
      "claude-3-sonnet": 35000
    },
    "user_usage": [
      {"user_id": "user-001", "usage": 120000},
      {"user_id": "user-002", "usage": 80000},
      {"user_id": "user-003", "usage": 50000}
    ]
  }
}
```

#### 1.4 重置租户配额

```http
POST /api/v1/admin/tenants/{tenant_id}/quotas/reset
```

**功能描述**: 手动重置租户配额

**请求参数**:
- `tenant_id`: 租户ID (路径参数)

**请求体**:
```json
{
  "quota_types": ["MONTHLY_TOKENS", "MODEL_SPECIFIC"],
  "model_name": "",
  "reset_reason": "客户请求手动重置"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "租户配额已重置",
  "data": {
    "reset_count": 2,
    "reset_time": "2025-07-18T14:30:00Z"
  }
}
```

### 2. 配额消费记录接口

#### 2.1 获取配额消费记录

```http
GET /api/v1/admin/tenants/{tenant_id}/quota-consumptions
```

**功能描述**: 获取租户配额消费记录

**请求参数**:
- `tenant_id`: 租户ID (路径参数)
- `start_date`: 开始日期 (查询参数，可选)
- `end_date`: 结束日期 (查询参数，可选)
- `user_id`: 用户ID (查询参数，可选)
- `model_name`: 模型名称 (查询参数，可选)
- `page`: 页码 (查询参数，可选，默认1)
- `size`: 每页记录数 (查询参数，可选，默认20)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "consumptions": [
      {
        "id": 1001,
        "tenant_id": "tenant-001",
        "user_id": "user-001",
        "token_id": 123,
        "request_id": "req-abc123",
        "quota_type": "MONTHLY_TOKENS",
        "quota_before": 240000,
        "quota_after": 250000,
        "quota_used": 10000,
        "model_name": "gpt-4",
        "created_at": "2025-07-18T10:15:30Z"
      }
    ],
    "total": 156,
    "page": 1,
    "size": 20
  }
}
```

## 🔧 实现要点

### 1. 配额检查流程

```go
// 配额检查流程
func (s *QuotaService) CheckTenantQuota(tenantID, userID string, tokenID uint, modelName string, requestTokens int64) error {
    // 1. 检查平台级配额
    if err := s.checkPlatformQuota(requestTokens); err != nil {
        return err
    }
    
    // 2. 检查租户级配额
    if err := s.checkTenantQuota(tenantID, modelName, requestTokens); err != nil {
        return err
    }
    
    // 3. 检查用户级配额
    if err := s.checkUserQuota(tenantID, userID, modelName, requestTokens); err != nil {
        return err
    }
    
    // 4. 检查Token级配额
    if err := s.checkTokenQuota(tokenID, requestTokens); err != nil {
        return err
    }
    
    return nil
}
```

### 2. 配额消费记录

```go
// 配额消费记录
func (s *QuotaService) RecordQuotaConsumption(tenantID, userID string, tokenID uint, requestID string, 
    quotaType string, quotaBefore, quotaUsed int64, modelName string) error {
    
    consumption := &models.QuotaConsumption{
        TenantID:    tenantID,
        UserID:      userID,
        TokenID:     tokenID,
        RequestID:   requestID,
        QuotaType:   quotaType,
        QuotaBefore: quotaBefore,
        QuotaAfter:  quotaBefore + quotaUsed,
        QuotaUsed:   quotaUsed,
        ModelName:   modelName,
    }
    
    return s.repo.CreateQuotaConsumption(consumption)
}
```

### 3. 配额自动重置

```go
// 配额自动重置任务
func (s *QuotaService) RunQuotaResetTask() {
    now := time.Now()
    
    // 查找需要重置的配额
    quotasToReset, err := s.repo.FindQuotasToReset(now)
    if err != nil {
        log.Errorf("查找需要重置的配额失败: %v", err)
        return
    }
    
    for _, quota := range quotasToReset {
        // 重置配额
        quota.QuotaUsed = 0
        
        // 计算下次重置时间
        quota.NextResetAt = calculateNextResetTime(quota.ResetPeriod, now)
        
        // 更新配额
        if err := s.repo.UpdateTenantQuota(quota); err != nil {
            log.Errorf("重置配额失败: %v", err)
            continue
        }
        
        // 记录重置日志
        s.recordQuotaReset(quota)
    }
}
```

## 📊 监控与告警

### 1. 配额使用率监控

```go
// 配额使用率监控
func (s *QuotaService) MonitorQuotaUsage() {
    // 查找所有活跃租户配额
    quotas, err := s.repo.FindAllActiveTenantQuotas()
    if err != nil {
        log.Errorf("查询活跃租户配额失败: %v", err)
        return
    }
    
    for _, quota := range quotas {
        // 计算使用率
        usageRatio := float64(quota.QuotaUsed) / float64(quota.QuotaLimit)
        
        // 检查是否需要告警
        if usageRatio >= 0.8 && !s.hasRecentAlert(quota.TenantID, quota.QuotaType) {
            // 创建告警
            s.createQuotaAlert(quota, usageRatio)
            
            // 发送通知
            s.sendQuotaAlertNotification(quota, usageRatio)
        }
    }
}
```

### 2. 告警级别

| 告警级别 | 使用率阈值 | 处理方式 |
|---------|-----------|---------|
| `INFO` | 50% | 系统记录，无主动通知 |
| `WARNING` | 80% | 系统记录，发送通知 |
| `CRITICAL` | 95% | 系统记录，发送通知，考虑自动扩容 |
| `BLOCKED` | 100% | 系统记录，发送通知，拒绝新请求 |

## 🔗 与其他系统的集成

### 1. 与计费系统集成

```go
// 与计费系统集成
func (s *QuotaService) SyncWithBillingSystem() {
    // 获取所有租户的配额使用情况
    tenantUsage, err := s.repo.GetAllTenantQuotaUsage()
    if err != nil {
        log.Errorf("获取租户配额使用情况失败: %v", err)
        return
    }
    
    // 同步到计费系统
    for tenantID, usage := range tenantUsage {
        billingData := &billing.UsageData{
            TenantID:  tenantID,
            UsageData: usage,
            Timestamp: time.Now(),
        }
        
        if err := s.billingClient.SyncUsageData(billingData); err != nil {
            log.Errorf("同步租户[%s]使用数据到计费系统失败: %v", tenantID, err)
        }
    }
}
```

### 2. 与通知系统集成

```go
// 与通知系统集成
func (s *QuotaService) sendQuotaAlertNotification(quota *models.TenantQuota, usageRatio float64) {
    // 获取租户管理员
    admins, err := s.userService.GetTenantAdmins(quota.TenantID)
    if err != nil {
        log.Errorf("获取租户管理员失败: %v", err)
        return
    }
    
    // 构建通知内容
    notification := &notification.Message{
        Title:   "配额使用告警",
        Content: fmt.Sprintf("您的%s配额已使用%.1f%%，请及时处理", quota.QuotaType, usageRatio*100),
        Type:    "QUOTA_ALERT",
        Level:   getAlertLevel(usageRatio),
        Data: map[string]interface{}{
            "tenant_id":  quota.TenantID,
            "quota_type": quota.QuotaType,
            "usage":      usageRatio,
        },
    }
    
    // 发送通知给所有管理员
    for _, admin := range admins {
        notification.RecipientID = admin.ID
        s.notificationService.SendNotification(notification)
    }
}
```

---

**文档状态**: 待与另一团队确认  
**下一步**: 根据反馈调整接口设计，开始基础预留实现
