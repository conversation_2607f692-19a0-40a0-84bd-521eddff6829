# 多租户数据隔离加强实施方案

**文档版本**: v1.0  
**创建时间**: 2025-07-18  
**负责人**: AI开发团队  
**目标**: 完善AI生态平台的多租户数据隔离机制，确保数据安全和租户隔离  

---

## 📋 现状分析

### 当前多租户实现情况

基于代码分析，AI生态平台的多租户支持现状如下：

#### ✅ 已实现的功能

1. **数据模型层面**：
   - 所有核心表已包含`tenant_id`字段
   - 用户模型（User）、智能体模型（Agent）、Token模型等均支持租户隔离
   - 角色权限模型（Role、Permission）支持租户级别权限

2. **中间件层面**：
   - ai-llms服务已实现租户识别中间件（`TenantMiddleware`）
   - 支持从域名、Header、JWT Token多种方式提取租户ID
   - 基础的租户上下文传递机制

3. **数据访问层面**：
   - ai-users服务实现了基础的租户感知模型（`BaseTenantModel`）
   - 提供了从上下文获取租户ID的工具函数

#### ❌ 需要加强的功能

1. **数据库查询自动过滤**：缺少统一的租户数据过滤机制
2. **跨服务租户一致性**：各服务间租户识别机制不统一
3. **租户数据访问审计**：缺少完整的数据访问日志
4. **租户级别的数据备份和恢复**：缺少租户数据隔离备份
5. **租户配额和限制**：缺少租户级别的资源限制

## 🎯 加强目标

### 核心目标

1. **100%数据隔离**：确保租户间数据完全隔离，无数据泄露风险
2. **统一租户识别**：所有服务使用统一的租户识别机制
3. **自动数据过滤**：数据库查询自动添加租户过滤条件
4. **完整审计日志**：记录所有跨租户数据访问尝试
5. **租户级别监控**：实时监控租户数据访问和资源使用

### 技术指标

- **数据隔离率**：100%（零跨租户数据访问）
- **查询性能影响**：< 5%（租户过滤开销）
- **审计日志覆盖率**：100%（所有数据访问）
- **租户识别准确率**：99.9%（容错机制）

## 🔧 技术实施方案

### 1. 统一租户识别中间件

#### 1.1 跨服务租户识别标准化

```go
// pkg/middleware/tenant_unified.go
package middleware

import (
    "context"
    "net/http"
    "strings"
    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt/v5"
)

type UnifiedTenantMiddleware struct {
    jwtSecret []byte
    cache     *redis.Client
    logger    *logrus.Logger
}

// TenantIdentification 统一租户识别中间件
func (utm *UnifiedTenantMiddleware) TenantIdentification() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := utm.extractTenantID(c)
        
        // 验证租户ID有效性
        if !utm.validateTenantID(tenantID) {
            utm.logger.Warnf("Invalid tenant ID: %s, IP: %s", tenantID, c.ClientIP())
            c.JSON(http.StatusBadRequest, gin.H{
                "error": "Invalid tenant ID",
                "code":  "INVALID_TENANT_ID",
            })
            c.Abort()
            return
        }
        
        // 检查租户状态
        if !utm.isTenantActive(tenantID) {
            utm.logger.Warnf("Inactive tenant: %s, IP: %s", tenantID, c.ClientIP())
            c.JSON(http.StatusForbidden, gin.H{
                "error": "Tenant is inactive",
                "code":  "TENANT_INACTIVE",
            })
            c.Abort()
            return
        }
        
        // 设置租户上下文
        c.Set("tenant_id", tenantID)
        ctx := context.WithValue(c.Request.Context(), "tenant_id", tenantID)
        c.Request = c.Request.WithContext(ctx)
        
        // 记录租户访问日志
        utm.logTenantAccess(c, tenantID)
        
        c.Next()
    }
}

// extractTenantID 多种方式提取租户ID
func (utm *UnifiedTenantMiddleware) extractTenantID(c *gin.Context) string {
    // 1. 优先从自定义Header获取
    if tenantID := c.GetHeader("X-Tenant-ID"); tenantID != "" {
        return tenantID
    }
    
    // 2. 从子域名提取
    if tenantID := utm.extractFromSubdomain(c.Request.Host); tenantID != "" {
        return tenantID
    }
    
    // 3. 从JWT Token提取
    if tenantID := utm.extractFromJWT(c); tenantID != "" {
        return tenantID
    }
    
    // 4. 从查询参数提取（兜底方案）
    if tenantID := c.Query("tenant_id"); tenantID != "" {
        return tenantID
    }
    
    // 5. 默认租户（开发环境）
    return "default"
}

// extractFromSubdomain 从子域名提取租户ID
func (utm *UnifiedTenantMiddleware) extractFromSubdomain(host string) string {
    parts := strings.Split(host, ".")
    if len(parts) >= 3 {
        subdomain := parts[0]
        // 排除系统保留子域名
        reserved := []string{"www", "api", "admin", "static", "cdn"}
        for _, r := range reserved {
            if subdomain == r {
                return ""
            }
        }
        return subdomain
    }
    return ""
}

// extractFromJWT 从JWT Token提取租户ID
func (utm *UnifiedTenantMiddleware) extractFromJWT(c *gin.Context) string {
    authHeader := c.GetHeader("Authorization")
    if authHeader == "" {
        return ""
    }
    
    tokenString := strings.TrimPrefix(authHeader, "Bearer ")
    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        return utm.jwtSecret, nil
    })
    
    if err != nil || !token.Valid {
        return ""
    }
    
    if claims, ok := token.Claims.(jwt.MapClaims); ok {
        if tenantID, exists := claims["tenant_id"]; exists {
            return tenantID.(string)
        }
    }
    
    return ""
}
```

#### 1.2 租户验证和缓存机制

```go
// validateTenantID 验证租户ID格式和存在性
func (utm *UnifiedTenantMiddleware) validateTenantID(tenantID string) bool {
    // 1. 格式验证
    if len(tenantID) < 3 || len(tenantID) > 50 {
        return false
    }
    
    // 2. 字符验证（只允许字母、数字、下划线、连字符）
    matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, tenantID)
    if !matched {
        return false
    }
    
    // 3. 缓存检查
    cacheKey := fmt.Sprintf("tenant:valid:%s", tenantID)
    if cached, err := utm.cache.Get(cacheKey).Result(); err == nil {
        return cached == "true"
    }
    
    // 4. 数据库验证
    exists := utm.checkTenantExists(tenantID)
    
    // 5. 缓存结果（5分钟）
    utm.cache.Set(cacheKey, fmt.Sprintf("%t", exists), 5*time.Minute)
    
    return exists
}

// isTenantActive 检查租户是否激活
func (utm *UnifiedTenantMiddleware) isTenantActive(tenantID string) bool {
    cacheKey := fmt.Sprintf("tenant:active:%s", tenantID)
    if cached, err := utm.cache.Get(cacheKey).Result(); err == nil {
        return cached == "true"
    }
    
    active := utm.checkTenantActive(tenantID)
    utm.cache.Set(cacheKey, fmt.Sprintf("%t", active), 2*time.Minute)
    
    return active
}
```

### 2. 数据访问层自动过滤

#### 2.1 GORM插件实现

```go
// pkg/database/tenant_plugin.go
package database

import (
    "context"
    "fmt"
    "gorm.io/gorm"
)

// TenantPlugin GORM租户插件
type TenantPlugin struct{}

// Name 插件名称
func (tp *TenantPlugin) Name() string {
    return "tenant_isolation"
}

// Initialize 初始化插件
func (tp *TenantPlugin) Initialize(db *gorm.DB) error {
    // 注册查询回调
    db.Callback().Query().Before("gorm:query").Register("tenant:query", tp.beforeQuery)
    
    // 注册创建回调
    db.Callback().Create().Before("gorm:create").Register("tenant:create", tp.beforeCreate)
    
    // 注册更新回调
    db.Callback().Update().Before("gorm:update").Register("tenant:update", tp.beforeUpdate)
    
    // 注册删除回调
    db.Callback().Delete().Before("gorm:delete").Register("tenant:delete", tp.beforeDelete)
    
    return nil
}

// beforeQuery 查询前添加租户过滤
func (tp *TenantPlugin) beforeQuery(db *gorm.DB) {
    if db.Statement.Schema == nil {
        return
    }
    
    // 检查模型是否有tenant_id字段
    if !tp.hasTenantField(db.Statement.Schema) {
        return
    }
    
    // 从上下文获取租户ID
    tenantID := tp.getTenantFromContext(db.Statement.Context)
    if tenantID == "" {
        return
    }
    
    // 检查是否已经有租户过滤条件
    if tp.hasExistingTenantCondition(db) {
        return
    }
    
    // 添加租户过滤条件
    db.Where("tenant_id = ?", tenantID)
}

// beforeCreate 创建前设置租户ID
func (tp *TenantPlugin) beforeCreate(db *gorm.DB) {
    if db.Statement.Schema == nil {
        return
    }
    
    if !tp.hasTenantField(db.Statement.Schema) {
        return
    }
    
    tenantID := tp.getTenantFromContext(db.Statement.Context)
    if tenantID == "" {
        return
    }
    
    // 设置租户ID
    if db.Statement.Dest != nil {
        tp.setTenantID(db.Statement.Dest, tenantID)
    }
}

// beforeUpdate 更新前添加租户过滤
func (tp *TenantPlugin) beforeUpdate(db *gorm.DB) {
    if db.Statement.Schema == nil {
        return
    }
    
    if !tp.hasTenantField(db.Statement.Schema) {
        return
    }
    
    tenantID := tp.getTenantFromContext(db.Statement.Context)
    if tenantID == "" {
        return
    }
    
    if !tp.hasExistingTenantCondition(db) {
        db.Where("tenant_id = ?", tenantID)
    }
}

// beforeDelete 删除前添加租户过滤
func (tp *TenantPlugin) beforeDelete(db *gorm.DB) {
    if db.Statement.Schema == nil {
        return
    }
    
    if !tp.hasTenantField(db.Statement.Schema) {
        return
    }
    
    tenantID := tp.getTenantFromContext(db.Statement.Context)
    if tenantID == "" {
        return
    }
    
    if !tp.hasExistingTenantCondition(db) {
        db.Where("tenant_id = ?", tenantID)
    }
}

// hasTenantField 检查模型是否有tenant_id字段
func (tp *TenantPlugin) hasTenantField(schema *gorm.Schema) bool {
    _, ok := schema.FieldsByDBName["tenant_id"]
    return ok
}

// getTenantFromContext 从上下文获取租户ID
func (tp *TenantPlugin) getTenantFromContext(ctx context.Context) string {
    if ctx == nil {
        return ""
    }
    
    if tenantID, ok := ctx.Value("tenant_id").(string); ok {
        return tenantID
    }
    
    return ""
}

// hasExistingTenantCondition 检查是否已有租户过滤条件
func (tp *TenantPlugin) hasExistingTenantCondition(db *gorm.DB) bool {
    for _, clause := range db.Statement.Clauses {
        if whereClause, ok := clause.Expression.(gorm.Expr); ok {
            if strings.Contains(whereClause.SQL, "tenant_id") {
                return true
            }
        }
    }
    return false
}

// setTenantID 设置模型的租户ID
func (tp *TenantPlugin) setTenantID(dest interface{}, tenantID string) {
    if setter, ok := dest.(TenantSetter); ok {
        setter.SetTenantID(tenantID)
        return
    }
    
    // 使用反射设置
    rv := reflect.ValueOf(dest)
    if rv.Kind() == reflect.Ptr {
        rv = rv.Elem()
    }
    
    if rv.Kind() == reflect.Struct {
        field := rv.FieldByName("TenantID")
        if field.IsValid() && field.CanSet() {
            field.SetString(tenantID)
        }
    }
}

// TenantSetter 租户设置接口
type TenantSetter interface {
    SetTenantID(tenantID string)
}
```

#### 2.2 数据库连接配置

```go
// pkg/database/connection.go
package database

import (
    "gorm.io/gorm"
    "gorm.io/driver/postgres"
)

// NewTenantAwareDB 创建租户感知的数据库连接
func NewTenantAwareDB(dsn string) (*gorm.DB, error) {
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, err
    }
    
    // 注册租户插件
    if err := db.Use(&TenantPlugin{}); err != nil {
        return nil, fmt.Errorf("failed to register tenant plugin: %w", err)
    }
    
    return db, nil
}
```

### 3. 租户数据访问审计

#### 3.1 审计日志模型

```go
// internal/models/audit.go
package models

import (
    "time"
    "encoding/json"
)

// TenantAuditLog 租户数据访问审计日志
type TenantAuditLog struct {
    ID            uint            `json:"id" gorm:"primaryKey"`
    TenantID      string          `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID        *string         `json:"user_id" gorm:"type:varchar(50);index"`
    Action        string          `json:"action" gorm:"type:varchar(50);not null"` // CREATE, READ, UPDATE, DELETE
    Resource      string          `json:"resource" gorm:"type:varchar(100);not null"` // 表名或资源类型
    ResourceID    *string         `json:"resource_id" gorm:"type:varchar(50)"`
    IPAddress     string          `json:"ip_address" gorm:"type:varchar(45);not null"`
    UserAgent     *string         `json:"user_agent" gorm:"type:text"`
    RequestPath   string          `json:"request_path" gorm:"type:varchar(500);not null"`
    RequestMethod string          `json:"request_method" gorm:"type:varchar(10);not null"`
    StatusCode    int             `json:"status_code" gorm:"not null"`
    ResponseTime  int64           `json:"response_time" gorm:"not null"` // 毫秒
    ErrorMessage  *string         `json:"error_message" gorm:"type:text"`
    Metadata      json.RawMessage `json:"metadata" gorm:"type:jsonb"`
    CreatedAt     time.Time       `json:"created_at" gorm:"index"`
}

// CrossTenantAccessAttempt 跨租户访问尝试记录
type CrossTenantAccessAttempt struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    RequestTenantID string    `json:"request_tenant_id" gorm:"type:varchar(50);not null;index"`
    TargetTenantID  string    `json:"target_tenant_id" gorm:"type:varchar(50);not null;index"`
    UserID          *string   `json:"user_id" gorm:"type:varchar(50);index"`
    Resource        string    `json:"resource" gorm:"type:varchar(100);not null"`
    ResourceID      *string   `json:"resource_id" gorm:"type:varchar(50)"`
    IPAddress       string    `json:"ip_address" gorm:"type:varchar(45);not null"`
    UserAgent       *string   `json:"user_agent" gorm:"type:text"`
    RequestPath     string    `json:"request_path" gorm:"type:varchar(500);not null"`
    IsBlocked       bool      `json:"is_blocked" gorm:"default:true"`
    BlockReason     string    `json:"block_reason" gorm:"type:varchar(200)"`
    CreatedAt       time.Time `json:"created_at" gorm:"index"`
}
```

#### 3.2 审计中间件

```go
// pkg/middleware/audit.go
package middleware

import (
    "bytes"
    "encoding/json"
    "io"
    "time"
    "github.com/gin-gonic/gin"
)

type AuditMiddleware struct {
    db     *gorm.DB
    logger *logrus.Logger
}

// TenantAuditLog 租户审计日志中间件
func (am *AuditMiddleware) TenantAuditLog() gin.HandlerFunc {
    return func(c *gin.Context) {
        startTime := time.Now()
        
        // 记录请求体
        var requestBody []byte
        if c.Request.Body != nil {
            requestBody, _ = io.ReadAll(c.Request.Body)
            c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
        }
        
        // 使用自定义ResponseWriter记录响应
        blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
        c.Writer = blw
        
        c.Next()
        
        // 计算响应时间
        responseTime := time.Since(startTime).Milliseconds()
        
        // 获取租户信息
        tenantID, _ := c.Get("tenant_id")
        userID, _ := c.Get("user_id")
        
        // 构建审计日志
        auditLog := &models.TenantAuditLog{
            TenantID:      tenantID.(string),
            Action:        am.getActionFromMethod(c.Request.Method),
            Resource:      am.getResourceFromPath(c.Request.URL.Path),
            IPAddress:     c.ClientIP(),
            UserAgent:     &c.Request.UserAgent(),
            RequestPath:   c.Request.URL.Path,
            RequestMethod: c.Request.Method,
            StatusCode:    c.Writer.Status(),
            ResponseTime:  responseTime,
            CreatedAt:     startTime,
        }
        
        if userID != nil {
            userIDStr := userID.(string)
            auditLog.UserID = &userIDStr
        }
        
        // 添加元数据
        metadata := map[string]interface{}{
            "request_size":  len(requestBody),
            "response_size": blw.body.Len(),
            "query_params":  c.Request.URL.RawQuery,
        }
        
        if metadataJSON, err := json.Marshal(metadata); err == nil {
            auditLog.Metadata = metadataJSON
        }
        
        // 异步保存审计日志
        go am.saveAuditLog(auditLog)
    }
}

// bodyLogWriter 自定义ResponseWriter用于记录响应体
type bodyLogWriter struct {
    gin.ResponseWriter
    body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
    w.body.Write(b)
    return w.ResponseWriter.Write(b)
}

// getActionFromMethod 从HTTP方法获取操作类型
func (am *AuditMiddleware) getActionFromMethod(method string) string {
    switch method {
    case "GET":
        return "READ"
    case "POST":
        return "CREATE"
    case "PUT", "PATCH":
        return "UPDATE"
    case "DELETE":
        return "DELETE"
    default:
        return "UNKNOWN"
    }
}

// getResourceFromPath 从路径获取资源类型
func (am *AuditMiddleware) getResourceFromPath(path string) string {
    // 简单的路径解析，可以根据实际需求优化
    parts := strings.Split(strings.Trim(path, "/"), "/")
    if len(parts) >= 3 {
        return parts[2] // /api/v1/users -> users
    }
    return "unknown"
}

// saveAuditLog 异步保存审计日志
func (am *AuditMiddleware) saveAuditLog(log *models.TenantAuditLog) {
    if err := am.db.Create(log).Error; err != nil {
        am.logger.Errorf("Failed to save audit log: %v", err)
    }
}
```

### 4. 租户配额和限制管理

#### 4.1 租户资源配额模型

```go
// internal/models/tenant_quota.go
package models

import (
    "time"
    "github.com/shopspring/decimal"
)

// TenantQuota 租户配额模型
type TenantQuota struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    TenantID        string          `json:"tenant_id" gorm:"type:varchar(50);not null;uniqueIndex:idx_tenant_resource"`
    ResourceType    string          `json:"resource_type" gorm:"type:varchar(50);not null;uniqueIndex:idx_tenant_resource"` // users, storage, api_calls, bandwidth
    QuotaLimit      int64           `json:"quota_limit" gorm:"not null"`
    QuotaUsed       int64           `json:"quota_used" gorm:"default:0"`
    QuotaUnit       string          `json:"quota_unit" gorm:"type:varchar(20);not null"` // count, bytes, requests
    ResetPeriod     string          `json:"reset_period" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
    LastResetAt     *time.Time      `json:"last_reset_at"`
    NextResetAt     *time.Time      `json:"next_reset_at"`
    IsActive        bool            `json:"is_active" gorm:"default:true"`
    AlertThreshold  decimal.Decimal `json:"alert_threshold" gorm:"type:decimal(5,2);default:80.00"` // 告警阈值百分比
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}

// TenantQuotaUsage 租户配额使用记录
type TenantQuotaUsage struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TenantID     string    `json:"tenant_id" gorm:"type:varchar(50);not null;index"`
    ResourceType string    `json:"resource_type" gorm:"type:varchar(50);not null"`
    UsageAmount  int64     `json:"usage_amount" gorm:"not null"`
    UsageDate    time.Time `json:"usage_date" gorm:"type:date;not null;index"`
    CreatedAt    time.Time `json:"created_at"`
}

// TenantLimit 租户限制模型
type TenantLimit struct {
    ID           uint   `json:"id" gorm:"primaryKey"`
    TenantID     string `json:"tenant_id" gorm:"type:varchar(50);not null;uniqueIndex:idx_tenant_limit_type"`
    LimitType    string `json:"limit_type" gorm:"type:varchar(50);not null;uniqueIndex:idx_tenant_limit_type"` // concurrent_users, file_size, request_rate
    LimitValue   int64  `json:"limit_value" gorm:"not null"`
    LimitUnit    string `json:"limit_unit" gorm:"type:varchar(20);not null"`
    IsActive     bool   `json:"is_active" gorm:"default:true"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

#### 4.2 配额检查中间件

```go
// pkg/middleware/quota.go
package middleware

import (
    "fmt"
    "net/http"
    "time"
    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
)

type QuotaMiddleware struct {
    db    *gorm.DB
    cache *redis.Client
}

// TenantQuotaCheck 租户配额检查中间件
func (qm *QuotaMiddleware) TenantQuotaCheck(resourceType string) gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID, exists := c.Get("tenant_id")
        if !exists {
            c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID not found"})
            c.Abort()
            return
        }

        // 检查配额
        if !qm.checkQuota(tenantID.(string), resourceType) {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Quota exceeded",
                "code":  "QUOTA_EXCEEDED",
                "resource_type": resourceType,
            })
            c.Abort()
            return
        }

        c.Next()

        // 请求完成后增加使用量
        go qm.incrementUsage(tenantID.(string), resourceType, 1)
    }
}

// checkQuota 检查配额是否超限
func (qm *QuotaMiddleware) checkQuota(tenantID, resourceType string) bool {
    // 从缓存获取配额信息
    cacheKey := fmt.Sprintf("quota:%s:%s", tenantID, resourceType)

    var quota models.TenantQuota
    if err := qm.db.Where("tenant_id = ? AND resource_type = ? AND is_active = true",
        tenantID, resourceType).First(&quota).Error; err != nil {
        // 如果没有配额限制，默认允许
        return true
    }

    // 检查是否需要重置配额
    if qm.shouldResetQuota(&quota) {
        qm.resetQuota(&quota)
    }

    // 检查配额使用情况
    return quota.QuotaUsed < quota.QuotaLimit
}

// incrementUsage 增加配额使用量
func (qm *QuotaMiddleware) incrementUsage(tenantID, resourceType string, amount int64) {
    // 更新数据库
    qm.db.Model(&models.TenantQuota{}).
        Where("tenant_id = ? AND resource_type = ?", tenantID, resourceType).
        UpdateColumn("quota_used", gorm.Expr("quota_used + ?", amount))

    // 记录使用记录
    usage := &models.TenantQuotaUsage{
        TenantID:     tenantID,
        ResourceType: resourceType,
        UsageAmount:  amount,
        UsageDate:    time.Now().Truncate(24 * time.Hour),
    }
    qm.db.Create(usage)

    // 检查是否需要告警
    qm.checkQuotaAlert(tenantID, resourceType)
}

// shouldResetQuota 检查是否需要重置配额
func (qm *QuotaMiddleware) shouldResetQuota(quota *models.TenantQuota) bool {
    if quota.NextResetAt == nil {
        return false
    }
    return time.Now().After(*quota.NextResetAt)
}

// resetQuota 重置配额
func (qm *QuotaMiddleware) resetQuota(quota *models.TenantQuota) {
    now := time.Now()
    var nextReset time.Time

    switch quota.ResetPeriod {
    case "daily":
        nextReset = now.AddDate(0, 0, 1).Truncate(24 * time.Hour)
    case "weekly":
        nextReset = now.AddDate(0, 0, 7).Truncate(24 * time.Hour)
    case "monthly":
        nextReset = now.AddDate(0, 1, 0).Truncate(24 * time.Hour)
    case "yearly":
        nextReset = now.AddDate(1, 0, 0).Truncate(24 * time.Hour)
    default:
        return
    }

    qm.db.Model(quota).Updates(map[string]interface{}{
        "quota_used":    0,
        "last_reset_at": &now,
        "next_reset_at": &nextReset,
    })
}

// checkQuotaAlert 检查配额告警
func (qm *QuotaMiddleware) checkQuotaAlert(tenantID, resourceType string) {
    var quota models.TenantQuota
    if err := qm.db.Where("tenant_id = ? AND resource_type = ?",
        tenantID, resourceType).First(&quota).Error; err != nil {
        return
    }

    usagePercent := float64(quota.QuotaUsed) / float64(quota.QuotaLimit) * 100
    threshold, _ := quota.AlertThreshold.Float64()

    if usagePercent >= threshold {
        // 发送告警
        qm.sendQuotaAlert(tenantID, resourceType, usagePercent)
    }
}
```

### 5. 数据库迁移脚本

#### 5.1 租户字段补全迁移

```sql
-- migrations/add_tenant_isolation.sql

-- 1. 为缺少tenant_id的表添加字段
DO $$
DECLARE
    table_record RECORD;
    column_exists BOOLEAN;
BEGIN
    -- 检查需要添加tenant_id的表
    FOR table_record IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        AND table_name NOT IN ('schema_migrations', 'tenant_audit_logs')
    LOOP
        -- 检查表是否已有tenant_id字段
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = table_record.table_name
            AND column_name = 'tenant_id'
        ) INTO column_exists;

        -- 如果没有tenant_id字段，则添加
        IF NOT column_exists THEN
            EXECUTE format('ALTER TABLE %I ADD COLUMN tenant_id VARCHAR(50) NOT NULL DEFAULT ''default''',
                table_record.table_name);
            EXECUTE format('CREATE INDEX idx_%I_tenant_id ON %I (tenant_id)',
                table_record.table_name, table_record.table_name);

            RAISE NOTICE 'Added tenant_id to table: %', table_record.table_name;
        END IF;
    END LOOP;
END $$;

-- 2. 创建租户管理相关表
CREATE TABLE IF NOT EXISTS tenants (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200),
    domain VARCHAR(100) UNIQUE,
    subdomain VARCHAR(50) UNIQUE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'deleted')),
    plan_type VARCHAR(50) DEFAULT 'basic',
    config JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

CREATE INDEX idx_tenants_status ON tenants (status);
CREATE INDEX idx_tenants_domain ON tenants (domain) WHERE domain IS NOT NULL;
CREATE INDEX idx_tenants_subdomain ON tenants (subdomain) WHERE subdomain IS NOT NULL;

-- 3. 创建租户配额表
CREATE TABLE IF NOT EXISTS tenant_quotas (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL,
    quota_limit BIGINT NOT NULL,
    quota_used BIGINT DEFAULT 0,
    quota_unit VARCHAR(20) NOT NULL,
    reset_period VARCHAR(20) NOT NULL,
    last_reset_at TIMESTAMP,
    next_reset_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    alert_threshold DECIMAL(5,2) DEFAULT 80.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, resource_type)
);

CREATE INDEX idx_tenant_quotas_tenant_id ON tenant_quotas (tenant_id);
CREATE INDEX idx_tenant_quotas_resource_type ON tenant_quotas (resource_type);

-- 4. 创建租户配额使用记录表
CREATE TABLE IF NOT EXISTS tenant_quota_usage (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    usage_amount BIGINT NOT NULL,
    usage_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_tenant_quota_usage_tenant_date ON tenant_quota_usage (tenant_id, usage_date);
CREATE INDEX idx_tenant_quota_usage_resource_date ON tenant_quota_usage (resource_type, usage_date);

-- 5. 创建租户限制表
CREATE TABLE IF NOT EXISTS tenant_limits (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    limit_type VARCHAR(50) NOT NULL,
    limit_value BIGINT NOT NULL,
    limit_unit VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, limit_type)
);

-- 6. 创建租户审计日志表
CREATE TABLE IF NOT EXISTS tenant_audit_logs (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50),
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(50),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    request_path VARCHAR(500) NOT NULL,
    request_method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time BIGINT NOT NULL,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_tenant_audit_logs_tenant_id ON tenant_audit_logs (tenant_id);
CREATE INDEX idx_tenant_audit_logs_user_id ON tenant_audit_logs (user_id);
CREATE INDEX idx_tenant_audit_logs_created_at ON tenant_audit_logs (created_at);
CREATE INDEX idx_tenant_audit_logs_action ON tenant_audit_logs (action);

-- 7. 创建跨租户访问尝试记录表
CREATE TABLE IF NOT EXISTS cross_tenant_access_attempts (
    id SERIAL PRIMARY KEY,
    request_tenant_id VARCHAR(50) NOT NULL,
    target_tenant_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50),
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(50),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    request_path VARCHAR(500) NOT NULL,
    is_blocked BOOLEAN DEFAULT true,
    block_reason VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_cross_tenant_access_request_tenant ON cross_tenant_access_attempts (request_tenant_id);
CREATE INDEX idx_cross_tenant_access_target_tenant ON cross_tenant_access_attempts (target_tenant_id);
CREATE INDEX idx_cross_tenant_access_created_at ON cross_tenant_access_attempts (created_at);

-- 8. 插入默认租户数据
INSERT INTO tenants (id, name, display_name, status, plan_type)
VALUES ('default', 'Default Tenant', '默认租户', 'active', 'enterprise')
ON CONFLICT (id) DO NOTHING;

-- 9. 为默认租户设置基础配额
INSERT INTO tenant_quotas (tenant_id, resource_type, quota_limit, quota_unit, reset_period) VALUES
('default', 'users', 10000, 'count', 'monthly'),
('default', 'storage', 107374182400, 'bytes', 'monthly'), -- 100GB
('default', 'api_calls', 1000000, 'count', 'monthly'),
('default', 'bandwidth', 1073741824000, 'bytes', 'monthly') -- 1TB
ON CONFLICT (tenant_id, resource_type) DO NOTHING;

-- 10. 创建行级安全策略（可选，用于额外保护）
-- 注意：这会影响性能，建议在应用层实现隔离
/*
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_policy ON users
    FOR ALL TO application_role
    USING (tenant_id = current_setting('app.current_tenant_id', true));
*/

COMMIT;
```

### 6. 实施步骤和时间计划

#### 6.1 第一周：基础设施搭建

**Day 1-2: 统一租户识别中间件**
- 实现`UnifiedTenantMiddleware`
- 集成到所有服务（ai-users、ai-agents、ai-llms、ai-tools）
- 测试多种租户识别方式

**Day 3-4: 数据库插件开发**
- 实现GORM租户插件
- 测试自动数据过滤功能
- 性能测试和优化

**Day 5-7: 数据库迁移**
- 执行租户字段补全迁移
- 创建租户管理相关表
- 数据一致性验证

#### 6.2 第二周：审计和配额系统

**Day 8-10: 审计系统实现**
- 实现审计日志中间件
- 创建审计日志存储和查询
- 跨租户访问检测

**Day 11-12: 配额系统实现**
- 实现配额检查中间件
- 配额使用统计和重置
- 配额告警机制

**Day 13-14: 集成测试**
- 端到端测试
- 性能测试
- 安全测试

### 7. 监控和告警

#### 7.1 关键监控指标

```yaml
# 监控指标配置
tenant_isolation_metrics:
  - name: tenant_data_access_total
    type: counter
    labels: [tenant_id, resource, action]
    description: "租户数据访问总数"

  - name: cross_tenant_access_attempts_total
    type: counter
    labels: [request_tenant, target_tenant, blocked]
    description: "跨租户访问尝试总数"

  - name: tenant_quota_usage_ratio
    type: gauge
    labels: [tenant_id, resource_type]
    description: "租户配额使用率"

  - name: tenant_query_performance_seconds
    type: histogram
    labels: [tenant_id, table_name]
    description: "租户查询性能"
```

#### 7.2 告警规则

```yaml
# 告警规则配置
alerts:
  - alert: CrossTenantAccessDetected
    expr: increase(cross_tenant_access_attempts_total[5m]) > 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "检测到跨租户访问尝试"

  - alert: TenantQuotaExceeded
    expr: tenant_quota_usage_ratio > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "租户配额使用率超过90%"

  - alert: TenantQueryPerformanceDegraded
    expr: histogram_quantile(0.95, tenant_query_performance_seconds) > 1.0
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "租户查询性能下降"
```

---

**文档状态**: 完整实施方案设计完成
**下一步**: 开始具体代码实现和测试
**预计完成时间**: 2周
**风险评估**: 低风险，向后兼容
