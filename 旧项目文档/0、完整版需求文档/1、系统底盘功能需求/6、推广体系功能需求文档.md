# 💰 AI生态平台推广体系功能需求文档

**文档版本**: v1.0  
**更新时间**: 2025年7月26日  
**功能模块**: ai-users推广体系功能  
**开发状态**: 100%已完成  
**负责团队**: ai-users开发团队  

---

## 📋 目录

1. [功能概述](#功能概述)
2. [核心功能需求](#核心功能需求)
3. [推广权限管理](#推广权限管理)
4. [佣金计算体系](#佣金计算体系)
5. [团队管理体系](#团队管理体系)


---

## 🎯 功能概述

### 1.1 功能定位
推广体系功能是AI生态平台的用户裂变获客引擎，通过2级分销模式、灵活的佣金机制、完善的团队管理，实现用户自主推广、降低获客成本、提升平台收入的核心目标。

### 1.2 核心价值
- **获客引擎**：用户裂变推广，降低平台获客成本
- **收入增长**：推广佣金激励，促进平台销售增长
- **用户粘性**：推广收益绑定，提升用户平台粘性
- **生态扩展**：推广网络建设，扩大平台影响力

### 1.3 功能边界
- ✅ 推广权限申请和审核管理
- ✅ 推广链接生成和追踪统计
- ✅ 2级分销佣金计算和发放
- ✅ 推广团队建设和管理
- ✅ 佣金提现和财务结算
- ❌ 税务处理（用户自行处理）
- ❌ 第三方推广平台对接（预留接口）

---

## 🔧 核心功能需求

### 2.1 推广链接管理功能

#### 2.1.1 推广链接生成
**功能描述**: 个性化推广链接的生成和管理

**链接类型**:
- **通用推广链接**: 平台首页推广链接，适用于全平台推广
- **商品推广链接**: 特定商品的推广链接，提高转化率
- **课程推广链接**: 特定课程的推广链接，精准推广
- **活动推广链接**: 特定活动的推广链接，限时推广

**链接特性**:
- **唯一标识**: 每个推广员有唯一的推广码
- **参数追踪**: 链接包含推广员ID、商品ID、活动ID等参数
- **多端适配**: 支持PC端、移动端、微信端等多端访问
- **有效期管理**: 支持永久有效和限时有效的链接设置

**链接格式示例**:
```
通用推广: https://demo.cees.cc?ref=ABC123
商品推广: https://demo.cees.cc/product/123?ref=ABC123
课程推广: https://demo.cees.cc/course/456?ref=ABC123
活动推广: https://demo.cees.cc/activity/789?ref=ABC123&utm_source=referral
```

#### 2.1.2 推广素材管理
**功能描述**: 推广素材的制作和分发管理

**素材类型**:
- **图片素材**: 商品图片、活动海报、品牌LOGO
- **文案素材**: 推广文案、商品介绍、活动说明
- **视频素材**: 产品演示、使用教程、推广视频
- **H5页面**: 推广落地页、活动页面、产品介绍页

**素材特点**:
- **个性化**: 自动嵌入推广员信息和推广码
- **多规格**: 适配不同平台的尺寸要求
- **实时更新**: 素材内容和价格信息实时更新
- **下载管理**: 素材下载次数统计和权限控制

#### 2.1.3 推广数据追踪
**功能描述**: 推广效果的全链路数据追踪

**追踪指标**:
- **访问数据**: 链接点击次数、访问用户数、访问时长
- **转化数据**: 注册转化率、购买转化率、复购率
- **收益数据**: 推广收益、佣金明细、提现记录
- **用户数据**: 推广用户画像、行为分析、价值评估

**数据维度**:
- **时间维度**: 按日、周、月、季度、年度统计
- **渠道维度**: 按推广渠道、推广方式分类统计
- **商品维度**: 按商品类型、价格区间分类统计
- **地域维度**: 按省市、地区分类统计

### 2.2 推广用户管理功能

#### 2.2.1 用户关系绑定
**功能描述**: 推广用户关系的建立和管理

**绑定规则**:
- **首次绑定**: 用户首次通过推广链接访问时自动绑定
- **永久绑定**: 绑定关系永久有效，不会因时间而失效
- **唯一绑定**: 每个用户只能绑定一个推广员
- **追溯绑定**: 支持用户注册后7天内的推广关系追溯

**绑定流程**:
1. **链接访问**: 用户点击推广链接访问平台
2. **参数解析**: 系统解析推广链接中的推广员信息
3. **关系记录**: 记录用户与推广员的绑定关系
4. **Cookie设置**: 设置推广追踪Cookie，有效期30天
5. **注册绑定**: 用户注册时自动建立推广关系
6. **关系确认**: 推广关系建立后发送确认通知

**绑定验证**:
- **IP检测**: 防止同一IP大量注册刷佣金
- **设备检测**: 基于设备指纹的异常检测
- **行为分析**: 用户行为的真实性分析
- **人工审核**: 可疑推广关系的人工审核

#### 2.2.2 推广用户分类
**功能描述**: 推广用户的分类管理和标签化

**用户分类**:
- **直推用户**: 直接通过推广链接注册的一级用户
- **间推用户**: 通过直推用户推广注册的二级用户
- **活跃用户**: 注册后有实际使用行为的用户
- **付费用户**: 注册后有付费购买行为的用户
- **流失用户**: 注册后长期未活跃的用户

**用户标签**:
- **价值标签**: 高价值、中价值、低价值用户
- **行为标签**: 学习型、购买型、推广型用户
- **偏好标签**: 课程偏好、商品偏好、价格偏好
- **状态标签**: 新用户、活跃用户、沉睡用户

---

## 🔐 推广权限管理

### 3.1 推广员申请审核

#### 3.1.1 申请条件设置
**功能描述**: 推广员申请的条件配置和管理

**基础条件**:
- **注册时长**: 注册满7天以上
- **实名认证**: 完成实名认证验证
- **账户状态**: 账户状态正常，无违规记录
- **活跃度**: 最近30天内有登录和使用记录

**进阶条件**:
- **消费记录**: 有过平台消费记录（可选）
- **学习记录**: 完成过课程学习（可选）
- **社区贡献**: 有过社区互动和贡献（可选）
- **推荐人**: 有现有推广员推荐（可选）

**申请材料**:
- **个人信息**: 真实姓名、联系方式、身份证号
- **推广计划**: 推广渠道、推广方式、预期目标
- **推广经验**: 相关推广经验和成功案例
- **承诺书**: 遵守推广规则的承诺书

#### 3.1.2 审核流程管理
**功能描述**: 推广员申请的审核流程和管理

**审核流程**:
1. **申请提交**: 用户提交推广员申请和相关材料
2. **自动初审**: 系统自动检查基础条件和材料完整性
3. **人工复审**: 人工审核申请材料和推广计划
4. **背景调查**: 核实申请人身份和推广经验
5. **审核决定**: 做出通过、拒绝或补充材料的决定
6. **结果通知**: 通知申请人审核结果和后续步骤
7. **权限开通**: 审核通过后开通推广权限

**审核标准**:
- **真实性**: 申请信息的真实性和完整性
- **合规性**: 推广计划的合规性和可行性
- **专业性**: 推广经验和能力的专业性
- **诚信度**: 申请人的诚信记录和信用状况

### 3.2 推广权限等级

#### 3.2.1 三级权限体系
**功能描述**: 基于能力和业绩的推广权限等级

**基础推广员**:
- **申请条件**: 满足基础申请条件即可
- **推广权限**: 通用推广链接、基础推广素材
- **佣金比例**: 一级30%、二级10%
- **提现限制**: 最低100元起提，2%手续费
- **团队规模**: 最多管理50人团队

**高级推广员**:
- **申请条件**: 推广满3个月，团队20人以上
- **推广权限**: 全部推广链接、高级推广素材
- **佣金比例**: 一级35%、二级15%
- **提现限制**: 最低50元起提，1%手续费
- **团队规模**: 最多管理200人团队

**企业推广员**:
- **申请条件**: 推广满6个月，团队100人以上
- **推广权限**: 定制推广方案、专属推广素材
- **佣金比例**: 一级40%、二级20%
- **提现限制**: 无最低限制，免手续费
- **团队规模**: 无限制团队规模

#### 3.2.2 权限升级机制
**功能描述**: 推广员权限等级的升级和降级

**升级条件**:
- **业绩要求**: 达到相应等级的业绩要求
- **团队要求**: 达到相应等级的团队规模
- **时长要求**: 满足相应等级的推广时长
- **评估要求**: 通过平台的综合评估

**升级流程**:
1. **条件检查**: 系统自动检查升级条件
2. **申请提交**: 推广员提交升级申请
3. **业绩审核**: 审核推广业绩和团队数据
4. **综合评估**: 综合评估推广能力和表现
5. **升级决定**: 做出升级或继续观察的决定
6. **权限调整**: 升级成功后调整相应权限

---

## 💰 佣金计算体系

### 4.1 佣金计算规则

#### 4.1.1 2级分销模式
**功能描述**: 2级分销的佣金计算和分配

**分销层级**:
- **一级分销**: 直接推广用户的消费佣金
- **二级分销**: 间接推广用户的消费佣金
- **层级限制**: 最多2级，不支持3级及以上分销

**佣金比例**:
- **基础推广员**: 一级30%、二级10%
- **高级推广员**: 一级35%、二级15%
- **企业推广员**: 一级40%、二级20%

**计算公式**:
```
一级佣金 = 订单金额 × 一级佣金比例
二级佣金 = 订单金额 × 二级佣金比例
推广员佣金 = 一级佣金 + 二级佣金
```

#### 4.1.2 佣金计算时机
**功能描述**: 佣金计算和确认的时机管理

**计算时机**:
- **订单支付**: 用户支付订单后立即计算佣金
- **佣金冻结**: 佣金先进入冻结状态，等待确认
- **佣金确认**: 订单完成后佣金转为可提现状态
- **佣金失效**: 订单退款后相应佣金失效扣除

**确认条件**:
- **数字商品**: 购买后立即确认佣金
- **实物商品**: 确认收货后确认佣金
- **服务商品**: 服务完成后确认佣金
- **课程商品**: 开始学习后确认佣金

#### 4.1.3 特殊佣金规则
**功能描述**: 特殊情况下的佣金计算规则

**促销活动佣金**:
- **折扣商品**: 按折扣后价格计算佣金
- **满减活动**: 按满减后价格计算佣金
- **优惠券**: 按优惠券抵扣后价格计算佣金
- **积分抵扣**: 按积分抵扣后价格计算佣金

**退款处理佣金**:
- **全额退款**: 扣除全部相关佣金
- **部分退款**: 按比例扣除相关佣金
- **换货处理**: 不影响已确认佣金
- **售后服务**: 不影响已确认佣金

### 4.2 佣金发放管理

#### 4.2.1 佣金账户管理
**功能描述**: 推广员佣金账户的管理

**账户类型**:
- **可提现余额**: 已确认可以提现的佣金
- **冻结余额**: 暂时冻结等待确认的佣金
- **累计收入**: 历史累计获得的佣金总额
- **累计提现**: 历史累计提现的佣金总额

**账户操作**:
- **余额查询**: 实时查询各类佣金余额
- **明细查询**: 查询佣金收入和支出明细
- **冻结解冻**: 佣金的冻结和解冻操作
- **调整记录**: 人工调整的记录和说明

#### 4.2.2 提现管理系统
**功能描述**: 佣金提现的申请和处理

**提现规则**:
- **最低金额**: 基础推广员100元起提
- **手续费**: 基础推广员2%手续费
- **提现周期**: T+3个工作日到账
- **提现次数**: 每月最多提现4次

**提现流程**:
1. **提现申请**: 推广员提交提现申请
2. **身份验证**: 支付密码或短信验证
3. **风险检测**: 系统风险评估和异常检测
4. **人工审核**: 大额提现的人工审核
5. **财务处理**: 财务部门处理转账
6. **到账确认**: 确认到账并更新状态

**提现方式**:
- **银行卡**: 绑定银行卡直接转账
- **支付宝**: 绑定支付宝账户转账
- **微信**: 绑定微信账户转账（企业推广员）
- **对公账户**: 企业推广员对公账户转账

---

## 👥 团队管理体系

### 5.1 团队建设功能

#### 5.1.1 团队招募管理
**功能描述**: 推广团队的招募和建设

**招募方式**:
- **邀请链接**: 生成专属邀请链接招募团队成员
- **邀请码**: 生成邀请码供线下招募使用
- **二维码**: 生成二维码供扫码加入团队
- **直接邀请**: 直接邀请平台用户加入团队

**招募激励**:
- **招募奖励**: 成功招募团队成员获得奖励
- **团队奖励**: 团队达到一定规模获得奖励
- **业绩奖励**: 团队业绩达标获得额外奖励
- **排名奖励**: 团队排名靠前获得排名奖励

#### 5.1.2 团队层级管理
**功能描述**: 团队成员的层级关系管理

**层级结构**:
```
团队长 (推广员)
├── 直属成员 (一级团队)
│   ├── 成员A
│   ├── 成员B
│   └── 成员C
└── 间接成员 (二级团队)
    ├── 成员A的团队
    ├── 成员B的团队
    └── 成员C的团队
```

**层级权限**:
- **团队长**: 查看全团队数据，管理团队成员
- **一级成员**: 查看自己和下级数据，管理下级成员
- **二级成员**: 只能查看自己的数据和业绩

### 5.2 团队数据统计

#### 5.2.1 团队业绩统计
**功能描述**: 团队整体业绩的统计分析

**统计指标**:
- **团队规模**: 团队总人数、一级人数、二级人数
- **团队业绩**: 团队总业绩、人均业绩、业绩增长率
- **团队活跃**: 活跃成员数、活跃率、留存率
- **团队收入**: 团队总收入、人均收入、收入增长率

**统计维度**:
- **时间维度**: 日、周、月、季、年统计
- **成员维度**: 按成员、层级、加入时间统计
- **业务维度**: 按商品、课程、服务类型统计
- **地域维度**: 按省市、地区分布统计

#### 5.2.2 团队排行榜
**功能描述**: 团队和成员的排行榜系统

**排行类型**:
- **业绩排行**: 按业绩金额排名
- **人数排行**: 按团队人数排名
- **增长排行**: 按增长率排名
- **活跃排行**: 按活跃度排名

**排行奖励**:
- **月度排行**: 月度排行前10名获得奖励
- **季度排行**: 季度排行前5名获得大奖
- **年度排行**: 年度排行冠军获得特别奖励
- **专项排行**: 特定活动的专项排行奖励

---

## 💾 数据模型设计


## 🔌 API接口规范


## ✅ 验收标准

### 8.1 功能验收
- [ ] 完整的推广员申请和审核流程
- [ ] 准确的2级分销佣金计算
- [ ] 灵活的推广链接生成和追踪
- [ ] 完善的团队管理和数据统计
- [ ] 安全的佣金提现和财务结算

### 8.2 性能验收
- [ ] 推广链接访问响应时间 < 200ms
- [ ] 佣金计算准确性100%
- [ ] 数据统计查询响应时间 < 500ms
- [ ] 支持10000+推广员并发操作
- [ ] 提现处理时效T+3个工作日

### 8.3 安全验收
- [ ] 推广关系绑定防刷机制
- [ ] 佣金计算防篡改机制
- [ ] 提现申请安全验证
- [ ] 异常推广行为监控
- [ ] 推广数据隐私保护

### 8.4 业务验收
- [ ] 推广转化率达到预期目标
- [ ] 佣金发放及时准确
- [ ] 推广员满意度良好
- [ ] 平台获客成本降低
- [ ] 推广网络持续扩大

---

**文档结束** - AI生态平台推广体系功能需求文档 v1.0
