# 🎯 AI生态平台全场景智能营销功能-需求文档 V2.0

**文档版本**: v2.0
**更新时间**: 2025年7月26日
**功能模块**: ai-users全场景智能营销功能模块    
**开发状态**: 重新设计开发中
**负责团队**: ai-users开发团队

---

## 📋 目录

1. [一、功能概述](#一、功能概述)    
2. [二、核心功能需求](#二、核心功能需求)

---

## 一、功能概述

### 1.1 功能定位
全场景智能营销功能，是AI生态平台运营的核心枢纽，通过三层组装架构（配额组装→权益包组装→产品组装），将4大业务功能进行灵活组合，形成高度自定义和多样化的营销产品。

**核心设计原则**: 单体配额→同业务权益包→跨业务产品组合

### 1.2 核心价值
- **营销枢纽**：连接4大业务体系，形成统一的营销产品
- **灵活组装**：三层架构支持高度自定义的产品组合和定价策略
- **场景覆盖**：为不同用户提供不同的解决方案
- **收入增长**：通过产品组合最大化平台收入

### 1.3 功能边界
- ✅ 4大业务单体配额的组装管理
- ✅ 把同业务的不同配额组装为不同的权益包
- ✅ 把不同的权益包组合成产品，即客户解决方案
- ✅ 组合的产品将自动同步到商城进行销售（同步后由商城做上架下架处理）
- ❌ 具体业务功能实现（由各业务体系负责）
- ❌ 支付处理（由商城体系负责）

---

## 二、核心功能需求

### 2.1 配额组装层功能

**功能描述**: 把平台4大核心业务进行单体配额组装，为第二层权益包组装提供配额模板基础
**设计思路**: 配额组装是全场景智能营销体系三层架构的第一层，将4个具体业务功能（课程、智能体、AI算力、AI插件）的资源配额抽象为可配置的单体模板，支持灵活组合营销。这一层不直接面向用户，而是为第二层权益包组装提供配额模板基础。
**核心原则**: 所有配额都需要明确的配额源，以单个形态进行配额设置，每个配额模板只对应一个具体的业务资源。配额模板一旦被权益包引用，不允许删除，只能修改名称和描述。

**配额组合对象**:
- **课程业务** (ai-users课程体系提供)
- **智能体业务** (ai-agents服务提供)
- **AI算力业务** (ai-llms服务提供)
- **AI插件业务** (ai-tools服务提供)

#### 2.1.1 配额组装流程图
```
业务资源选择 → 配额类型设置 → 配额参数配置 → 成本核算 → 保存为配额模板 → 发布至权益包层
     ↓            ↓            ↓           ↓          ↓              ↓        
选择4大业务   设置配额类型    配置具体参数   计算成本    效果预览        权益包组装
具体资源      时长/次数/价格  数量/时间等    成本分析    预览确认       发布至权益包层
```

#### 2.1.2 配额组装具体功能

##### 2.1.2.1 配额自定义分类设置

**功能描述**: 管理员可自定义配额分类，如基础配额、高级配额、专业配额等分类
**具体需求**:
- 管理员可自定义分类名称和描述
- 分类名称必须唯一
- 分类描述可选
- 支持按业务类型分类
- 分类排序功能

##### 2.1.2.2 配额设计器

**功能描述**: 可视化的单体配额组装工具，支持配额参数配置
**具体需求**:
- **业务资源选择**: 从4大业务中选择具体的业务资源
- **配额类型设置**: 根据业务特点设置配额类型（时长配额、次数配额、买断配额、使用量配额、价格配额等）
- **配额参数配置**: 配置配额的具体参数（数量、时间、限制条件等）
- **实时预览**: 实时展示配额模板的具体内容和配置。实时计算配置的成本和价值。实时预览配额在权益包中的展示效果
- **成本核算**: 根据每个实际产品设置的成本价，实时计算出配额的成本。比如上传课程时设置的成本价是10元/年，那么配额2年就是20元的成本

##### 2.1.2.3 配额列表

###### 2.1.2.3.1 配额创建

**功能描述**: 在配额列表页面右上角，有一个创建配额按钮
**具体需求**:
点击创建配额→进入配额设计器

###### 2.1.2.3.2 配额列表展示信息

**功能描述**: 配额列表展示配额的基本信息
**具体需求**:
- 配额名称
- 业务类型（课程、智能体、AI算力、AI插件）
- 配额类型（时长配额、次数配额、买断配额、使用量配额、价格配额等）
- 配额分类
- 配额描述
- 配额状态（未引用、已引用、已废弃）
- 配额成本（元/次、元/月、元/年、元/次/月、元/次/年等）
- 配额创建时间
- 配额更新时间
- 配额引用数量
- 操作管理区（详情按钮 操作按钮）【操作按钮下拉显示：编辑、复制、删除、废弃】

###### 2.1.2.3.3 配额操作管理（对应操作管理区的操作按钮）

**功能描述**: 配额的全生命周期管理
**具体需求**:
- **配额编辑**: 支持配额内容的修改和调整，包括配额参数、成本核算等
- **配额复制**: 基于现有配额创建新的配额模板
- **配额操作**: 编辑、复制、删除、废弃操作
- **配额状态**: 未引用、已引用、已废弃

**具体状态说明**：
- ✅ **未引用**：配额模板未被任何权益包引用，可执行所有操作（编辑、复制、删除、废弃）
- ✅ **已引用**：配额模板被权益包引用中，可执行编辑基础信息（名称、分类、描述）和复制操作，不可执行删除、废弃操作。修改后自动同步到引用该配额的权益包中。
- ✅ **已废弃**：配额模板已废弃，仅保留查看权限，不可执行任何修改操作，以维护历史数据完整性。

**废弃操作判断流程**：
1. 检查配额是否被权益包引用 → 如果被引用，提示"配额正在被权益包使用，无法废弃"
2. 检查配额相关订单状态 → 如果存在未完成的订单，提示"存在未完成订单，无法废弃"
3. 满足废弃条件 → 执行废弃操作，状态变更为"已废弃"

废弃流程图
```
点击废弃 → 检查是否被引用 → 检查是否有未完成订单 → 执行废弃操作
    ↓          ↓                 ↓                ↓
废弃按钮    如果被引用        如果有未完成订单   更新状态为已废弃
               ↓                 ↓
           提示无法废弃       提示无法废弃
           被权益包引用       存在未完成订单
```

**状态转换规则**：
- 未引用 → 已引用：当配额被权益包引用时自动转换
- 已引用 → 未引用：当所有引用关系被取消时自动转换
- 已引用 → 已废弃：满足废弃条件时可手动转换
- 未引用 → 已废弃：满足废弃条件时可手动转换
- 已废弃状态为终态，不可逆转

**废弃条件分级设计**
✅ 可以废弃的情况：
- 所有引用该配额的权益包都已废弃
- 所有相关订单都已完成或取消（无进行中订单）
- 无未来生效的定时任务或预约订单
- 配额对应的底层资源已下线或不可用

❌ 不可废弃的情况：
- 存在活跃状态的权益包引用
- 存在未完成的订单（待支付、进行中、待交付）
- 存在未来生效的订单或任务
- 配额正在被系统任务使用

###### 2.1.2.3.4 配额详情（对应操作管理区的详情按钮）

**功能描述**: 配额详情展示配额的详细信息
**具体需求**:
- 配额名称
- 业务类型
- 配额类型
- 配额分类
- 配额描述
- 配额状态（未引用、已引用、已废弃）
- 配额参数详情
- 配额成本详情
- 配额技术规格
- 配额创建时间
- 配额引用详情
- 配额使用统计

##### 2.1.2.3.5 配额筛选和搜索
**功能描述**: 管理员可根据多种条件筛选和搜索配额
**具体需求**:
- **业务类型筛选**: 管理员可根据业务类型筛选配额
- **配额类型筛选**: 管理员可根据配额类型筛选配额
- **分类筛选**: 管理员可根据配额分类筛选配额
- **状态筛选**: 管理员可根据配额状态筛选配额
- **名称搜索**: 管理员可根据配额名称搜索配额
- **成本区间筛选**: 管理员可根据配额成本区间筛选配额
- **创建日期筛选**: 管理员可根据配额创建日期筛选配额


#### 2.1.3 配额组装示例

##### 课程业务配额（连通课程体系）

```json
{
  "课程配额设计": {
      "配额来源": "从课程中心已上传的所有课程中选择",
      "选择数量": "每个配额模板只能选择1门课程进行配额",
      "配额示例": "从课程列表100门课程中选择1门课程进行时间配额",
      "配额类型=时间": {
        "按天设置": "自定义天数",
        "按年设置": "自定义年数",
        "永久权限": "永久可学习该配额内的课程"
      }
    },

    "配额模板示例": {
      "配额模板一：AI基础课程时长配额": {
        "配额类型": "时间",
        "配置课程": ["人工智能概论"],
        "学习时长": "180天",
        "配额描述": "AI入门必学课程，180天学习期限",
        "成本核算": "¥20"
      },
      "配额模板二：大模型专业课程时长配额": {
        "配额类型": "时间",
        "配置课程": ["模型微调实战"],
        "学习时长": "永久",
        "配额描述": "大模型专业课程，永久学习权限",
        "成本核算": "¥50"
      },
      "配额模板三：智能体开发课程时长配额": {
        "配额类型": "时间",
        "配置课程": ["智能体商业应用"],
        "学习时长": "2年",
        "配额描述": "智能体开发全套课程，2年学习期限",
        "成本核算": "¥35"
      },
    }
}
```

##### 智能体业务配额（连通ai-agents服务）

```json
{
  "智能体配额设计": {
    "配额来源": "从智能体市场选择已审核并上架的智能体",
    "选择数量": "每个配额模板只能选择1个智能体进行配额",
    "配额示例": "从智能体列表100个智能体中选择1个智能体进行时间配额",
    "配额类型=使用时长配额+使用次数配额+买断配额": {
      "使用时长配额": {
        "按天设置": "自定义天数",
        "按年设置": "自定义年数",
        "永久权限": "永久可使用该配额内的智能体"
      },
      "使用次数配额": {
        "每天对话次数": "自定义次数",
        "每月对话次数": "自定义次数",
      },
      "买断配额": {
        "买断智能体源码": "一次性买断智能体源码所有权",
        "配额内容": "授权单个智能体可以买断源码",
        "买断权限": "获得智能体完整JSON源码",
        "平台支持": "支持Coze、Dify、n8n三个平台的智能体买断"
      }
    }
  },

    "使用时长配额示例": {
      "文案润色智能体使用时长配额": {
        "配额类型": "使用时长配额",
        "配额智能体": ["文案润色智能体"],
        "授权时长": "1年",
        "配额描述": "文案润色智能体，1年使用权限",
        "成本核算": "¥12"
      },
      "PPT制作智能体使用时长配额": {
        "配额类型": "使用时长配额",
        "配额智能体": ["PPT制作智能体"],
        "授权时长": "永久",
        "配额描述": "PPT制作智能体，永久使用权限",
        "成本核算": "¥25"
      }
    },

    "使用次数配额示例": {
      "文案润色智能体次数配额": {
        "配额类型": "次数配额",
        "配额智能体": ["文案润色智能体"],
        "对话次数": "1000次/月",
        "配额描述": "文案润色智能体，每月1000次对话",
        "成本核算": "¥8"
      },
      "PPT制作智能体次数配额": {
        "配额类型": "次数配额",
        "配额智能体": ["PPT制作智能体"],
        "对话次数": "500次/月",
        "配额描述": "PPT制作智能体，每月500次对话",
        "成本核算": "¥15"
      },
    }

    "买断配额示例": {
      "文案润色智能体买断配额": {
        "配额类型": "买断配额",
        "配额智能体": ["文案润色智能体"],
        "买断权限": "获得完整JSON源码，可在对应平台自由使用",
        "配额描述": "文案润色智能体买断权限，获得源码所有权",
        "成本核算": "¥50"
      },
      "PPT制作智能体买断配额": {
        "配额类型": "买断配额",
        "配额智能体": ["PPT制作智能体"],
        "买断权限": "获得完整JSON源码，可在Coze平台自由使用",
        "配额描述": "PPT制作智能体买断权限，获得源码所有权",
        "成本核算": "¥100"
      },
    }
  
}
```

##### AI算力业务配额（连通ai-llms服务）

```json
{
  "算力配额设计": {
    "配额来源": "已对接AI大模型渠道，并上线到算力中心的模型列表",
    "选择数量": "每个配额模板只能选择1个大模型进行配额，比如选择deepseekV3这个模型",
    "配额示例": "选择deepseekV3这个模型进行Token总量配额",
    "配额类型=Token总量配额 + 调用次数配额": {
      "Token总量配额": "按Token数量限制模型使用量",
      "调用次数配额": "按调用次数限制模型使用量"
      },
    }
    
    "Token总量配额示例": {
      "Claude模型Token总量配额": {
        "配额类型": "Token总量配额",
        "配额模型": ["Claude-4-sonnet"],
        "Token数量": "50万Token/月",
        "Token数量描述": "Claude-4-sonnet模型，每月50万Token",
        "成本核算": "¥8"
      },
      "ChatGPT-4o模型Token总量配额": {
        "配额类型": "Token总量配额",
        "配额模型": ["ChatGPT-4o"],
        "Token数量": "100万Token/月",
        "Token数量描述": "ChatGPT-4o模型，每月100万Token",
        "成本核算": "¥12"
      },
    }

    "调用次数配额示例": {
      "Claude模型调用次数配额": {
        "配额类型": "调用次数配额",
        "配额模型": ["Claude-4-sonnet"],
        "调用次数": "500次/月",
        "调用次数描述": "Claude-4-sonnet模型，每月500次调用",
        "成本核算": "¥8"
      },
      "ChatGPT-4o模型调用次数配额": {
        "配额类型": "调用次数配额",
        "配额模型": ["ChatGPT-4o"],
        "调用次数": "1000次/月",
        "调用次数描述": "ChatGPT-4o模型，每月1000次调用",
        "成本核算": "¥12"
      },
    }
}

```

##### AI插件业务配额（连通ai-tools服务）

```json
{
  "插件配额设计": {
    "配额来源": "从AI插件市场已上架的插件中选择",
    "选择数量": "每个配额模板只能选择1个插件进行配额",
    "配额示例": "选择PDF解析插件进行使用配额",    
    "配额类型 = 使用时间配额 + 调用价格配额": {
      "使用时间配额": "按时间长度授权插件使用权限", {
        "按天设置": "自定义天数内免费使用",
        "按年设置": "自定义年数内免费使用",
        "永久权限": "永久可使用该插件"},
      "调用价格配额": "按单次调用价格授权插件使用，可自定义每个不同插件的调用价格，调用时需要实时扣除客户资产里的余额",
      },
    }

    "使用时间配额示例": {
      "PDF解析插件使用时间配额": {
        "配额类型": "使用时间配额",
        "配额插件": ["PDF解析插件"],
        "授权时长": "1年",
        "配额描述": "PDF解析插件，1年使用权限",
        "成本核算": "¥5"},

      "短视频自动发布插件使用时间配额": {
        "配额类型": "使用时间配额",
        "配额插件": ["短视频自动发布插件"],
        "授权时长": "永久",
        "配额描述": "短视频自动发布插件，永久使用权限",
        "成本核算": "¥55"},
    }

    "调用价格配额示例": {      
      "PDF解析插件调用价格配额": {
        "配额类型": "调用价格配额",
        "配额插件": ["PDF解析插件"],
        "调用价格": "¥0.1/次",
        "配额描述": "PDF解析插件，每次调用0.1元",
        "成本核算": "¥0.1"},
      "抖音视频链接下载调用价格配额": {
        "配额类型": "调用价格配额",
        "配额插件": ["抖音视频链接下载插件"],
        "调用价格": "¥0.1/次",
        "配额描述": "抖音视频链接下载插件，每次调用0.1元",
        "成本核算": "¥0.1"},
    }
}
```


### 2.2 权益包组装层功能

**功能描述**: 基于第一层配额组装的配额模板，进行第二层权益包的组装设计
**设计思路**: 权益包组装是全场景智能营销体系三层架构的第二层，将第一层的4大业务配额模板进行灵活组合，形成面向不同用户场景的权益包。这一层不直接面向用户，而是为第三层产品组装提供权益包基础。
**核心原则**: 权益包必须基于已发布的配额模板进行组装，只能对同业务的配额模板进行组合成权益包。

**权益包组合对象**:
- **课程配额模板** (来自第一层配额组装模板)
- **智能体配额模板** (来自第一层配额组装模板)  
- **AI算力配额模板** (来自第一层配额组装模板)
- **AI插件配额模板** (来自第一层配额组装模板)

#### 2.2.1 权益包组装流程图
```
配额模板选择 → 拖拽组装  → 冲突检测  → 保存为权益包 → 发布至产品层
     ↓          ↓          ↓           ↓              ↓        
选择4大业务   可视化组装   自动检测     效果预览        产品组装
配额模板      实时预览     冲突检测     预览确认       发布至产品层
```

#### 2.2.2 权益包组装具体功能

##### 2.2.2.1 权益包自定义分类设置

**功能描述**: 管理员可自定义权益包分类，如学习、创业、办公、企业等场景分类
**具体需求**:
- 管理员可自定义分类名称和描述
- 分类名称必须唯一
- 分类描述可选

##### 2.2.2.2 权益包设计器

**功能描述**: 可视化的权益包组装工具，支持拖拽式配额模板组合
**具体需求**:
- **业务类型限制**: 只能对同业务的配额模板进行组合成权益包
- **配额模板选择**: 从已发布的配额模板库中选择需要的配额模板
- **拖拽组装界面**: 通过拖拽方式将不同业务的配额模板组合到权益包中
- **实时预览**: 实时展示权益包包含的所有配额模板和具体配额。实时计算权益包的总成本和各项成本分解
- **冲突检测**: 检测同业务不同配额模板之间是否存在资源冲突。检测业务规则之间是否存在逻辑冲突。

##### 2.2.2.3 权益包列表

###### 2.2.2.3.1 权益包创建
**功能描述**: 在权益包列表页面右上角，有一个创建权益包按钮
**具体需求**:
点击创建权益包→进入权益包设计器

###### 2.2.2.3.2 权益包列表展示信息
**功能描述**: 权益包列表展示权益包的基本信息
**具体需求**:
- 权益包名称
- 创建日期
- 权益包分类
- 权益包描述
- 权益包状态（未引用、已引用、已废弃）
- 权益包创建时间
- 权益包更新时间
- 权益包引用数量
- 操作管理区（详情按钮  操作按钮）【操作按钮下拉显示：编辑、上架、下架、删除、废弃】

###### 2.2.2.3.3 权益包操作管理（对应的是操作管理区）

**功能描述**: 权益包的全生命周期管理
**具体需求**:
- **权益包编辑**: 支持权益包内容的修改和调整，包括增加、删除或替换权益包中的配额模板等
- **权益包操作**: 修改、上架、下架、删除、废弃操作（发布后在产品组装来源列表中可见，当配额模板在产品组装被引用时，不可删除.上架.下架.废弃操作，只能修改标题、分类、描述等，修改后同步显示在产品组装来源列表中，针对已发布的产品组装的权益包，也要同步修改（即当该权益包被产品组装成产品并发布到商城时，修改后的标题分类描述等也要自动同步修改）。当权益包没有任何引用时可执行所有操作）
- **权益包状态**: 未引用、已引用、已废弃

###### 2.2.2.3.4 权益包详情（对应的是操作管理区）
**功能描述**: 权益包详情展示权益包的详细信息
**具体需求**:
- 权益包名称
- 权益包分类
- 权益包描述
- 权益包状态（未引用、已引用、已废弃）
- 权益包创建时间
- 权益包引用详情
- 权益包包含的配额模板

##### 2.2.2.4 权益包筛选
**功能描述**: 管理员可根据分类筛选权益包
**具体需求**:
- **分类筛选**: 管理员可根据分类筛选权益包
- **状态筛选**: 管理员可根据权益包状态筛选权益包
- **名称筛选**: 管理员可根据权益包名称筛选权益包
- **创建日期筛选**: 管理员可根据权益包创建日期筛选权益包

#### 2.2.3 权益包组装示例

- **课程权益包**:
  - 权益包一：大模型专业包（AI基础课程配额+大模型专业课程配额，成本¥70）
  - 权益包二：编程入门包（AI基础课程配额+智能体开发课程配额，成本¥55）

- **智能体权益包**:
  - 权益包三：学习助手包（学习助手智能体配额+文案润色智能体配额，成本¥22）
  - 权益包四：内容创作包（文案润色智能体配额+PPT制作智能体配额，成本¥37）
  - 权益包五：智能体买断包（文案润色智能体买断配额+PPT制作智能体买断配额，成本¥150）

- **算力权益包**:
  - 权益包六：基础算力包（GPT-3.5配额+文心一言配额，成本¥13）
  - 权益包七：高级算力包（GPT-4配额+Claude配额，成本¥37）
  - 权益包八：全模型包（基础模型配额+高级模型配额，成本¥20）

- **插件权益包**:
  - 权益包九：学习工具包（文档处理插件配额+代码生成插件基础配额，成本¥13）
  - 权益包十：商业插件包（数据分析插件配额+图片生成插件配额，成本¥25）
  - 权益包十一：专业插件包（代码生成插件高级配额+数据分析插件配额，成本¥30）





### 2.3 产品组装层功能

**功能描述**: 基于第二层权益包组装的权益包，进行第三层营销产品的最终组装和包装
**设计思路**: 产品组装是全场景智能营销体系三层架构的第三层，将第二层的不同业务权益包进行跨业务组合，添加定价策略、营销素材、推广策略等商业元素，形成直接面向用户的营销产品。这一层是用户直接接触和购买的产品层。
**核心原则**: 营销产品必须基于已发布的权益包进行组装，支持跨业务权益包组合，具备完整的商业化属性和营销策略。已发布到商城的产品不允许修改核心配置，只能修改营销属性，保护用户购买体验的一致性。

**产品组合对象**:
- **课程权益包** (来自第二层权益包组装)
- **智能体权益包** (来自第二层权益包组装)  
- **AI算力权益包** (来自第二层权益包组装)
- **AI插件权益包** (来自第二层权益包组装)

#### 2.3.1 产品组装流程图
```
权益包选择 → 跨业务组装 → 定价策略 → 营销包装 → 提交审核 → 商城发布
     ↓          ↓          ↓         ↓         ↓         ↓        
选择不同业务  可视化组装   价格设置   包装设计   管理员审核  发布至商城
权益包组合    实时预览     促销策略   营销素材   审核通过    用户购买
```

#### 2.3.2 产品组装具体功能

##### 2.3.2.1 产品自定义分类设置功能

**功能描述**: 管理员可自定义产品分类，如学习成长、创业赚钱、效率办公、企业解决方案等产品线分类
**具体需求**:
- 管理员可自定义分类名称和描述
- 分类名称必须唯一
- 分类描述可选
- 支持分类图标上传
- 分类排序功能

##### 2.3.2.2 产品设计器功能

**功能描述**: 可视化的跨业务产品组装工具，支持拖拽式权益包组合
**具体需求**:
- **跨业务组合**: 填写权益包名称，然后选择不同业务类型的权益包进行组合
- **权益包选择**: 从已发布的权益包库中选择需要的权益包
- **拖拽组装界面**: 通过拖拽方式将不同业务的权益包组合到产品中
- **实时预览**: 实时展示产品包含的所有权益包和具体权益。实时计算产品的总成本和各项成本分解。实时预览用户在商城看到的产品详情展示效果
- **价值计算**: 自动计算产品的总价值和建议售价
- **价格设置**: 支持设置产品的标准售价
- **利润分析**: 当输入产品定价后，实时显示当前定价下的利润率情况（每个具体的业务产品，会在具体的功能层设置成本价，比如课程上架时会设置成本价，这里直接调用并实时计算即可）

##### 2.3.2.3 产品列表页功能

###### 2.3.2.3.1 产品创建
**功能描述**: 在产品列表页面右上角，有一个创建产品按钮
**具体需求**:
点击创建产品→进入产品设计器

###### 2.3.2.3.2 产品列表展示信息
**功能描述**: 产品列表展示产品的基本信息
**具体需求**:
- 产品名称
- 产品封面图
- 产品分类
- 产品描述
- 产品状态（草稿、待审核、已审核、已发布、已下架、已废弃）
- 审核状态（审核中、审核通过、审核驳回）
- 销售数据（销售量、销售额）
- 产品创建时间
- 产品更新时间
- 操作管理区（操作按钮 详情按钮 促销按钮 包装按钮 ）

###### 2.3.2.3.3 产品操作管理（对应操作管理区的操作按钮）

**功能描述**: 产品的全生命周期管理，【操作按钮下拉显示：编辑、预览、提交审核、发布、下架、复制、删除、废弃】

**产品状态**: 草稿、待审核、审核驳回、已发布、已下架、已废弃

**具体状态说明**：
- ✅ **草稿**：产品创建中或编辑中，可执行大部分操作，未提交审核
- ✅ **待审核**：产品已提交审核，等待管理员处理，锁定编辑功能
- ✅ **审核驳回**：产品审核未通过，可查看驳回原因并修改后重新提交
- ✅ **已发布**：产品审核通过并发布到商城，商城可自行上架下架操作
- ✅ **已下架**：产品暂停销售，已购买用户权益不受影响，可重新上架
- ✅ **已废弃**：产品永久停售，仅保留历史记录和数据统计

**状态转换流程**：
```
草稿 ──→ 提交审核 ──→ 待审核 ──→ 审核通过 ──→ 已发布 ──→ 手动下架 ──→ 已下架
 ↑                     ↓                     ↓           ↓
 └──修改后重新编辑 ── 审核驳回                  ├───废弃──→ 已废弃
                                             ↑             ↑
                        已下架 ──重新发布──→ 已发布          │
                          ↓                                │
                          └──────────废弃──────────────────┘
```

**各状态操作权限矩阵**：

| 状态 | 编辑 | 预览 | 提交审核 | 发布 | 下架 | 复制 | 删除 | 废弃 |
|------|------|------|----------|------|------|------|------|------|
| 草稿 | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ | ✅ | ❌ |
| 待审核 | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| 审核驳回 | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ | ✅ | ❌ |
| 已发布 | 🔸 | ✅ | ❌ | ❌ | ✅ | ✅ | 🔸 | ✅ |
| 已下架 | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ | 🔸 | ✅ |
| 已废弃 | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |

**权限图例**：
- ✅ 允许操作
- ❌ 不允许操作  
- 🔸 有条件允许

**有条件操作详细规则**：

1. **已发布状态编辑**：
   - ✅ 可修改：产品标题、副标题、描述、封面图、宣传素材、营销文案
   - ❌ 不可修改：权益包组合、产品定价、核心配置参数
   - 📝 修改后立即生效，无需重新审核

2. **已发布状态删除**：
   - ✅ 允许删除：无任何销售记录的产品
   - ❌ 禁止删除：已有销售记录的产品（保护用户权益和数据完整性）
   - 🔄 替代方案：有销售记录的产品只能执行"废弃"操作

3. **已下架状态删除**：
   - ✅ 允许删除：无任何销售记录的产品
   - ❌ 禁止删除：已有销售记录的产品
   - 📊 判断依据：检查订单表中是否存在该产品的购买记录

**具体操作功能说明**：

- **产品编辑**: 支持产品内容的修改和调整，根据产品状态限制编辑范围
- **产品预览**: 实时预览产品在商城中的展示效果，支持移动端和PC端预览
- **提交审核**: 将完成的产品提交给管理员审核，提交后状态变为"待审核"
- **产品发布**: 管理员审核通过后，可将产品发布到商城开始销售
- **产品下架**: 暂停产品销售，但不影响已购买用户的权益使用
- **产品复制**: 基于现有产品创建新的产品副本，状态为"草稿"
- **产品删除**: 永久删除产品记录（仅限无销售记录的产品）
- **产品废弃**: 永久停售产品，保留历史数据用于统计分析

**批量操作支持**：
- ✅ 批量提交审核（仅草稿和审核驳回状态）
- ✅ 批量发布（仅已审核状态）
- ✅ 批量下架（仅已发布状态）
- ✅ 批量删除（仅无销售记录的草稿状态产品）
- ✅ 批量复制（所有状态均支持）

**操作安全机制**：
- 🔒 **二次确认**：删除、废弃等高风险操作需要二次确认
- 📝 **操作日志**：记录所有产品状态变更和操作历史
- 👥 **权限控制**：不同角色用户具有不同的操作权限
- ⚠️ **风险提示**：执行有风险的操作时显示明确的提示信息

**业务规则补充**：
- 产品一旦有销售记录，核心配置（权益包、定价）不可修改
- 已废弃产品的历史数据永久保留，用于业务分析和用户权益保障
- 产品状态变更会触发相关业务系统的同步更新（如商城、订单、用户权益等）
- 支持产品版本管理，重大修改时可创建新版本而非直接覆盖

###### 2.3.2.3.4 产品详情（对应操作管理区的详情按钮）
**功能描述**: 产品详情展示产品的详细信息
**具体需求**:
- 产品基础信息（名称、分类、描述、状态、价格、创建时间、更新时间）
- 产品权益包组合详情
- 产品营销包装详情
- 产品销售数据统计
- 产品审核记录
- 产品创建和更新历史

###### 2.3.2.3.5 促销策略设置（对应操作管理区的促销按钮）
**功能描述**: 配置产品的促销策略，如限时折扣、满减优惠等，
**具体需求**: 




##### 2.3.2.3.6 产品营销包装设计（对应操作管理区的包装按钮）
**功能描述**: 为营销产品设计完整的视觉包装和营销素材
**具体需求**:
- **产品基础信息**: 产品名称、副标题、核心卖点、产品描述
- **目标用户画像**: 详细定义产品的目标用户群体和使用场景
- **产品特性提取**: 自动提取和整理产品的核心特性和功能点
- **营销素材上传**: 支持产品封面图、宣传海报、介绍视频等素材上传（用于用户分享和推广员推广）

##### 2.3.2.3.7 产品筛选和搜索
**功能描述**: 管理员可根据多种条件筛选和搜索产品
**具体需求**:
- **分类筛选**: 管理员可根据产品分类筛选产品
- **状态筛选**: 管理员可根据产品状态筛选产品
- **审核状态筛选**: 管理员可根据审核状态筛选产品
- **名称搜索**: 管理员可根据产品名称搜索产品
- **价格区间筛选**: 管理员可根据产品价格区间筛选产品
- **创建日期筛选**: 管理员可根据产品创建日期筛选产品
- **销售数据筛选**: 管理员可根据销量、收入等销售数据筛选产品


##### 2.3.2.4 产品审核页功能
**功能描述**: 管理员可在产品审核页查看待审核的产品，对产品进行审核操作）
**具体需求**:
- 管理员可查看产品的详细信息，包括产品名称、分类、描述、权益包组合、定价、营销包装等
- 管理员可对产品进行审核操作，包括审核通过、审核驳回等
- 管理员可添加审核备注，记录审核意见和建议

**审核流程**
```
产品创建 → 提交审核 → 管理员审核 → 审核完成 → 发布商城
   ↓         ↓         ↓           ↓        ↓
草稿状态   待审核状态  审核中状态  已审核状态  已发布状态
   ↑         ↑         ↑
   ←─────────┴─────────┘
        审核驳回（可修改后重新提交）
        驳回状态
```

#### 2.3.3 产品组装示例

##### **AI学习成长卡系列**：
- **产品一：AI学习入门卡**
  - 编程入门包（课程权益，成本¥55）
  - 学习助手包（智能体权益，成本¥22）
  - 基础算力包（算力权益，成本¥13）
  - 学习工具包（插件权益，成本¥13）
  - **总成本**: ¥103，**定价**: ¥159/月

- **产品二：AI学习专业卡**
  - 大模型专业包（课程权益，成本¥70）
  - 学习助手包（智能体权益，成本¥22）
  - 高级算力包（算力权益，成本¥37）
  - 学习工具包（插件权益，成本¥13）
  - **总成本**: ¥142，**定价**: ¥299/月

##### **AI创业赚钱卡系列**：
- **产品三：AI创业入门卡**
  - 编程入门包（课程权益，成本¥55）
  - 内容创作包（智能体权益，成本¥37）
  - 基础算力包（算力权益，成本¥13）
  - 商业插件包（插件权益，成本¥25）
  - **总成本**: ¥130，**定价**: ¥259/月

- **产品四：AI创业专业卡**
  - 大模型专业包（课程权益，成本¥70）
  - 内容创作包（智能体权益，成本¥37）
  - 高级算力包（算力权益，成本¥37）
  - 商业插件包（插件权益，成本¥25）
  - **总成本**: ¥169，**定价**: ¥399/月

##### **AI专业开发卡系列**：
- **产品五：AI开发专家卡**
  - 大模型专业包（课程权益，成本¥70）
  - 智能体买断包（智能体权益，成本¥150）
  - 全模型包（算力权益，成本¥20）
  - 专业插件包（插件权益，成本¥30）
  - **总成本**: ¥270，**定价**: ¥599/月

#### 2.3.4 产品发布到商城

**功能描述**: 将审核通过的产品发布到商城产品列表，管理员可以在商城管理这些产品，比如上架、下架等操作
**具体需求**:
- **自动同步**: 产品发布后自动同步到商城系统
- **库存管理**: 虚拟产品的库存设置和管理
- **价格同步**: 产品定价策略自动同步到商城
- **营销素材同步**: 产品封面、描述、特性等营销素材同步
- **用户权益激活**: 用户购买后自动激活对应的权益包内容（课程权益、智能体权益、算力权益、插件权益）
- **订单关联**: 建立产品与订单、用户权益的完整关联关系（订单包含产品、用户权益、购买时间、有效期等信息）




### 2.4 架构设计图

```
营销体系三层组装架构（详细举例版）
│ 
├── 第一层：单体配额组装层
│   ├── 📚 课程业务配额
│   │   ├── AI基础课程配额 (人工智能概论，1年，¥20)
│   │   ├── 大模型专业课程配额 (模型微调实战，永久，¥50)
│   │   └── 智能体开发课程配额 (智能体商业应用，2年，¥35)
│   ├── 🤖 智能体业务配额
│   │   ├── 文案润色智能体配额 (使用权限，1年，¥12)
│   │   ├── PPT制作智能体配额 (使用权限，永久，¥25)
│   │   ├── 学习助手智能体配额 (使用权限，1年，¥10)
│   │   ├── 文案润色智能体买断配额 (源码买断，¥50)
│   │   └── PPT制作智能体买断配额 (源码买断，¥100)
│   ├── ⚡ AI算力业务配额
│   │   ├── GPT-3.5配额 (10万Token/月，¥8)
│   │   ├── GPT-4配额 (5万Token/月，¥25)
│   │   └── Claude配额 (8万Token/月，¥12)
│   └── 🔧 AI插件业务配额
│       ├── 文档处理插件配额 (1000次/月，¥5)
│       ├── 图片生成插件配额 (500次/月，¥15)
│       ├── 数据分析插件配额 (800次/月，¥10)
│       ├── 代码生成插件基础配额 (基础功能，¥8)
│       └── 代码生成插件高级配额 (高级功能，¥20)
│ 
├── 第二层：同业务权益包组装层
│   ├── 📚 课程权益包
│   │   ├── 大模型专业包 (AI基础+大模型专业，¥70成本)
│   │   └── 编程入门包 (AI基础+智能体开发，¥55成本)
│   ├── 🤖 智能体权益包
│   │   ├── 学习助手包 (学习助手+文案润色，¥22成本)
│   │   ├── 内容创作包 (文案润色+PPT制作，¥37成本)
│   │   └── 智能体买断包 (文案润色买断+PPT制作买断，¥150成本)
│   ├── ⚡ 算力权益包
│   │   ├── 基础算力包 (GPT-3.5+文心一言，¥13成本)
│   │   ├── 高级算力包 (GPT-4+Claude，¥37成本)
│   │   └── 全模型包 (基础模型+高级模型，¥20成本)
│   └──🔧 插件权益包
│       ├── 学习工具包 (文档处理+代码生成基础，¥13成本)
│       ├── 商业插件包 (数据分析+图片生成，¥25成本)
│       └── 专业插件包 (代码生成高级+数据分析，¥30成本)   
│ 
└── 第三层：跨业务产品组装层
    ├── 产品一、AI学习成长卡 (大模型专业包+学习助手包+基础算力包+学习工具包+基础推广包，¥118成本)
    ├── 产品二、AI创业赚钱卡 (编程入门包+内容创作包+高级算力包+商业插件包+高级推广包，¥154成本)
    ├── 产品三、AI专业开发卡 (大模型专业包+智能体买断包+全模型包+专业插件包+企业推广包，¥270成本)
    └── 产品四、AI企业解决方案 (全部权益包组合，企业级定制)
```

