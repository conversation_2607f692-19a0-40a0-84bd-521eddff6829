# AI生态平台用户端页面设计规划方案

**设计时间**: 2025年7月8日 22:30 (北京时间)  
**设计规范**: Material Design 3.0  
**技术栈**: Vue.js 3.4 + TypeScript + Element Plus  
**设计原则**: 国际化、美观大气、人机交互、用户体验、动态交互  

## 📋 设计概览

### 🎯 设计目标
- **用户体验**: 基于Material Design 3.0的现代化界面设计
- **国际化支持**: 多语言切换，支持中文、英文等主流语言
- **响应式设计**: 适配桌面端、平板端、移动端
- **交互体验**: 丰富的动画效果和微交互
- **无障碍访问**: 符合WCAG 2.1 AA标准

### 📊 技术要求
- **前端框架**: Vue.js 3.4 + Composition API
- **UI组件库**: Element Plus + Material Design 3.0定制
- **状态管理**: Pinia
- **路由管理**: Vue Router 4.0
- **国际化**: Vue I18n
- **动画库**: Framer Motion Vue / GSAP
- **图标库**: Material Design Icons + Element Plus Icons

## 🏗️ 页面架构规划

### 一级导航结构
```
AI生态平台用户端
├── 🏠 首页 (/)
├── 🤖 智能体市场 (/market) ✅ 已有功能
├── 🔌 插件市场 (/plugins) 🚧 重点规划
├── 🧠 模型API (/models) 🚧 重点规划
├── 🎓 赛斯学堂 (/academy) [预留]
├── 💎 购买会员 (/membership) ✅ 已有功能
├── 👤 用户中心 (/user) ✅ 已有功能
├── 💰 推广赚钱 (/promotion) ✅ 已有功能
├── 🛠️ 开发者中心 (/developer) [预留]
├── ❓ 帮助中心 (/help)
└── 💬 在线客服 (/support)
```

## 📱 详细页面规划

### 🏠 首页模块 (/)

#### 页面结构
```
首页 (Home)
├── 导航栏 (Header)
│   ├── Logo + 品牌名称
│   ├── 主导航菜单
│   ├── 语言切换
│   ├── 主题切换 (明/暗)
│   ├── 搜索框
│   ├── 通知中心
│   └── 用户菜单
├── 英雄区域 (Hero Section)
│   ├── 主标题 + 副标题
│   ├── CTA按钮组
│   ├── 产品演示视频
│   └── 平台统计数据
├── 功能特色 (Features)
│   ├── 智能体生态
│   ├── 插件市场
│   ├── 模型API
│   └── 学习中心
├── 热门智能体 (Popular Agents)
│   ├── 智能体卡片网格
│   ├── 分类筛选
│   └── 查看更多按钮
├── 用户评价 (Testimonials)
│   ├── 用户头像 + 评价
│   ├── 轮播展示
│   └── 星级评分
├── 新闻动态 (News & Updates)
│   ├── 平台公告
│   ├── 产品更新
│   └── 行业资讯
└── 页脚 (Footer)
    ├── 产品链接
    ├── 公司信息
    ├── 社交媒体
    └── 版权信息
```

#### 核心功能
- **响应式英雄区域**: 渐变背景 + 动态粒子效果
- **实时统计数据**: 智能体数量、用户数、API调用次数
- **智能推荐**: 基于用户行为的个性化内容推荐
- **多语言支持**: 中英文切换，RTL语言支持
- **暗黑模式**: 自动/手动主题切换

### 🤖 智能体市场模块 (/market)

#### 页面结构 (已有功能，需重新设计)
```
智能体市场 (Market)
├── 搜索和筛选区域
│   ├── 全局搜索框
│   ├── 分类筛选器
│   ├── 价格筛选器
│   ├── 评分筛选器
│   ├── 排序选项
│   └── 视图切换 (网格/列表)
├── 智能体展示区域
│   ├── 智能体卡片网格
│   ├── 智能体列表视图
│   ├── 无限滚动加载
│   └── 骨架屏加载
├── 智能体详情页 (/market/:id)
│   ├── 智能体信息
│   ├── 功能介绍
│   ├── 使用示例
│   ├── 评价评论
│   ├── 相关推荐
│   └── 购买/使用按钮
└── 智能体对话页 (/market/:id/chat)
    ├── 对话界面
    ├── 历史记录
    ├── 设置面板
    └── 分享功能
```

#### 设计要求
- **Material Design 3.0卡片**: 动态阴影、圆角、颜色系统
- **流畅动画**: 卡片悬停、加载动画、页面转场
- **智能筛选**: 实时搜索、标签云、智能推荐
- **社交功能**: 收藏、分享、评论、评分

### 🔌 插件市场模块 (/plugins) [预留功能]

#### 页面结构
```
插件市场 (Plugins)
├── 插件市场首页 (/plugins)
│   ├── 英雄区域
│   │   ├── 插件市场介绍
│   │   ├── 搜索框
│   │   └── 热门插件统计
│   ├── 插件分类导航
│   │   ├── 工具类插件
│   │   ├── 数据处理插件
│   │   ├── 集成类插件
│   │   ├── 自动化插件
│   │   └── 自定义插件
│   ├── 推荐插件区域
│   │   ├── 编辑推荐
│   │   ├── 最新上架
│   │   ├── 热门下载
│   │   └── 免费精选
│   └── 开发者专区
│       ├── 插件开发指南
│       ├── SDK下载
│       └── 开发者社区
├── 插件列表页 (/plugins/category/:category)
│   ├── 面包屑导航
│   ├── 筛选和排序工具栏
│   │   ├── 价格筛选 (免费/付费)
│   │   ├── 评分筛选
│   │   ├── 兼容性筛选
│   │   ├── 更新时间筛选
│   │   └── 排序选项
│   ├── 插件卡片网格
│   │   ├── 插件图标
│   │   ├── 插件名称
│   │   ├── 开发者信息
│   │   ├── 评分和评论数
│   │   ├── 价格标签
│   │   ├── 兼容性标识
│   │   └── 安装/购买按钮
│   └── 分页组件
├── 插件详情页 (/plugins/:id)
│   ├── 插件基本信息
│   │   ├── 插件图标和截图
│   │   ├── 插件名称和版本
│   │   ├── 开发者信息
│   │   ├── 评分和评论
│   │   ├── 下载/安装次数
│   │   └── 价格和购买按钮
│   ├── 插件详细介绍
│   │   ├── 功能描述
│   │   ├── 使用场景
│   │   ├── 安装要求
│   │   ├── 更新日志
│   │   └── 常见问题
│   ├── 使用示例
│   │   ├── 代码示例
│   │   ├── 配置说明
│   │   ├── API文档
│   │   └── 视频教程
│   ├── 用户评价区域
│   │   ├── 评分统计
│   │   ├── 评论列表
│   │   ├── 评论筛选
│   │   └── 写评论功能
│   └── 相关推荐
│       ├── 同类插件
│       ├── 开发者其他插件
│       └── 用户也喜欢
├── 我的插件 (/plugins/my)
│   ├── 已安装插件
│   │   ├── 插件列表
│   │   ├── 启用/禁用状态
│   │   ├── 版本信息
│   │   ├── 更新提醒
│   │   └── 卸载功能
│   ├── 已购买插件
│   │   ├── 购买记录
│   │   ├── 下载链接
│   │   ├── 许可证信息
│   │   └── 技术支持
│   └── 插件设置
│       ├── 全局设置
│       ├── 插件配置
│       ├── 权限管理
│       └── 数据备份
└── 插件开发中心 (/plugins/developer)
    ├── 开发者仪表板
    │   ├── 插件统计
    │   ├── 收益报告
    │   ├── 用户反馈
    │   └── 版本管理
    ├── 插件上传
    │   ├── 插件包上传
    │   ├── 信息填写
    │   ├── 审核状态
    │   └── 发布管理
    ├── 开发工具
    │   ├── SDK下载
    │   ├── 开发文档
    │   ├── 调试工具
    │   └── 测试环境
    └── 开发者社区
        ├── 技术论坛
        ├── 经验分享
        ├── 问题求助
        └── 官方公告
```

#### 核心功能
- **插件生态管理**: 插件分类、搜索、筛选、推荐
- **安装和管理**: 一键安装、版本管理、权限控制
- **开发者支持**: 完整的开发工具链和社区支持
- **商业化功能**: 付费插件、收益分成、数据统计

#### 设计要求
- **Material Design 3.0卡片**: 插件卡片设计
- **安装体验**: 流畅的安装和配置流程
- **开发者友好**: 完善的开发工具和文档
- **社区互动**: 评论、评分、分享功能

### 🧠 模型API模块 (/models) [预留功能]

#### 页面结构
```
模型API (Models)
├── 模型API首页 (/models)
│   ├── 英雄区域
│   │   ├── API服务介绍
│   │   ├── 实时API状态
│   │   └── 调用统计展示
│   ├── 模型分类导航
│   │   ├── 大语言模型 (LLM)
│   │   ├── 图像生成模型
│   │   ├── 语音识别模型
│   │   ├── 文本转语音模型
│   │   ├── 计算机视觉模型
│   │   └── 多模态模型
│   ├── 热门模型推荐
│   │   ├── 最受欢迎
│   │   ├── 最新发布
│   │   ├── 性价比之选
│   │   └── 免费试用
│   └── API服务特色
│       ├── 高可用性保障
│       ├── 弹性扩容
│       ├── 全球加速
│       └── 技术支持
├── 模型列表页 (/models/category/:category)
│   ├── 筛选工具栏
│   │   ├── 价格筛选
│   │   ├── 性能筛选
│   │   ├── 供应商筛选
│   │   ├── 支持语言筛选
│   │   └── 排序选项
│   ├── 模型卡片展示
│   │   ├── 模型名称和版本
│   │   ├── 供应商信息
│   │   ├── 性能指标
│   │   ├── 价格信息
│   │   ├── 支持功能
│   │   └── 试用/购买按钮
│   └── 对比功能
│       ├── 模型对比表
│       ├── 性能对比
│       └── 价格对比
├── 模型详情页 (/models/:id)
│   ├── 模型基本信息
│   │   ├── 模型介绍
│   │   ├── 技术规格
│   │   ├── 性能指标
│   │   ├── 支持语言
│   │   └── 更新历史
│   ├── 价格和配额
│   │   ├── 计费方式
│   │   ├── 价格阶梯
│   │   ├── 免费额度
│   │   └── 套餐推荐
│   ├── API文档
│   │   ├── 接口说明
│   │   ├── 参数详解
│   │   ├── 响应格式
│   │   ├── 错误码说明
│   │   └── SDK示例
│   ├── 在线测试
│   │   ├── API测试工具
│   │   ├── 参数配置
│   │   ├── 实时调用
│   │   └── 结果展示
│   └── 使用案例
│       ├── 应用场景
│       ├── 代码示例
│       ├── 最佳实践
│       └── 性能优化
├── API控制台 (/models/console)
│   ├── 概览仪表板
│   │   ├── 使用统计
│   │   ├── 费用统计
│   │   ├── 调用趋势
│   │   └── 错误监控
│   ├── API密钥管理
│   │   ├── 密钥列表
│   │   ├── 创建密钥
│   │   ├── 权限设置
│   │   └── 使用限制
│   ├── 调用记录
│   │   ├── 请求日志
│   │   ├── 响应详情
│   │   ├── 错误分析
│   │   └── 性能监控
│   ├── 配额管理
│   │   ├── 当前配额
│   │   ├── 使用情况
│   │   ├── 配额购买
│   │   └── 自动续费
│   └── 账单中心
│       ├── 费用明细
│       ├── 账单下载
│       ├── 支付记录
│       └── 发票管理
├── API文档中心 (/models/docs)
│   ├── 快速开始
│   │   ├── 注册和认证
│   │   ├── 第一次调用
│   │   ├── SDK安装
│   │   └── 常见问题
│   ├── API参考
│   │   ├── 接口列表
│   │   ├── 参数说明
│   │   ├── 响应格式
│   │   └── 错误处理
│   ├── SDK文档
│   │   ├── Python SDK
│   │   ├── JavaScript SDK
│   │   ├── Java SDK
│   │   └── 其他语言
│   ├── 代码示例
│   │   ├── 基础调用
│   │   ├── 高级功能
│   │   ├── 批量处理
│   │   └── 异步调用
│   └── 最佳实践
│       ├── 性能优化
│       ├── 错误处理
│       ├── 安全建议
│       └── 成本控制
└── 模型训练平台 (/models/training) [高级功能]
    ├── 训练项目管理
    │   ├── 项目列表
    │   ├── 创建项目
    │   ├── 数据集管理
    │   └── 模型版本
    ├── 训练配置
    │   ├── 模型选择
    │   ├── 参数设置
    │   ├── 资源配置
    │   └── 训练启动
    ├── 训练监控
    │   ├── 训练进度
    │   ├── 性能指标
    │   ├── 资源使用
    │   └── 日志查看
    └── 模型部署
        ├── 模型评估
        ├── 部署配置
        ├── 服务发布
        └── 性能监控
```

#### 核心功能
- **模型服务**: 多种AI模型的API服务
- **使用管理**: 配额管理、调用统计、费用控制
- **开发支持**: 完整的API文档和SDK
- **监控运维**: 实时监控、日志分析、性能优化

#### 设计要求
- **技术文档**: 清晰的API文档和代码示例
- **控制台设计**: 专业的开发者控制台界面
- **数据可视化**: 丰富的统计图表和监控面板
- **开发者体验**: 便捷的测试工具和调试功能

### 🎓 赛斯学堂模块 (/academy) [预留功能]

#### 页面结构
```
赛斯学堂 (Academy)
├── 课程分类导航
├── 课程列表展示
├── 学习路径规划
├── 视频教程播放
├── 在线考试系统
└── 学习进度跟踪
```

### 💎 购买会员模块 (/membership)

#### 页面结构 (已有功能，需重新设计)
```
购买会员 (Membership)
├── 会员套餐对比表
│   ├── 免费版特权
│   ├── VIP月卡特权
│   ├── VIP年卡特权
│   └── SVIP年卡特权
├── 特权详细介绍
│   ├── 配额权益
│   ├── 功能权益
│   └── 服务权益
├── 支付流程
│   ├── 套餐选择
│   ├── 支付方式
│   ├── 优惠券使用
│   └── 订单确认
└── 会员管理
    ├── 当前会员状态
    ├── 续费管理
    ├── 发票管理
    └── 退款申请
```

#### 设计要求
- **价格表设计**: Material Design 3.0卡片系统
- **对比功能**: 清晰的特权对比表格
- **支付体验**: 流畅的支付流程设计
- **状态展示**: 直观的会员状态指示器

### 👤 用户中心模块 (/user)

#### 页面结构 (已有功能，需重新设计)
```
用户中心 (UserCenter)
├── 侧边导航菜单
├── 账户概览 (/user/overview)
│   ├── 用户信息卡片
│   ├── 会员状态展示
│   ├── 使用统计图表
│   └── 快捷操作按钮
├── 个人资料 (/user/profile)
│   ├── 基本信息编辑
│   ├── 头像上传
│   ├── 联系方式管理
│   └── 偏好设置
├── 安全设置 (/user/security)
│   ├── 密码修改
│   ├── 两步验证
│   ├── 登录设备管理
│   └── 安全日志
├── 我的订单 (/user/orders)
│   ├── 订单列表
│   ├── 订单详情
│   ├── 发票管理
│   └── 退款记录
├── 我的应用 (/user/apps)
│   ├── 已购智能体
│   ├── 已购插件
│   ├── API使用情况
│   └── 收藏夹
└── 消费明细 (/user/billing)
    ├── 账单列表
    ├── 积分记录
    ├── 配额使用
    └── 消费统计
```

#### 设计要求
- **响应式侧边栏**: 移动端折叠菜单
- **数据可视化**: 使用统计图表展示
- **表单设计**: Material Design 3.0表单组件
- **状态管理**: 实时数据更新

### 💰 推广赚钱模块 (/promotion)

#### 页面结构 (已有功能，需重新设计)
```
推广赚钱 (Promotion)
├── 推广概览 (/promotion/overview)
│   ├── 收益统计面板
│   ├── 推广数据图表
│   ├── 排行榜展示
│   └── 任务中心
├── 佣金提现 (/promotion/withdraw)
│   ├── 提现申请表单
│   ├── 银行卡管理
│   ├── 提现规则说明
│   └── 手续费计算
├── 提现记录 (/promotion/records)
│   ├── 提现历史列表
│   ├── 状态跟踪
│   ├── 详情查看
│   └── 凭证下载
├── 实名认证 (/promotion/verification)
│   ├── 身份证上传
│   ├── 人脸识别
│   ├── 银行卡绑定
│   └── 认证状态
├── 推广链接 (/promotion/links)
│   ├── 链接生成器
│   ├── 二维码生成
│   ├── 分享工具
│   └── 数据统计
└── 我的团队 (/promotion/team)
    ├── 下级用户列表
    ├── 团队结构图
    ├── 收益明细
    └── 团队管理
```

#### 设计要求
- **数据面板**: 丰富的图表和统计数据
- **表单设计**: 清晰的提现和认证流程
- **分享功能**: 便捷的链接和二维码分享
- **团队管理**: 直观的层级关系展示

### 🔐 认证页面模块

#### 页面结构
```
认证页面组
├── 登录页面 (/login)
│   ├── 邮箱/手机登录
│   ├── 密码登录
│   ├── 短信验证登录
│   ├── 第三方登录
│   └── 记住登录状态
├── 注册页面 (/register)
│   ├── 邮箱注册
│   ├── 手机注册
│   ├── 邀请码注册
│   ├── 协议确认
│   └── 验证码验证
├── 找回密码 (/forgot-password)
│   ├── 邮箱重置
│   ├── 短信重置
│   ├── 安全问题
│   └── 新密码设置
└── 邮箱验证 (/verify-email)
    ├── 验证链接处理
    ├── 重发验证邮件
    └── 验证状态反馈
```

#### 设计要求
- **Material Design 3.0表单**: 现代化输入框设计
- **渐进式表单**: 分步骤引导用户完成
- **安全提示**: 清晰的安全信息展示
- **错误处理**: 友好的错误提示和处理

### 📱 消息通知模块

#### 页面结构
```
消息中心 (/messages)
├── 消息分类标签
├── 系统通知
├── 交易消息
├── 推广消息
├── 客服消息
└── 消息设置
```

### ❌ 错误页面模块

#### 页面结构
```
错误页面组
├── 404页面 (/404)
├── 500页面 (/500)
├── 403权限错误 (/403)
└── 网络错误页面 (/network-error)
```

### 🛠️ 辅助功能页面

#### 页面结构
```
辅助功能
├── 开发者中心 (/developer) [预留]
├── 帮助中心 (/help)
├── 在线客服 (/support)
├── 关于我们 (/about)
├── 隐私政策 (/privacy)
└── 服务条款 (/terms)
```

## 🎨 Material Design 3.0设计规范 (全新优化版)

### 🌈 现代化颜色系统
```scss
// 主色调 (Primary) - 深海科技蓝渐变系统
$primary-50: #EBF4FF;              // 最浅蓝，用于背景
$primary-100: #DBEAFE;             // 浅蓝，用于悬停状态
$primary-200: #BFDBFE;             // 中浅蓝，用于禁用状态
$primary-300: #93C5FD;             // 中蓝，用于边框
$primary-400: #60A5FA;             // 中深蓝，用于图标
$primary-500: #3B82F6;             // 标准蓝，主要品牌色
$primary-600: #2563EB;             // 深蓝，用于悬停
$primary-700: #1D4ED8;             // 更深蓝，用于激活
$primary-800: #1E40AF;             // 深海蓝，用于文字
$primary-900: #1E3A8A;             // 最深蓝，用于标题

// 次要色调 (Secondary) - 翡翠智能绿渐变系统
$secondary-50: #ECFDF5;            // 最浅绿，用于背景
$secondary-100: #D1FAE5;           // 浅绿，用于成功提示
$secondary-200: #A7F3D0;           // 中浅绿，用于图标背景
$secondary-300: #6EE7B7;           // 中绿，用于进度条
$secondary-400: #34D399;           // 中深绿，用于按钮
$secondary-500: #10B981;           // 标准绿，次要品牌色
$secondary-600: #059669;           // 深绿，用于悬停
$secondary-700: #047857;           // 更深绿，用于激活
$secondary-800: #065F46;           // 深翡翠，用于文字
$secondary-900: #064E3B;           // 最深绿，用于标题

// 第三色调 (Tertiary) - 极光创新紫渐变系统
$tertiary-50: #FAF5FF;             // 最浅紫，用于背景
$tertiary-100: #F3E8FF;            // 浅紫，用于特殊状态
$tertiary-200: #E9D5FF;            // 中浅紫，用于装饰
$tertiary-300: #D8B4FE;            // 中紫，用于图标
$tertiary-400: #C084FC;            // 中深紫，用于强调
$tertiary-500: #A855F7;            // 标准紫，创新色
$tertiary-600: #9333EA;            // 深紫，用于悬停
$tertiary-700: #7C3AED;            // 更深紫，用于激活
$tertiary-800: #6B21A8;            // 深极光，用于文字
$tertiary-900: #581C87;            // 最深紫，用于标题

// 中性色调 (Neutral) - 温暖专业灰渐变系统
$neutral-50: #FAFAFA;              // 纯白背景
$neutral-100: #F5F5F5;             // 浅灰背景
$neutral-200: #E5E5E5;             // 分割线
$neutral-300: #D4D4D4;             // 边框
$neutral-400: #A3A3A3;             // 占位符
$neutral-500: #737373;             // 次要文字
$neutral-600: #525252;             // 常规文字
$neutral-700: #404040;             // 重要文字
$neutral-800: #262626;             // 标题文字
$neutral-900: #171717;             // 主要文字
```

### 🎯 色彩设计理念升级
- **主色调**: 深海科技蓝渐变系统，从浅蓝到深海蓝，体现AI技术的深度和专业性
- **次要色调**: 翡翠智能绿渐变系统，象征AI的智能、生机和创新活力
- **第三色调**: 极光创新紫渐变系统，代表未来科技、创造力和突破性思维
- **中性色调**: 温暖专业灰渐变系统，提供舒适的阅读体验和专业的视觉感受

### 🔤 现代化字体系统
```scss
// 字体族定义 - 中英文优化组合
$font-family-display: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
$font-family-body: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
$font-family-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Consolas', monospace;
$font-family-brand: 'SF Pro Display', 'PingFang SC', 'Hiragino Sans GB', sans-serif;

// 显示级标题 (Display) - 用于英雄区域和重要标题
$display-2xl: 72px/80px $font-family-display;     // 超大标题，品牌展示
$display-xl: 60px/72px $font-family-display;      // 特大标题，页面主标题
$display-lg: 48px/56px $font-family-display;      // 大标题，模块标题
$display-md: 36px/44px $font-family-display;      // 中标题，卡片标题
$display-sm: 30px/36px $font-family-display;      // 小标题，组件标题
$display-xs: 24px/32px $font-family-display;      // 最小标题，列表标题

// 标题级别 (Heading) - 用于内容层级
$heading-h1: 32px/40px $font-family-display;      // H1标题
$heading-h2: 28px/36px $font-family-display;      // H2标题
$heading-h3: 24px/32px $font-family-display;      // H3标题
$heading-h4: 20px/28px $font-family-display;      // H4标题
$heading-h5: 18px/24px $font-family-display;      // H5标题
$heading-h6: 16px/24px $font-family-display;      // H6标题

// 正文字体 (Body) - 用于内容阅读
$body-xl: 20px/32px $font-family-body;            // 超大正文，重要说明
$body-lg: 18px/28px $font-family-body;            // 大正文，介绍文字
$body-md: 16px/24px $font-family-body;            // 标准正文，主要内容
$body-sm: 14px/20px $font-family-body;            // 小正文，辅助信息
$body-xs: 12px/16px $font-family-body;            // 最小正文，备注信息

// 标签字体 (Label) - 用于UI元素
$label-xl: 16px/20px $font-family-body;           // 超大标签，重要按钮
$label-lg: 14px/20px $font-family-body;           // 大标签，主要按钮
$label-md: 13px/16px $font-family-body;           // 标准标签，表单标签
$label-sm: 12px/16px $font-family-body;           // 小标签，次要按钮
$label-xs: 11px/12px $font-family-body;           // 最小标签，状态标签

// 等宽字体 (Monospace) - 用于代码和数据
$mono-lg: 16px/24px $font-family-mono;            // 大等宽，代码块
$mono-md: 14px/20px $font-family-mono;            // 标准等宽，行内代码
$mono-sm: 12px/16px $font-family-mono;            // 小等宽，数据展示
```

### 📝 字体权重系统
```scss
$font-weight-thin: 100;           // 极细，装饰性文字
$font-weight-light: 300;          // 细体，次要信息
$font-weight-normal: 400;         // 常规，正文内容
$font-weight-medium: 500;         // 中等，重要信息
$font-weight-semibold: 600;       // 半粗，小标题
$font-weight-bold: 700;           // 粗体，标题
$font-weight-extrabold: 800;      // 特粗，重要标题
$font-weight-black: 900;          // 最粗，品牌标题
```

### 🧩 精致组件规范

#### 卡片系统 (Card System)
```scss
// 基础卡片
.card-base {
  border-radius: 16px;                    // 更加圆润的圆角
  background: $neutral-50;
  border: 1px solid $neutral-200;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // 多层次阴影系统
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),       // 基础阴影
    0 1px 2px rgba(0, 0, 0, 0.1);        // 边缘阴影

  &:hover {
    transform: translateY(-2px);          // 悬浮效果
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.08),
      0 2px 4px rgba(0, 0, 0, 0.12);
  }
}

// 卡片变体
.card-elevated {
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-interactive {
  cursor: pointer;
  &:active {
    transform: translateY(0);
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      0 1px 1px rgba(0, 0, 0, 0.06);
  }
}
```

#### 按钮系统 (Button System)
```scss
// 主要按钮 (Primary)
.btn-primary {
  background: linear-gradient(135deg, $primary-500 0%, $primary-600 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font: $label-md;
  font-weight: $font-weight-medium;
  transition: all 0.2s ease;

  &:hover {
    background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba($primary-500, 0.3);
  }
}

// 次要按钮 (Secondary)
.btn-secondary {
  background: linear-gradient(135deg, $secondary-500 0%, $secondary-600 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font: $label-md;
  font-weight: $font-weight-medium;
}

// 轮廓按钮 (Outline)
.btn-outline {
  background: transparent;
  color: $primary-600;
  border: 2px solid $primary-300;
  border-radius: 12px;
  padding: 10px 22px;

  &:hover {
    background: $primary-50;
    border-color: $primary-500;
    color: $primary-700;
  }
}

// 文本按钮 (Text)
.btn-text {
  background: transparent;
  color: $primary-600;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;

  &:hover {
    background: $primary-50;
    color: $primary-700;
  }
}
```

#### 输入框系统 (Input System)
```scss
.input-field {
  position: relative;
  margin-bottom: 24px;

  .input-control {
    width: 100%;
    padding: 16px;
    border: 2px solid $neutral-300;
    border-radius: 12px;
    font: $body-md;
    background: $neutral-50;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary-500;
      background: white;
      box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
    }

    &.error {
      border-color: #EF4444;
      box-shadow: 0 0 0 3px rgba(#EF4444, 0.1);
    }
  }

  .input-label {
    position: absolute;
    top: 16px;
    left: 16px;
    font: $label-md;
    color: $neutral-500;
    transition: all 0.2s ease;
    pointer-events: none;

    &.floating {
      top: -8px;
      left: 12px;
      font-size: 12px;
      background: white;
      padding: 0 4px;
      color: $primary-600;
    }
  }
}
```

## 🌐 国际化设计

### 支持语言
- **中文 (简体)**: zh-CN (默认)
- **中文 (繁体)**: zh-TW
- **英文**: en-US
- **日文**: ja-JP (预留)
- **韩文**: ko-KR (预留)

### 国际化要求
- **文本翻译**: 所有界面文本支持多语言
- **日期格式**: 根据地区自动调整
- **数字格式**: 货币、百分比本地化
- **RTL支持**: 阿拉伯语、希伯来语支持 (预留)

### 🎨 间距系统 (Spacing System)
```scss
// 基于8px网格的间距系统
$spacing-0: 0;                    // 0px
$spacing-1: 4px;                  // 4px - 最小间距
$spacing-2: 8px;                  // 8px - 基础间距
$spacing-3: 12px;                 // 12px - 小间距
$spacing-4: 16px;                 // 16px - 标准间距
$spacing-5: 20px;                 // 20px - 中间距
$spacing-6: 24px;                 // 24px - 大间距
$spacing-8: 32px;                 // 32px - 特大间距
$spacing-10: 40px;                // 40px - 超大间距
$spacing-12: 48px;                // 48px - 巨大间距
$spacing-16: 64px;                // 64px - 页面级间距
$spacing-20: 80px;                // 80px - 区块级间距
$spacing-24: 96px;                // 96px - 模块级间距
```

### 🌟 视觉效果系统
```scss
// 阴影系统
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

// 渐变系统
$gradient-primary: linear-gradient(135deg, $primary-500 0%, $primary-600 100%);
$gradient-secondary: linear-gradient(135deg, $secondary-500 0%, $secondary-600 100%);
$gradient-tertiary: linear-gradient(135deg, $tertiary-500 0%, $tertiary-600 100%);
$gradient-rainbow: linear-gradient(135deg, $primary-500 0%, $secondary-500 50%, $tertiary-500 100%);
$gradient-surface: linear-gradient(135deg, $neutral-50 0%, $neutral-100 100%);

// 模糊效果
$blur-sm: blur(4px);
$blur-md: blur(8px);
$blur-lg: blur(16px);
$blur-xl: blur(24px);
```

## 📱 响应式设计升级

### 🔧 现代化断点系统
```scss
// 移动设备优先的断点设计
$breakpoint-xs: 0px;              // 超小屏幕 (手机竖屏)
$breakpoint-sm: 640px;            // 小屏幕 (手机横屏)
$breakpoint-md: 768px;            // 中等屏幕 (平板竖屏)
$breakpoint-lg: 1024px;           // 大屏幕 (平板横屏/小笔记本)
$breakpoint-xl: 1280px;           // 超大屏幕 (桌面显示器)
$breakpoint-2xl: 1536px;          // 2K屏幕 (大桌面显示器)

// 容器最大宽度
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1536px;
```

### 📐 布局适配策略
```scss
// 移动端 (0-639px)
@media (max-width: 639px) {
  .layout-mobile {
    padding: $spacing-4;
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }

  .navigation-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 64px;
    background: white;
    border-top: 1px solid $neutral-200;
    box-shadow: $shadow-lg;
  }
}

// 平板端 (640px-1023px)
@media (min-width: 640px) and (max-width: 1023px) {
  .layout-tablet {
    padding: $spacing-6;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-6;
  }

  .navigation-tablet {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    width: 280px;
    background: white;
    border-right: 1px solid $neutral-200;
    box-shadow: $shadow-md;
  }
}

// 桌面端 (1024px+)
@media (min-width: 1024px) {
  .layout-desktop {
    padding: $spacing-8;
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-8;
  }

  .navigation-desktop {
    position: sticky;
    top: 0;
    height: 80px;
    background: rgba(white, 0.8);
    backdrop-filter: $blur-md;
    border-bottom: 1px solid $neutral-200;
  }
}
```

## 🎭 动画和微交互系统

### 🎬 动画时长和缓动
```scss
// 动画时长系统
$duration-instant: 0ms;           // 即时响应
$duration-fast: 150ms;            // 快速反馈
$duration-normal: 250ms;          // 标准动画
$duration-slow: 350ms;            // 慢速动画
$duration-slower: 500ms;          // 复杂动画
$duration-slowest: 750ms;         // 特殊效果

// 缓动函数系统
$ease-linear: linear;
$ease-in: cubic-bezier(0.4, 0, 1, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
$ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
```

### ✨ 微交互动画
```scss
// 悬停效果
.hover-lift {
  transition: transform $duration-fast $ease-out;
  &:hover {
    transform: translateY(-2px);
  }
}

.hover-scale {
  transition: transform $duration-fast $ease-out;
  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: box-shadow $duration-normal $ease-out;
  &:hover {
    box-shadow: 0 0 20px rgba($primary-500, 0.3);
  }
}

// 点击反馈
.click-feedback {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(white, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width $duration-normal $ease-out, height $duration-normal $ease-out;
  }

  &:active::after {
    width: 200px;
    height: 200px;
  }
}

// 加载动画
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-8px); }
  70% { transform: translateY(-4px); }
  90% { transform: translateY(-2px); }
}

.loading-pulse {
  animation: pulse 2s infinite;
}

.loading-spin {
  animation: spin 1s linear infinite;
}

.loading-bounce {
  animation: bounce 1s infinite;
}
```

### 🌊 页面转场动画
```scss
// 淡入淡出
.fade-enter-active, .fade-leave-active {
  transition: opacity $duration-normal $ease-in-out;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

// 滑动效果
.slide-enter-active, .slide-leave-active {
  transition: transform $duration-normal $ease-in-out;
}
.slide-enter-from {
  transform: translateX(100%);
}
.slide-leave-to {
  transform: translateX(-100%);
}

// 缩放效果
.scale-enter-active, .scale-leave-active {
  transition: all $duration-normal $ease-in-out;
}
.scale-enter-from, .scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
```

### 🎯 性能优化原则
- **GPU加速**: 使用transform和opacity属性
- **避免重排**: 不使用width、height、top、left等属性
- **合理时长**: 快速反馈150ms，标准动画250ms，复杂动画500ms
- **减少动画**: 移动端减少复杂动画，提升性能
- **用户偏好**: 支持用户的减少动画偏好设置

## 📊 技术实现要求

### 前端技术栈
- **前端框架**: Vue.js 3.4 + Composition API + TypeScript
- **UI组件**: Element Plus + Material Design 3.0定制
- **状态管理**: Pinia (用户状态、主题状态、应用状态)
- **路由管理**: Vue Router 4.0 + 导航守卫
- **国际化**: Vue I18n (中英文支持)
- **动画库**: CSS3 + GSAP (性能优化)
- **样式系统**: SCSS + 四色调系统 + 响应式Mixins
- **构建工具**: Vite 5.0 + 热重载
- **响应式**: 移动优先设计 + 断点系统

### 🎨 样式系统文件结构
```
frontend/src/styles/
├── design-system/
│   ├── colors.scss              # 现代化颜色系统
│   ├── typography.scss          # 字体系统
│   ├── spacing.scss             # 间距系统
│   ├── shadows.scss             # 阴影系统
│   ├── animations.scss          # 动画系统
│   └── components.scss          # 组件样式
├── themes/
│   ├── light.scss               # 浅色主题
│   ├── dark.scss                # 深色主题
│   └── auto.scss                # 自动主题
├── utilities/
│   ├── mixins.scss              # 样式混合
│   ├── functions.scss           # 样式函数
│   └── helpers.scss             # 辅助类
├── components/
│   ├── buttons.scss             # 按钮组件
│   ├── cards.scss               # 卡片组件
│   ├── forms.scss               # 表单组件
│   └── navigation.scss          # 导航组件
└── main.scss                    # 主样式文件
```

### 🔧 CSS自定义属性系统
```scss
:root {
  // 颜色令牌
  --color-primary-50: #{$primary-50};
  --color-primary-500: #{$primary-500};
  --color-primary-900: #{$primary-900};

  // 字体令牌
  --font-family-display: #{$font-family-display};
  --font-size-display-lg: #{48px};
  --font-weight-medium: #{$font-weight-medium};

  // 间距令牌
  --spacing-4: #{$spacing-4};
  --spacing-8: #{$spacing-8};

  // 阴影令牌
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};

  // 动画令牌
  --duration-normal: #{$duration-normal};
  --ease-in-out: #{$ease-in-out};
}
```

### 开发规范
- **组件命名**: PascalCase (如: AgentCard.vue)
- **文件结构**: 按功能模块组织，最大300行限制
- **代码风格**: TypeScript严格模式 + ESLint + Prettier
- **测试要求**: 单元测试 + 集成测试 + E2E测试

## 📋 核心页面规划总结

### 🔍 主要功能模块 (9个核心模块)

1. **🏠 首页**: 英雄区域 + 功能特色 + 热门智能体 + 用户评价
2. **🤖 智能体市场**: 搜索筛选 + 卡片展示 + 详情页 + 对话界面 ✅ 已有功能
3. **🔌 插件市场**: 插件分类 + 搜索筛选 + 详情页 + 开发者中心 🚧 重点规划
4. **🧠 模型API**: 模型列表 + API文档 + 控制台 + 训练平台 🚧 重点规划
5. **🎓 赛斯学堂**: 课程列表 + 学习路径 + 视频教程 + 认证考试 [预留]
6. **💎 购买会员**: 套餐对比 + 特权介绍 + 支付流程 + 会员管理 ✅ 已有功能
7. **👤 用户中心**: 6个子模块 (概览、资料、安全、订单、应用、账单) ✅ 已有功能
8. **💰 推广赚钱**: 6个子模块 (概览、提现、记录、认证、链接、团队) ✅ 已有功能
9. **🔐 认证页面**: 登录、注册、找回密码、邮箱验证

### 🛠️ 辅助功能模块 (5个支持模块)

1. **📱 消息中心**: 系统通知 + 交易消息 + 推广消息 + 客服消息
2. **❌ 错误页面**: 404/500/403/网络错误页面
3. **🛠️ 开发者中心**: API文档 + SDK下载 + 开发指南 [预留]
4. **❓ 帮助中心**: 常见问题 + 使用教程 + 视频指南
5. **💬 在线客服**: 实时聊天 + 工单系统 + 知识库搜索

### 📊 页面统计

- **总页面数**: 50+ 个页面
- **主要功能页面**: 30+ 个
- **辅助功能页面**: 20+ 个
- **已实现功能**: 4个模块 (智能体市场、购买会员、用户中心、推广赚钱)
- **重点规划功能**: 2个模块 (插件市场、模型API)
- **预留功能**: 3个模块 (赛斯学堂、开发者中心、部分辅助功能)

## 🎨 设计系统总结

### ✨ 全新设计亮点

#### 🌈 颜色系统升级
- **渐变色系**: 从单一颜色升级为完整的50-900渐变色系
- **语义化命名**: 更加直观的颜色命名和使用场景
- **品牌一致性**: 深海蓝、翡翠绿、极光紫的科技感配色
- **无障碍支持**: 符合WCAG 2.1 AA对比度标准

#### 🔤 字体系统优化
- **中英文优化**: Inter + PingFang SC 的现代化字体组合
- **层级丰富**: 从Display到Label的完整字体层级
- **等宽字体**: JetBrains Mono 提升代码和数据展示
- **权重系统**: 9个字体权重满足各种设计需求

#### 🧩 组件系统精致化
- **圆角升级**: 从12px升级到16px，更加现代
- **阴影系统**: 多层次阴影提供更好的视觉深度
- **交互反馈**: 丰富的悬停、点击、加载状态
- **动画效果**: 流畅的微交互提升用户体验

#### 📱 响应式系统现代化
- **断点优化**: 6个断点覆盖所有设备尺寸
- **移动优先**: 从小屏幕开始的渐进式设计
- **容器系统**: 灵活的容器最大宽度设置
- **布局适配**: 针对不同设备的专门优化

#### ✨ 动画系统专业化
- **时长标准**: 科学的动画时长分级
- **缓动函数**: 丰富的缓动效果选择
- **微交互**: 悬停、点击、加载的精致动画
- **性能优化**: GPU加速和性能最佳实践

### 🚀 实施建议

1. **分阶段实施**: 先实施颜色和字体系统，再逐步应用组件样式
2. **组件库建设**: 基于新设计系统构建完整的Vue组件库
3. **设计令牌**: 使用CSS自定义属性实现设计令牌系统
4. **主题切换**: 支持浅色/深色主题的无缝切换
5. **文档完善**: 为设计系统编写详细的使用文档

### 📊 预期效果

- **视觉提升**: 更加现代、专业、科技感的视觉体验
- **用户体验**: 流畅的交互和清晰的信息层次
- **品牌形象**: 强化AI生态平台的专业品牌形象
- **开发效率**: 标准化的设计系统提升开发效率
- **维护性**: 模块化的样式结构便于维护和扩展

---

**设计完成时间**: 2025年7月9日 14:30 (北京时间)
**设计版本**: v2.0 全新优化版
**更新内容**: 全面升级颜色、字体、组件、动画系统
**下一步**: 等待确认后开始新设计系统的技术实现



# 🔍 未开发页面分析报告

## 📊 项目概览

**分析时间**: 2025-07-10  
**项目状态**: 用户端插件市场开发完成，需要梳理剩余未开发页面  
**路由总数**: 约60+个路由定义  
**已开发页面**: 约40+个页面组件  

## 🚨 未开发页面清单

### 1. 核心功能页面（高优先级）

#### 1.1 模型API模块 `/models`
- ❌ **ModelAPI.vue** - 模型API主页
- ❌ **ModelList.vue** - 模型列表页
- ❌ **ModelDetail.vue** - 模型详情页
- ❌ **ModelPlayground.vue** - 模型测试页
- ❌ **ModelPricing.vue** - 模型定价页
- ❌ **ModelDocs.vue** - 模型文档页

**影响**: 模型API是平台核心功能之一，缺失会影响用户体验

#### 1.2 赛斯学堂模块 `/academy`
- ❌ **Academy.vue** - 学堂主页
- ❌ **CourseList.vue** - 课程列表
- ❌ **CourseDetail.vue** - 课程详情
- ❌ **LearningPath.vue** - 学习路径
- ❌ **Certification.vue** - 认证中心
- ❌ **Instructor.vue** - 讲师中心

**影响**: 学习培训功能缺失，影响用户技能提升

#### 1.3 开发者中心模块 `/developer`
- ❌ **Developer.vue** - 开发者中心主页
- ❌ **DeveloperOverview.vue** - 开发者概览
- ❌ **DeveloperPlugins.vue** - 我的插件
- ❌ **CreatePlugin.vue** - 创建插件
- ❌ **EditPlugin.vue** - 编辑插件
- ❌ **PluginAnalytics.vue** - 插件分析
- ❌ **DeveloperDocs.vue** - 开发文档
- ❌ **DeveloperEarnings.vue** - 收益管理

**影响**: 开发者生态缺失，影响插件生态发展

### 2. 用户中心页面（中优先级）

#### 2.1 用户中心缺失页面
- ❌ **UserOverview.vue** - 用户概览页
- ❌ **UserProfile.vue** - 个人资料页
- ❌ **UserSecurity.vue** - 安全设置页
- ❌ **UserApplications.vue** - 我的应用页
- ❌ **UserBilling.vue** - 账单管理页

**影响**: 用户个人中心功能不完整

#### 2.2 会员系统页面
- ❌ **Membership.vue** - 会员主页
- ❌ **MembershipPlans.vue** - 会员套餐页
- ❌ **MembershipPayment.vue** - 会员支付页

**影响**: 会员购买流程不完整

### 3. 辅助功能页面（低优先级）

#### 3.1 帮助支持页面
- ❌ **Help.vue** - 帮助中心主页（存在HelpCenter.vue但路由指向Help.vue）

#### 3.2 智能体市场子页面
- ❌ **AgentList.vue** - 智能体列表页（路由定义但可能使用AgentMarket.vue）
- ❌ **AgentCategory.vue** - 分类浏览页（已存在）

## 📈 开发优先级建议

### 🔴 高优先级（立即开发）
1. **模型API模块** - 平台核心功能
2. **开发者中心** - 插件生态基础
3. **用户中心核心页面** - 用户体验基础

### 🟡 中优先级（近期开发）
1. **赛斯学堂模块** - 用户价值提升
2. **会员系统页面** - 商业化功能
3. **帮助支持页面** - 用户服务

### 🟢 低优先级（后期优化）
1. **智能体市场优化** - 功能完善
2. **其他辅助页面** - 体验优化

## 🛠️ 技术实施建议

### 1. 开发策略
- **复用现有组件**: 利用已开发的Market组件、用户中心组件等
- **统一设计风格**: 保持与现有页面一致的Material Design 3.0风格
- **API优先**: 确保后端API支持完备后再开发前端页面

### 2. 开发顺序
1. **第一阶段**: 模型API模块（6个页面）
2. **第二阶段**: 开发者中心（8个页面）
3. **第三阶段**: 用户中心完善（5个页面）
4. **第四阶段**: 会员系统（3个页面）
5. **第五阶段**: 赛斯学堂（6个页面）

### 3. 资源估算
- **总页面数**: 约28个主要页面
- **预估工作量**: 14-20个工作日
- **技术难度**: 中等（有现有组件和模式可复用）

## 📋 下一步行动计划

### 立即行动
1. **创建目录结构**: 为未开发模块创建目录
2. **API接口梳理**: 确认后端API支持情况
3. **组件库完善**: 补充通用组件

### 近期规划
1. **模型API模块开发**: 优先开发核心功能
2. **开发者中心开发**: 完善插件生态
3. **用户中心完善**: 提升用户体验

### 长期规划
1. **功能迭代优化**: 根据用户反馈持续改进
2. **新功能扩展**: 根据业务需求添加新模块
3. **性能优化**: 提升页面加载和交互性能

## 🎯 成功指标

### 开发完成度
- [ ] 模型API模块: 0/6 页面
- [ ] 开发者中心: 0/8 页面  
- [ ] 用户中心: 3/8 页面 (37.5%)
- [ ] 会员系统: 0/3 页面
- [ ] 赛斯学堂: 0/6 页面
- [ ] 帮助支持: 0/1 页面

### 整体进度
- **已完成**: 约40个页面 (67%)
- **待开发**: 约28个页面 (33%)
- **预期完成时间**: 3-4周

---

**总结**: 虽然插件市场开发已完成，但仍有约1/3的页面需要开发，主要集中在模型API、开发者中心、用户中心等核心功能模块。建议按优先级分阶段开发，确保平台功能完整性。


