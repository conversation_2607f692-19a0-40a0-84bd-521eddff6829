# 🎨 AI生态平台 Naive UI使用指南

## 📋 目录

1. [🚀 快速开始](#快速开始)
2. [🎨 主题系统](#主题系统)
3. [🧩 组件使用](#组件使用)
4. [🔧 工具与脚本](#工具与脚本)
5. [📱 响应式设计](#响应式设计)
6. [🎯 最佳实践](#最佳实践)
7. [🔍 调试与验证](#调试与验证)

---

## 🚀 快速开始

### 基本使用

```vue
<template>
  <!-- 基础组件使用 -->
  <n-space vertical :size="16">
    <n-button type="primary" @click="handleClick">
      主要按钮
    </n-button>
    
    <n-card title="卡片标题">
      <n-text>这是卡片内容</n-text>
    </n-card>
    
    <n-input 
      v-model:value="inputValue" 
      placeholder="请输入内容"
      clearable
    />
  </n-space>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMessage } from 'naive-ui'

const inputValue = ref('')
const message = useMessage()

const handleClick = () => {
  message.success('操作成功！')
}
</script>
```

### 自动导入配置

项目已配置自动导入，无需手动导入组件：

```typescript
// ✅ 自动导入 - 推荐
<n-button>按钮</n-button>

// ❌ 手动导入 - 不需要
import { NButton } from 'naive-ui'
```

---

## 🎨 主题系统

### 主题配置

项目使用分层主题系统，支持多租户定制：

```typescript
// 使用主题
import { useTheme } from '@/theme'

const { themeConfig, isDark, toggleTheme } = useTheme()
```

### 自定义主题变量

```scss
// 在组件中使用主题变量
.custom-component {
  background: var(--n-color-base);
  color: var(--n-text-color);
  border: 1px solid var(--n-border-color);
  border-radius: var(--n-border-radius);
}
```

### 动态主题切换

```vue
<template>
  <n-button @click="toggleTheme">
    <template #icon>
      <n-icon>
        <SunnyIcon v-if="isDark" />
        <MoonIcon v-else />
      </n-icon>
    </template>
    切换主题
  </n-button>
</template>

<script setup lang="ts">
import { useTheme } from '@/theme'
import { Sunny as SunnyIcon, Moon as MoonIcon } from '@vicons/ionicons5'

const { isDark, toggleTheme } = useTheme()
</script>
```

---

## 🧩 组件使用

### 基础组件

#### 按钮组件
```vue
<template>
  <!-- 不同类型的按钮 -->
  <n-space>
    <n-button type="primary">主要按钮</n-button>
    <n-button type="info">信息按钮</n-button>
    <n-button type="success">成功按钮</n-button>
    <n-button type="warning">警告按钮</n-button>
    <n-button type="error">错误按钮</n-button>
  </n-space>
  
  <!-- 不同尺寸 -->
  <n-space>
    <n-button size="small">小按钮</n-button>
    <n-button size="medium">中按钮</n-button>
    <n-button size="large">大按钮</n-button>
  </n-space>
  
  <!-- 带图标 -->
  <n-button type="primary">
    <template #icon>
      <n-icon><AddIcon /></n-icon>
    </template>
    添加
  </n-button>
</template>
```

#### 表单组件
```vue
<template>
  <n-form ref="formRef" :model="formData" :rules="rules">
    <n-form-item path="username" label="用户名">
      <n-input 
        v-model:value="formData.username" 
        placeholder="请输入用户名"
      />
    </n-form-item>
    
    <n-form-item path="email" label="邮箱">
      <n-input 
        v-model:value="formData.email" 
        placeholder="请输入邮箱"
        type="email"
      />
    </n-form-item>
    
    <n-form-item path="role" label="角色">
      <n-select 
        v-model:value="formData.role" 
        :options="roleOptions"
        placeholder="请选择角色"
      />
    </n-form-item>
    
    <n-form-item>
      <n-space>
        <n-button @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSubmit">提交</n-button>
      </n-space>
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

const formRef = ref<FormInst>()
const formData = reactive({
  username: '',
  email: '',
  role: null
})

const rules: FormRules = {
  username: {
    required: true,
    message: '请输入用户名',
    trigger: ['blur', 'input']
  },
  email: {
    required: true,
    message: '请输入邮箱',
    trigger: ['blur', 'input']
  }
}

const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '用户', value: 'user' }
]

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    console.log('表单数据:', formData)
  } catch (error) {
    console.log('验证失败')
  }
}

const handleReset = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    username: '',
    email: '',
    role: null
  })
}
</script>
```

#### 数据表格
```vue
<template>
  <n-data-table
    :columns="columns"
    :data="tableData"
    :pagination="pagination"
    :loading="loading"
    @update:checked-row-keys="handleCheck"
  />
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import type { DataTableColumns } from 'naive-ui'

const loading = ref(false)
const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: '李四', email: '<EMAIL>', role: 'user' }
])

const columns: DataTableColumns = [
  {
    type: 'selection'
  },
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '姓名',
    key: 'name'
  },
  {
    title: '邮箱',
    key: 'email'
  },
  {
    title: '角色',
    key: 'role',
    render(row) {
      return h(
        NTag,
        { type: row.role === 'admin' ? 'success' : 'info' },
        { default: () => row.role }
      )
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(
        NSpace,
        {},
        {
          default: () => [
            h(NButton, { size: 'small', onClick: () => handleEdit(row) }, { default: () => '编辑' }),
            h(NButton, { size: 'small', type: 'error', onClick: () => handleDelete(row) }, { default: () => '删除' })
          ]
        }
      )
    }
  }
]

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
  }
}

const handleCheck = (keys: Array<string | number>) => {
  console.log('选中的行:', keys)
}

const handleEdit = (row: any) => {
  console.log('编辑:', row)
}

const handleDelete = (row: any) => {
  console.log('删除:', row)
}
</script>
```

### 高级组件

#### 模态框
```vue
<template>
  <n-button @click="showModal = true">打开模态框</n-button>
  
  <n-modal v-model:show="showModal">
    <n-card
      style="width: 600px"
      title="模态框标题"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <n-button quaternary circle @click="showModal = false">
          <template #icon>
            <n-icon><CloseIcon /></n-icon>
          </template>
        </n-button>
      </template>
      
      <n-text>这是模态框内容</n-text>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleConfirm">确认</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Close as CloseIcon } from '@vicons/ionicons5'

const showModal = ref(false)

const handleConfirm = () => {
  console.log('确认操作')
  showModal.value = false
}
</script>
```

---

## 🔧 工具与脚本

### Composables

#### useToast - 消息提示
```typescript
import { useToast } from '@/composables/useToast'

const { success, error, warning, info, showNotification } = useToast()

// 简单消息
success('操作成功！')
error('操作失败！')
warning('警告信息')
info('提示信息')

// 复杂通知
showNotification({
  title: '系统通知',
  message: '这是一条重要通知',
  type: 'info',
  duration: 5000
})
```

#### useTheme - 主题管理
```typescript
import { useTheme } from '@/theme'

const { 
  themeConfig, 
  isDark, 
  toggleTheme, 
  setTheme,
  currentTenant 
} = useTheme()

// 切换主题
toggleTheme()

// 设置特定主题
setTheme('dark')
setTheme('light')
```

### 验证脚本

```bash
# 运行迁移验证
npm run migration:verify

# 或直接运行脚本
node scripts/migration-verification.js
```

### 开发工具

#### 组件迁移工具
```typescript
import { 
  transformProps, 
  transformEvents, 
  createMigrationLog 
} from '@/utils/component-migration'

// 转换属性
const naiveProps = transformProps(elementProps)

// 转换事件
const naiveEvents = transformEvents(elementEvents)

// 创建迁移日志
createMigrationLog('ComponentName', [
  '组件从Element Plus迁移到Naive UI',
  '属性映射完成',
  '事件处理优化'
])
```

---

## 📱 响应式设计

### 断点系统

```scss
// 使用内置断点
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
}

@media (min-width: 769px) {
  .desktop-only {
    display: block;
  }
}
```

### 响应式组件

```vue
<template>
  <n-grid :cols="gridCols" :x-gap="16" :y-gap="16">
    <n-grid-item v-for="item in items" :key="item.id">
      <n-card>{{ item.title }}</n-card>
    </n-grid-item>
  </n-grid>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useBreakpoint } from '@/composables/useBreakpoint'

const { isMobile, isTablet, isDesktop } = useBreakpoint()

const gridCols = computed(() => {
  if (isMobile.value) return 1
  if (isTablet.value) return 2
  return 3
})
</script>
```

---

## 🎯 最佳实践

### 1. 组件命名规范

```vue
<!-- ✅ 推荐：使用 n- 前缀 -->
<n-button>按钮</n-button>
<n-input>输入框</n-input>

<!-- ❌ 避免：混用不同前缀 -->
<el-button>按钮</el-button>
<n-input>输入框</n-input>
```

### 2. 类型安全

```typescript
// ✅ 使用 TypeScript 类型
import type { FormInst, DataTableColumns } from 'naive-ui'

const formRef = ref<FormInst>()
const columns: DataTableColumns = []
```

### 3. 性能优化

```vue
<template>
  <!-- ✅ 使用 v-show 而不是 v-if 对于频繁切换 -->
  <n-card v-show="visible">内容</n-card>
  
  <!-- ✅ 使用 key 优化列表渲染 -->
  <n-list>
    <n-list-item v-for="item in items" :key="item.id">
      {{ item.name }}
    </n-list-item>
  </n-list>
</template>
```

### 4. 主题一致性

```vue
<template>
  <!-- ✅ 使用主题变量 -->
  <div class="custom-component">内容</div>
</template>

<style scoped>
.custom-component {
  background: var(--n-color-base);
  color: var(--n-text-color);
  padding: var(--n-space-medium);
  border-radius: var(--n-border-radius);
}
</style>
```

---

## 🔍 调试与验证

### 开发模式调试

```typescript
// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('组件状态:', componentState)
  
  // 查看主题变量
  console.log('当前主题:', useTheme().themeConfig.value)
}
```

### 组件验证

```bash
# 检查组件迁移状态
npm run naive-ui:check

# 验证依赖完整性
npm run deps:check
```

### 常见问题排查

1. **组件不显示**：检查是否正确导入
2. **样式异常**：检查主题配置和CSS变量
3. **类型错误**：确保使用正确的TypeScript类型
4. **性能问题**：检查是否有不必要的重渲染

---

## 📚 参考资源

- [Naive UI 官方文档](https://www.naiveui.com/)
- [图标库文档](https://www.xicons.org/)
- [项目迁移文档](./AI生态平台Naive UI迁移进度总结.md)
- [迁移完成报告](../frontend/NAIVE_UI_MIGRATION_COMPLETE.md)

---

## 🎨 常用组件速查表

### 布局组件
```vue
<!-- 栅格布局 -->
<n-grid :cols="3" :x-gap="16" :y-gap="16">
  <n-grid-item><n-card>内容1</n-card></n-grid-item>
  <n-grid-item><n-card>内容2</n-card></n-grid-item>
  <n-grid-item><n-card>内容3</n-card></n-grid-item>
</n-grid>

<!-- 间距布局 -->
<n-space vertical :size="16">
  <n-button>按钮1</n-button>
  <n-button>按钮2</n-button>
</n-space>

<!-- 分割线 -->
<n-divider>分割线文字</n-divider>
```

### 反馈组件
```vue
<!-- 加载状态 -->
<n-spin :show="loading">
  <n-card>内容</n-card>
</n-spin>

<!-- 骨架屏 -->
<n-skeleton :width="200" :height="20" />

<!-- 进度条 -->
<n-progress type="line" :percentage="percentage" />

<!-- 结果页 -->
<n-result status="success" title="操作成功" description="您的操作已成功完成">
  <template #footer>
    <n-button>返回首页</n-button>
  </template>
</n-result>
```

### 导航组件
```vue
<!-- 面包屑 -->
<n-breadcrumb>
  <n-breadcrumb-item>首页</n-breadcrumb-item>
  <n-breadcrumb-item>用户管理</n-breadcrumb-item>
  <n-breadcrumb-item>用户列表</n-breadcrumb-item>
</n-breadcrumb>

<!-- 标签页 -->
<n-tabs v-model:value="activeTab" type="line">
  <n-tab-pane name="tab1" tab="标签1">内容1</n-tab-pane>
  <n-tab-pane name="tab2" tab="标签2">内容2</n-tab-pane>
</n-tabs>

<!-- 步骤条 -->
<n-steps :current="currentStep">
  <n-step title="步骤1" description="描述1" />
  <n-step title="步骤2" description="描述2" />
  <n-step title="步骤3" description="描述3" />
</n-steps>
```

---

## 🚀 AI生态平台专用组件

### Modern系列组件使用

```vue
<template>
  <!-- Modern按钮 -->
  <ModernButton
    type="primary"
    size="large"
    :loading="loading"
    @click="handleAction"
  >
    智能分析
  </ModernButton>

  <!-- Modern卡片 -->
  <ModernCard
    title="AI智能体"
    :hoverable="true"
    :actions="cardActions"
  >
    <template #cover>
      <img src="/agent-cover.jpg" alt="智能体封面" />
    </template>

    <n-text>这是一个强大的AI智能体，可以帮助您...</n-text>

    <template #extra>
      <n-tag type="success">推荐</n-tag>
    </template>
  </ModernCard>

  <!-- Modern表格 -->
  <ModernTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    :pagination="pagination"
    @refresh="handleRefresh"
    @export="handleExport"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ModernButton, ModernCard, ModernTable } from '@/components/modern'

const loading = ref(false)
const cardActions = [
  { label: '编辑', key: 'edit' },
  { label: '删除', key: 'delete' }
]

const handleAction = () => {
  loading.value = true
  // 执行操作
}
</script>
```

### 业务组件使用

```vue
<template>
  <!-- 动态表单 -->
  <DynamicForm
    :form-items="formItems"
    v-model="formData"
    @submit="handleSubmit"
    @reset="handleReset"
  />

  <!-- 确认对话框 -->
  <ConfirmDialog
    v-model:show="showConfirm"
    title="删除确认"
    content="确定要删除这个智能体吗？"
    @confirm="handleDelete"
  />

  <!-- 安全头像 -->
  <SafeAvatar
    :src="userAvatar"
    :name="userName"
    :size="48"
    :fallback-color="'primary'"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DynamicForm, ConfirmDialog, SafeAvatar } from '@/components/common'

const formItems = [
  {
    type: 'input',
    prop: 'name',
    label: '智能体名称',
    required: true,
    placeholder: '请输入智能体名称'
  },
  {
    type: 'textarea',
    prop: 'description',
    label: '描述',
    placeholder: '请输入智能体描述'
  },
  {
    type: 'select',
    prop: 'category',
    label: '分类',
    options: [
      { label: '文本处理', value: 'text' },
      { label: '图像处理', value: 'image' }
    ]
  }
]
</script>
```

---

## 🎯 页面开发模板

### 列表页面模板

```vue
<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-h2>智能体管理</n-h2>
      <n-space>
        <n-button type="primary" @click="handleCreate">
          <template #icon>
            <n-icon><AddIcon /></n-icon>
          </template>
          新建智能体
        </n-button>
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
          刷新
        </n-button>
      </n-space>
    </div>

    <!-- 搜索区域 -->
    <n-card class="search-card">
      <n-form inline :model="searchForm">
        <n-form-item label="名称">
          <n-input v-model:value="searchForm.name" placeholder="请输入名称" />
        </n-form-item>
        <n-form-item label="状态">
          <n-select v-model:value="searchForm.status" :options="statusOptions" />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleSearch">搜索</n-button>
            <n-button @click="handleReset">重置</n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 数据表格 -->
    <n-card>
      <ModernTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @refresh="loadData"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Add as AddIcon, Refresh as RefreshIcon } from '@vicons/ionicons5'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchForm = ref({
  name: '',
  status: null
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // API调用
    const response = await api.getAgents(searchForm.value)
    tableData.value = response.data
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 16px;
}
</style>
```

### 表单页面模板

```vue
<template>
  <div class="form-page">
    <n-card title="创建智能体">
      <DynamicForm
        :form-items="formItems"
        v-model="formData"
        :submit-text="isEdit ? '更新' : '创建'"
        @submit="handleSubmit"
        @reset="handleReset"
      >
        <template #actions="{ formData }">
          <n-space>
            <n-button @click="$router.back()">取消</n-button>
            <n-button @click="handleReset">重置</n-button>
            <n-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? '更新' : '创建' }}
            </n-button>
          </n-space>
        </template>
      </DynamicForm>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from '@/composables/useToast'

const route = useRoute()
const router = useRouter()
const { success, error } = useToast()

const isEdit = computed(() => !!route.params.id)
const submitting = ref(false)

const formData = ref({
  name: '',
  description: '',
  category: null,
  tags: [],
  isPublic: true
})

const handleSubmit = async (data: any) => {
  submitting.value = true
  try {
    if (isEdit.value) {
      await api.updateAgent(route.params.id, data)
      success('更新成功')
    } else {
      await api.createAgent(data)
      success('创建成功')
    }
    router.back()
  } catch (err) {
    error('操作失败')
  } finally {
    submitting.value = false
  }
}
</script>
```

---

**🎉 现在您可以高效地使用Naive UI开发AI生态平台的页面了！**

**💡 提示**：
- 优先使用Modern系列组件获得最佳视觉效果
- 利用DynamicForm快速构建表单页面
- 使用页面模板加速开发进度
- 遵循响应式设计原则确保移动端体验
