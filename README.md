# 🚀 AI生态变现平台 - 多租户SAAS智能化解决方案

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org/)
[![Vue Version](https://img.shields.io/badge/Vue-3.0+-green.svg)](https://vuejs.org/)
[![Docker](https://img.shields.io/badge/Docker-Compose-blue.svg)](https://docker.com/)
[![License](https://img.shields.io/badge/License-Commercial-red.svg)](LICENSE)

## 📋 项目概述

AI生态变现平台是一个基于Go微服务架构的多租户AI-SAAS变现服务平台，提供大模型API、智能体、AI工具箱等AI能力服务，支持代理商独立运营的完整商业生态。

### 🎯 核心定位
- **多租户SAAS平台**：支持代理商独立运营，"卖坑位"商业模式
- **AI生态服务**：集成大模型API中转、Agent智能体、AI工具箱、全场景智能营销、课堂学习、购物商城、全栈推广等核心AI能力
- **完整商业闭环**：从用户获取到变现的完整业务流程
- **微服务架构**：10容器架构部署（5容器业务微服务+3容器数据库+1容器消息队列+1容器nginx负载均衡），支持大规模用户访问和后期扩展
- **商业源码授权**： 支持私有化部署，按年授权、永久授权、源码买断授权三种授权
- **多端支持**：支持PC端、H5端、小程序端、公众号端

### 💼 商业盈利模式

```
商业模式一：公司直营（平台 → SAAS代理商 → 推广员 → 最终用户）

盈利点一：客户对象为AI使用用户
├── AI算力差价利润
├── 智能体直接销售利润
├── AI虚拟商品利润
└── AI工具箱销售利润

盈利点二：客户对象为AI创业者
└── SAAS代理商合作费用利润（提供AI创业平台）


商业模式二：源码销售
盈利点：
├── 按年授权利润
├── 永久授权利润
└── 源码买断利润

```

## 🏗️ 技术架构

### 技术栈
- **后端**: Go 1.21 + Gin + GORM + JWT
- **前端**: Vue3 + UniApp + Naive UI + Material Design 3.0
- **数据库**: PostgreSQL + Redis + MongoDB
- **容器**: Docker + Docker Compose + Nginx
- **架构**: （5容器业务微服务+3容器数据库+1容器消息队列+1容器nginx负载均衡） + 多租户隔离

### 微服务架构
```
用户访问 → 反向代理 → Nginx(8080) → 微服务层
                                    ├── frontend(8001)   # Vue3前端界面
                                    ├── ai-users(8002)   # 用户/营销/商城/课程
                                    ├── ai-agents(8003)  # 智能体/对话/知识库
                                    ├── ai-llms(8004)    # 大模型API中转
                                    ├── ai-tools(8005)   # AI工具箱/插件
                                    └── ai-admin(8006)   # 超级管理员/租户管理
数据层: PostgreSQL(5432) + Redis(6379) + MongoDB(27017)
```

### 容器职责分离
- **ai-users (8002)**: 租户推广员用户三角色、全场景智能营销、商城购物系统、课程学习体系、积分体系、全栈推广体系
- **ai-agents (8003)**: 智能体管理、多轮对话、知识库、工作流、第三方集成
- **ai-llms (8004)**: 大模型API中转、New-API融合、配额管理、算力分发
- **ai-tools (8005)**: AI插件工具、代码生成、文档处理、云提示词、可扩展化设计
- **ai-admin (8006)**: 超级管理员，全平台最高级管理，拥有全平台最高权限，租户管理、系统配置、数据分析、系统监控......
- **frontend (8001)**: 前端，用户端页面、推广员页面、SAAS租户页面、超级管理员页面....（支持PC端、H5端、小程序端、公众号端）

## 🎯 核心功能

### 多租户架构
- **租户隔离**: 基于6位租户ID的完整数据隔离
- **域名路由**: 支持代理商自定义域名
- **配置管理**: 租户级第三方服务配置 (支付/短信/存储/通知/智能体编程平台/大模型API渠道)
- **权限控制**: 基于租户的精细化权限管理

### 营销体系
- **三层组装**: 配额组装 → 权益包组装 → 产品组装
- **积分营销**: 积分获取、消费、兑换、等级体系
- **分销推广**: 全栈推广体系，支持2级推广员佣金分成、团队管理、多渠道推广、多场景分享

### 商城系统
- **产品类型**: 全场景智能营销三层组装的产品、实物类产品、课程类产品、服务类产品、帐号类产品
- **交易闭环**: 商品展示 → 购物车 → 订单 → 支付 → 交付
- **支付集成**: 微信支付、支付宝、易支付多渠道
- **售后服务**: 退款处理、客服支持、订单管理

### 课程体系
- **内容管理**: 视频课程、图文教程、实战项目
- **社区互动**: 学习讨论、问答交流、经验分享

### 智能体生态
- **对话管理**: 多轮对话、上下文记忆、会话管理
- **知识库**: 文档上传、向量检索、智能问答
- **工作流**: 自动化任务编排、流程设计
- **第三方集成**: Coze、Dify、n8n平台对接

### 大模型API
- **核心功能**: 基于开源项目new-api的大模型API中转功能
- **保留核心功能**: 大模型API渠道对接、渠道测试、模型调用、请求转发、响应处理、错误处理、缓存管理、配额管理、流量控制、安全防护......
- **其它功能**： 去除new-api的支付、用户、营销、推广、积分等功能，这些功能全部接入到AI生态变现平台

### AI工具箱
- **核心功能**: 高扩展性设计，插件化架构
- **声音克隆插件**（待规划）
- **云提示词插件**（待规划）
- **异步任务插件**（待规划）
- **视频处理插件**（待规划）


## 📚 文档导航

### 系统底盘功能需求
```
docs/
├── 1、用户体系功能需求文档.md        # 用户管理、认证、权限体系
├── 2、营销体系功能需求文档.md        # 三层组装营销、会员、积分
├── 3、商城体系功能需求文档.md        # 商品管理、订单、支付系统
├── 4、课程体系功能需求文档.md        # 在线学习、考试认证系统
├── 5、积分体系功能需求文档.md        # 积分获取、消费、等级体系
└── 6、推广体系功能需求文档.md        # 分销推广、佣金管理系统
```

### 核心业务功能需求
```
docs/
├── 2、智能体功能需求/              # 智能体、对话、知识库功能
├── 3、大模型API功能需求/           # 大模型API中转、配额管理
└── 4、AI工具箱功能需求/            # AI插件工具、代码生成功能
```

## 🚀 快速开始

### 环境要求
- Docker 20.0+
- Docker Compose 2.0+
- Go 1.21+ (开发环境)
- Node.js 16+ (前端开发)

### 部署步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd ceesai

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等参数

# 3. 启动服务
docker-compose up -d

# 4. 验证服务
curl -H "Host: admin.cees.cc" http://127.0.0.1:8080/health
```

### 开发命令
```bash
# 重建特定服务
make rebuild SERVICE=ai-users
make rebuild SERVICE=ai-agents
make rebuild SERVICE=frontend

# 查看服务日志
docker-compose logs -f ai-users
docker-compose logs -f --tail=100 ai-agents

# 健康检查
curl -H "Host: demo.cees.cc" http://127.0.0.1:8080/api/v1/users/health
```

## 🔧 开发规范

### 多租户开发原则
1. **数据隔离**: 所有业务表必须包含 `tenant_id` 字段
2. **配置管理**: 第三方配置存储在数据库，支持租户级配置
3. **域名路由**: 支持自定义域名和智能识别
4. **权限控制**: 基于租户的精细化权限验证

### API设计规范
```
/api/v1/auth/        → ai-users (认证相关)
/api/v1/users/       → ai-users (用户管理、营销、商城、课程)
/api/v1/agents/      → ai-agents (智能体服务)
/api/v1/tools/       → ai-tools (工具服务)
/api/v1/llms/        → ai-llms (大模型服务)
/api/v1/admin/       → ai-admin (超级管理员)
```

### 开发约束
- ❌ 禁止修改核心微服务架构和端口分配
- ❌ 禁止跨服务直接数据库访问
- ❌ 禁止硬编码敏感配置信息
- ❌ 禁止忽略多租户数据隔离
- ✅ 必须使用 `make rebuild` 命令重建容器
- ✅ 必须遵循JWT认证机制
- ✅ 必须支持租户级配置管理

## 🔒 安全机制

### 身份认证与授权
- **JWT认证**: 基于JSON Web Token的无状态认证机制
  - Token有效期：48小时，支持刷新机制
  - 签名算法：HS256，密钥定期轮换
  - 载荷信息：用户ID、租户ID、角色权限、过期时间
- **多因素认证**: 支持短信验证码、邮箱验证等二次验证
- **OAuth集成**: 支持微信第三方登录
- **权限控制**: 基于RBAC模型的细粒度权限管理

### 数据安全
- **数据加密**:
  - 敏感数据AES-256加密存储
  - 密码bcrypt哈希加密（成本因子12）
  - 数据库连接SSL/TLS加密
  - API通信HTTPS强制加密
- **数据隔离**:
  - 租户级数据完全隔离
  - 数据库查询自动添加租户过滤
  - 文件存储按租户ID分目录
- **数据脱敏**:
  - 日志中敏感信息自动脱敏
  - 开发环境数据脱敏处理
  - API响应敏感字段过滤

### 安全防护
- **API安全**:
  - 请求频率限制（100次/分钟/IP）
  - SQL注入防护
  - XSS攻击防护
  - CSRF令牌验证
- **访问控制**:
  - IP白名单机制
  - 异常登录检测和告警
  - 账户锁定策略（5次失败锁定30分钟）
- **安全审计**:
  - 完整的操作审计日志
  - 敏感操作实时告警
  - 安全事件追踪和分析

## 📊 监控运维

### 系统监控
- **服务监控**:
  - 容器健康状态检查（/health端点）
  - 服务响应时间监控
  - 服务可用性监控（99.9%目标）
  - 微服务间调用链路追踪
- **资源监控**:
  - CPU使用率监控（告警阈值80%）
  - 内存使用率监控（告警阈值85%）
  - 磁盘空间监控（告警阈值90%）
  - 网络流量监控
- **数据库监控**:
  - 数据库连接数监控
  - 慢查询检测（>1秒告警）
  - 数据库锁等待监控
  - 缓存命中率监控（Redis >90%）

### 日志管理
- **日志分类**:
  - 访问日志：记录所有API请求
  - 错误日志：记录系统异常和错误
  - 业务日志：记录关键业务操作
  - 安全日志：记录认证和权限相关操作
- **日志格式**:
  - 结构化JSON格式
  - 包含租户ID、用户ID、时间戳、操作类型
  - 自动脱敏敏感信息
- **日志存储**:
  - 本地文件存储（保留7天）
  - 集中式日志收集（保留30天）
  - 关键日志长期归档（保留1年）

### 告警机制
- **实时告警**:
  - 服务异常告警（邮件+短信）
  - 性能指标超阈值告警
  - 安全事件告警
  - 业务异常告警
- **告警渠道**:
  - 邮件通知
  - 短信通知
  - 监控大屏展示

## ⚡ 性能要求

### 响应时间指标
- **API响应时间**:
  - 认证接口：< 200ms
  - 查询接口：< 300ms
  - 更新接口：< 500ms
  - 复杂业务接口：< 1000ms
- **页面加载时间**:
  - 首页加载：< 2秒
  - 功能页面：< 3秒
  - 大数据页面：< 5秒

### 并发能力指标
- **用户并发**:
  - 支持10,000+在线用户
  - 支持1,000+并发登录
  - 支持5,000+并发API请求
- **数据库并发**:
  - 连接池最大200个连接
  - 支持1,000+并发查询
  - 事务处理能力500+TPS

### 系统可用性
- **可用性目标**:
  - 系统可用性：99.9%
  - 数据库可用性：99.95%
  - 缓存可用性：99.5%
- **故障恢复**:
  - 故障检测时间：< 1分钟
  - 故障恢复时间：< 5分钟
  - 数据备份恢复：< 30分钟
- **扩展能力**:
  - 支持水平扩展
  - 支持负载均衡
  - 支持弹性伸缩

## 📄 许可证

本项目为商业软件，版权所有。未经授权不得复制、分发或修改。

## 📞 联系我们

- **技术支持**: 中实创社（广州）科技有限公司
- **商务合作**: <EMAIL>
- **联系电话**: +86-18802641532
- **联系地址**: 广州市天河区灵山东路7号首层101-1A230室
- **官方网站**: www.cees.cc


**🎯 项目愿景**: 打造业界领先的AI生态平台SAAS系统，支持千万级用户、万级代理商和双层商业生态的完整运营！

