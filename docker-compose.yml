version: '3.8'

# 全局配置模板
x-build-config: &build-config
  BUILD_TIMESTAMP: ${BUILD_TIMESTAMP:-unknown}
  GIT_COMMIT: ${GIT_COMMIT:-unknown}
  VERSION: ${VERSION:-dev}
  BUILDKIT_INLINE_CACHE: 1

# 同步检测健康检查模板
x-sync-healthcheck: &sync-healthcheck
  interval: 30s
  timeout: 15s
  retries: 3
  start_period: 40s

# Go微服务通用配置
x-go-service: &go-service
  labels:
    - "build.timestamp=${BUILD_TIMESTAMP:-unknown}"
    - "build.commit=${GIT_COMMIT:-unknown}"
    - "build.version=${VERSION:-dev}"
    - "sync.enabled=true"
  restart: unless-stopped
  networks:
    - app-network
    - data-network

networks:
  app-network:
    driver: bridge
    name: ai-ecosystem-app
  data-network:
    driver: bridge
    name: ai-ecosystem-data

volumes:
  # 数据库存储
  postgres_data:
    name: ai-ecosystem-postgres-data
  redis_data:
    name: ai-ecosystem-redis-data
  bd_data:
    name: ai-ecosystem-bd-data
  
  # 系统日志
  nginx_logs:
    name: ai-ecosystem-nginx-logs
  app_logs:
    name: ai-ecosystem-app-logs
  
  # 业务数据
  user_uploads:
    name: ai-ecosystem-user-uploads
  agent_knowledge:
    name: ai-ecosystem-agent-knowledge
  agent_vectors:
    name: ai-ecosystem-agent-vectors
  tools_generated:
    name: ai-ecosystem-tools-generated
  llms_cache:
    name: ai-ecosystem-llms-cache
  ssl_certificates:
    name: ai-ecosystem-ssl-certificates
  app_configs:
    name: ai-ecosystem-app-configs

services:
  # 1. Nginx负载均衡器 - 端口8080
  nginx-lb:
    image: ai-ecosystem/nginx-lb:latest
    container_name: nginx-lb
    environment:
      - TZ=Asia/Shanghai
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
      - ssl_certificates:/etc/nginx/ssl
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # 2. 前端服务 - 端口8001
  frontend:
    image: ai-ecosystem/frontend:latest
    container_name: frontend
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:11:12Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - TZ=Asia/Shanghai
      - NODE_ENV=production
      - VITE_DOMAIN=localhost
      - VITE_PROTOCOL=http
      - VITE_API_BASE_URL=http://localhost/api/v1
    expose:
      - "8001"
    volumes:
      - app_logs:/var/log/app
    healthcheck:
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8001/"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 40s

  # 3. 用户服务 - 端口8002
  ai-users:
    image: ai-ecosystem/ai-users:latest
    container_name: ai-users
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:17:22Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - SYNC_CHECK_ENABLED=true
      - EXPECTED_GIT_COMMIT=${GIT_COMMIT:-unknown}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=0
      - BD_HOST=bd
      - BD_PORT=27017
      - BD_DATABASE=ai_ecosystem_bd
      - BD_USER=bd_admin
      - BD_PASSWORD=${BD_PASSWORD}
      - BD_AUTH_SOURCE=admin
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_EXPIRATION_HOURS=24
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - SERVER_PORT=8002
      - SERVER_MODE=production
      - ENVIRONMENT=production
      - DEFAULT_TENANT_ID=${DEFAULT_TENANT_ID}
    expose:
      - "8002"
    volumes:
      - ./.env:/app/.env:ro
      - app_logs:/app/logs
      - user_uploads:/app/uploads
      - app_configs:/app/configs:ro
    depends_on:
      - postgres
      - redis
      - bd
    healthcheck:
      <<: *sync-healthcheck
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8002/health"]

  # 4. 智能体服务 - 端口8003
  ai-agents:
    image: ai-ecosystem/ai-agents:latest
    container_name: ai-agents
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:17:22Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=1
      - BD_HOST=bd
      - BD_PORT=27017
      - BD_DATABASE=ai_ecosystem_bd
      - BD_USER=bd_admin
      - BD_PASSWORD=${BD_PASSWORD}
      - BD_AUTH_SOURCE=admin
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_EXPIRATION_HOURS=24
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - SERVER_PORT=8003
      - SERVER_MODE=production
      - ENVIRONMENT=production
      - STORAGE_TYPE=local
    expose:
      - "8003"
    volumes:
      - ./.env:/app/.env:ro
      - app_logs:/app/logs
      - agent_knowledge:/app/uploads
      - agent_vectors:/app/data
      - app_configs:/app/configs:ro
    depends_on:
      - postgres
      - redis
      - bd
    healthcheck:
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8003/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 40s

  # 5. 大模型服务 - 端口8004
  ai-llms:
    image: ai-ecosystem/ai-llms:latest
    container_name: ai-llms
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:17:22Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=3
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_EXPIRATION_HOURS=24
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - SERVER_PORT=8004
      - GO_ENV=production
      - GIN_MODE=release
      - NEW_API_MODE=enabled
      - AI_USERS_SERVICE_URL=http://ai-users:8002
      - AI_AGENTS_SERVICE_URL=http://ai-agents:8003
      - AI_TOOLS_SERVICE_URL=http://ai-tools:8005
    expose:
      - "8004"
    volumes:
      - ./.env:/app/.env:ro
      - app_logs:/app/logs
      - llms_cache:/app/cache
      - app_configs:/app/configs:ro
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8004/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 40s

  # 6. AI工具服务 - 端口8005
  ai-tools:
    image: ai-ecosystem/ai-tools:latest
    container_name: ai-tools
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:17:22Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=2
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_EXPIRATION_HOURS=24
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - SERVER_PORT=8005
      - SERVER_MODE=production
      - ENVIRONMENT=production
    expose:
      - "8005"
    volumes:
      - ./.env:/app/.env:ro
      - app_logs:/app/logs
      - tools_generated:/app/generated
      - user_uploads:/app/uploads
      - app_configs:/app/configs:ro
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8005/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 40s

  # 7. 超级管理员服务 - 端口8006
  ai-admin:
    image: ai-ecosystem/ai-admin:latest
    container_name: ai-admin
    <<: *go-service
    environment:
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP:-2025-07-25T08:17:22Z}
      - GIT_COMMIT=${GIT_COMMIT:-unknown}
      - VERSION=${VERSION:-v1.0.0}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=4
      - BD_HOST=bd
      - BD_PORT=27017
      - BD_DATABASE=ai_ecosystem_bd
      - BD_USER=bd_admin
      - BD_PASSWORD=${BD_PASSWORD}
      - BD_AUTH_SOURCE=admin
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_EXPIRATION_HOURS=24
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - SERVER_PORT=8006
      - SERVER_MODE=production
      - ENVIRONMENT=production
      - AI_USERS_SERVICE_URL=http://ai-users:8002
      - AI_AGENTS_SERVICE_URL=http://ai-agents:8003
      - AI_TOOLS_SERVICE_URL=http://ai-tools:8005
      - AI_LLMS_SERVICE_URL=http://ai-llms:8004
    expose:
      - "8006"
    volumes:
      - ./.env:/app/.env:ro
      - app_logs:/app/logs
      - app_configs:/app/configs:ro
    depends_on:
      - postgres
      - redis
      - bd
      - ai-users
      - ai-agents
      - ai-tools
      - ai-llms
    healthcheck:
      test: ["CMD", "wget", "--spider", "--quiet", "http://localhost:8006/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 40s

  # 8. PostgreSQL主数据库 - 端口5432
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    environment:
      - POSTGRES_DB=ai_ecosystem_db
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - TZ=Asia/Shanghai
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    expose:
      - "5432"
    networks:
      - data-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_user -d ai_ecosystem_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # 9. Redis缓存数据库 - 端口6379
  redis:
    image: redis:7-alpine
    container_name: redis
    environment:
      - TZ=Asia/Shanghai
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    volumes:
      - redis_data:/data
    expose:
      - "6379"
    networks:
      - data-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # 10. BD数据库 (MongoDB) - 端口27017
  bd:
    image: mongo:7-jammy
    container_name: bd
    environment:
      - MONGO_INITDB_ROOT_USERNAME=bd_admin
      - MONGO_INITDB_ROOT_PASSWORD=${BD_PASSWORD}
      - MONGO_INITDB_DATABASE=ai_ecosystem_bd
      - TZ=Asia/Shanghai
    command: >
      mongod
      --auth
      --bind_ip_all
      --port 27017
      --logpath /var/log/mongodb/mongod.log
      --logappend
      --storageEngine wiredTiger
      --wiredTigerCacheSizeGB 0.5
    volumes:
      - bd_data:/data/db
      - ./scripts/init-bd.js:/docker-entrypoint-initdb.d/init-bd.js:ro
    expose:
      - "27017"
    networks:
      - data-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')", "--quiet"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
